import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  selectProjectList: apiUrl.defaultUrl + "/scoreProject/selectProjectList",
  save: apiUrl.defaultUrl + "/scoreProject/save",
  deleteById: apiUrl.defaultUrl + "/scoreProject/deleteById",
  getPanoramaOverviewVo:
    apiUrl.defaultUrl + "/moreProject/getPanoramaOverviewVo",
  getBaseInfoChart: apiUrl.defaultUrl + "/moreProject/getBaseInfoChart",
  getPanoramaChart: apiUrl.defaultUrl + "/moreProject/getPanoramaChart",
  getScoreProjectOverview:
    apiUrl.defaultUrl + "/moreProject/getScoreProjectOverview",
};

/**
 * 工程信息-分页查询
 * @param data
 * "areaCode": "string", // 行政区划
 * "projectName": "string", // 工程名称
 * "projectScale": 0,
 * "projectType": "string" // 工程类型，1-水库，2-水闸，3-堤防
 */
export function selectProjectList(data, params) {
  return $http.postParams(api.selectProjectList, data, params);
}

/**
 * 工程信息-保存
 * @param data
 * "addScore": 0,
  "address": "string",
  "appKey": "string",
  "areaCode": "string",
  "constructionUnit": "string",
  "contractName": "string",
  "floodLimit": "string",
  "id": 0,
  "infoScore": 0,
  "innerFlag": 0,
  "lgtd": "string",
  "lttd": "string",
  "manageScore": 0,
  "manageUnit": "string",
  "picId": "string",
  "projectName": "string",
  "projectScale": 0,
  "projectStatus": 0,
  "projectType": 0,
  "safetyScore": 0,
  "score": 0,
  "statusScore": 0,
  "storageCapacity": "string",
  "workingScore": 0
 */
export function save(data) {
  return $http.post(api.save, data);
}

/**
 * 工程信息-分页查询
 * @param data
 * "id": 0
 */
export function deleteById(data) {
  return $http.post(api.deleteById, data);
}
/*
工程信息-分页查询
*/
export function getPanoramaOverviewVo(data) {
  return $http.post(api.getPanoramaOverviewVo, data);
}
/*
工程信息 -  多工程工程自评总览图表
*/
export function getBaseInfoChart(data) {
  return $http.post(api.getBaseInfoChart, data);
}
/*
工程信息 -  多工程全景图总览
*/
export function getPanoramaChart(data) {
  return $http.post(api.getPanoramaChart, data);
}
/*
工程信息 -  多工程工程自评总览--省市区用户
*/
export function getScoreProjectOverview(data) {
  return $http.post(api.getScoreProjectOverview, data);
}
