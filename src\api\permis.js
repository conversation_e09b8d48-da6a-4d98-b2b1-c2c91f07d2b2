import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  tree: apiUrl.defaultUrl + '/permis/tree',
  list: apiUrl.defaultUrl + '/permis/list',
  add: apiUrl.defaultUrl + '/permis/add',
  update: apiUrl.defaultUrl + '/permis/update',
  delete: apiUrl.defaultUrl + '/permis/deleteMenu'
}

/**
 * 权限树状结构接口
 * @param data
 * username string
 * password string
 */
export function getPremisTree () {
  return $http.post(api.tree, { enabled: 1 })
}

/**
 * 权限列表接口
 */
export function getPermisList () {
  return $http.post(api.list, undefined)
}

/**
 * 新增权限接口
 */
export function addPermis (data) {
  return $http.post(api.add, data)
}

/**
 * 修改权限接口
 */
export function updatePermis (data) {
  return $http.post(api.update, data)
}

/**
 * 删除权限接口
 */
export function deletePermis (data) {
  return $http.post(api.delete, data)
}
