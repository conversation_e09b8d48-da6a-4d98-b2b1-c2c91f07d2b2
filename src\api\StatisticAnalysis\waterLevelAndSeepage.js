/* 统计分析-库上水位-大坝渗压报表统计 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  exportStageAndOsmoticReport: apiUrl.defaultUrl + '/res-dam-osmotic/exportStageAndOsmoticReport',
  getDamSection: apiUrl.defaultUrl + '/res-dam-osmotic/getDamSection',
  getStageAndOsmoticReportChart: apiUrl.defaultUrl + '/res-dam-osmotic/getStageAndOsmoticReportChart'
}

/**
 * 水位渗压报表统计-获取大坝断面
 */
export function getDamSection(params) {
  return $http.get(api.getDamSection, params)
}

// 导出水位渗压报表
export function exportStageAndOsmoticReport(params) {
  return $http.getDownLoad(api.exportStageAndOsmoticReport, params, undefined, 'blob')
}

/**
 * 水位渗压报表统计图
 */
export function getStageAndOsmoticReportChart(params) {
  return $http.get(api.getStageAndOsmoticReportChart, params)
}
