import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getBaseInfoList: apiUrl.defaultUrl + '/projectData/getBaseInfoList',
  saveBaseInfo: apiUrl.defaultUrl + '/projectData/saveBaseInfo',
  getSwtzList: apiUrl.defaultUrl + '/projectData/getSwtzList',
  saveSwtz: apiUrl.defaultUrl + '/projectData/saveSwtzInfo',
  getDbList: apiUrl.defaultUrl + '/projectData/getDbList',
  saveSz: apiUrl.defaultUrl + '/projectData/saveDb',
  deleteSz: apiUrl.defaultUrl + '/projectData/deleteDb',
  getGlqkList: apiUrl.defaultUrl + '/projectData/getGlqkList',
  saveGlqk: apiUrl.defaultUrl + '/projectData/saveGlqk',
  getGcxyList: apiUrl.defaultUrl + '/projectData/getGcxyList',
  saveGcxy: apiUrl.defaultUrl + '/projectData/saveGcxy',
  getZcdjList: apiUrl.defaultUrl + '/projectData/getZcdjList',
  saveZcdj: apiUrl.defaultUrl + '/projectData/saveZcdj',
  getZrrList: apiUrl.defaultUrl + '/projectData/getZrrList',
  saveZrr: apiUrl.defaultUrl + '/projectData/saveZrr',
  deleteZrr: apiUrl.defaultUrl + '/projectData/deleteZrr',
  getAqjdList: apiUrl.defaultUrl + '/projectData/getAqjdList',
  saveAqjd: apiUrl.defaultUrl + '/projectData/saveAqjd',
  getFayaList: apiUrl.defaultUrl + '/moreProject/getFayaList',
  saveFaya: apiUrl.defaultUrl + '/projectData/saveFaya',
  deleteFaya: apiUrl.defaultUrl + '/projectData/deleteFaya',
  getSbtzList: apiUrl.defaultUrl + '/moreProject/getSbtzList',
  saveSbtz: apiUrl.defaultUrl + '/projectData/saveSbtz',
  deleteSbtz: apiUrl.defaultUrl + '/projectData/deleteSbtz',
  exportSbtz: apiUrl.defaultUrl + '/moreProject/getSbtzListExport',
  getDaglList: apiUrl.defaultUrl + '/projectData/getDaglList',
  saveDagl: apiUrl.defaultUrl + '/projectData/saveDagl',
  deleteDagl: apiUrl.defaultUrl + '/projectData/deleteDagl',
  initAqscgl: apiUrl.defaultUrl + '/projectData/initAqscgl',
  getAqscglList: apiUrl.defaultUrl + '/projectData/getAqscglList',
  getSourceInfo: apiUrl.defaultUrl + '/projectData/getSourceInfo',
  addFolder: apiUrl.defaultUrl + '/projectData/addFolder',
  aqscglUploadFile: apiUrl.defaultUrl + '/projectData/aqscglUploadFile',
  deleteAqscgl: apiUrl.defaultUrl + '/projectData/deleteAqscgl',
  updateSourceName: apiUrl.defaultUrl + '/projectData/updateSourceName'
}

/**
 * 基本信息列表接口
 * @param data
 */
export function getBaseInfoList(data) {
  return $http.post(api.getBaseInfoList, data)
}

/**
 * 基本信息编辑接口
 */
export function saveBaseInfo(data) {
  return $http.post(api.saveBaseInfo, data)
}

/**
 * 水文特征列表接口
 */
export function getSwtzList(data) {
  return $http.post(api.getSwtzList, data)
}

/**
 * 水文特征编辑接口
 */
export function saveSwtz(data) {
  return $http.post(api.saveSwtz, data)
}

/**
 * 水闸列表接口
 */
export function getSzList(data) {
  return $http.post(api.getDbList, data)
}

/**
 * 水闸编辑接口
 */
export function saveSz(data) {
  return $http.post(api.saveSz, data)
}

/**
 * 水闸删除接口
 */
export function deleteSz(data) {
  return $http.post(api.deleteSz, data)
}

/**
 * 管理情况列表接口
 */
export function getGlqkList(data) {
  return $http.post(api.getGlqkList, data)
}

/**
 * 管理情况编辑接口
 */
export function saveGlqk(data) {
  return $http.post(api.saveGlqk, data)
}

/**
 * 工程效益列表接口
 */
export function getGcxyList(data) {
  return $http.post(api.getGcxyList, data)
}

/**
 * 工程效益编辑接口
 */
export function saveGcxy(data) {
  return $http.post(api.saveGcxy, data)
}

/**
 * 注册登记列表接口
 */
export function getZcdjList(data) {
  return $http.post(api.getZcdjList, data)
}

/**
 * 注册登记编辑接口
 */
export function saveZcdj(data) {
  return $http.post(api.saveZcdj, data)
}

/**
 * 责任人列表接口
 */
export function getZrrList(data) {
  return $http.post(api.getZrrList, data)
}

/**
 * 责任人编辑接口
 */
export function saveZrr(data) {
  return $http.post(api.saveZrr, data)
}

/**
 * 责任人删除接口
 */
export function deleteZrr(data) {
  return $http.post(api.deleteZrr, data)
}

/**
 * 安全鉴定列表接口
 */
export function getAqjdList(data) {
  return $http.post(api.getAqjdList, data)
}

/**
 * 安全鉴定修改接口
 */
export function saveAqjd(data) {
  return $http.post(api.saveAqjd, data)
}

/**
 * 预案管理分页列表接口
 */
export function getFayaList(data) {
  return $http.post(api.getFayaList, data)
}

/**
 * 预案编辑接口
 */
export function saveFaya(data) {
  return $http.post(api.saveFaya, data)
}

/**
 * 预案删除接口
 */
export function deleteFaya(data) {
  return $http.post(api.deleteFaya, data)
}

/**
 * 设备台账列表接口
 */
export function getSbtzList(data) {
  return $http.post(api.getSbtzList, data)
}

/**
 * 设备台账修改接口
 */
export function saveSbtz(data) {
  return $http.post(api.saveSbtz, data)
}

/**
 * 设备台账删除接口
 */
export function deleteSbtz(data) {
  return $http.post(api.deleteSbtz, data)
}

/**
 * 设备台账导出接口
 */
export function exportSbtz(data, contentType, responseType) {
  return $http.postDownLoad(api.exportSbtz, data, contentType, responseType, false, true)
}

/**
 * 档案管理列表接口
 */
export function getDaglList(data) {
  return $http.post(api.getDaglList, data)
}

/**
 * 档案管理修改接口
 */
export function saveDagl(data) {
  return $http.post(api.saveDagl, data)
}

/**
 * 档案管理删除接口
 */
export function deleteDagl(data) {
  return $http.post(api.deleteDagl, data)
}

/**
 * 安全生产管理列表接口
 * 初始化，获取全部数据
 */
export function initAqscgl() {
  return $http.get(api.initAqscgl)
}

/**
 * 安全生产管理列表接口
 * 根据id查询文件夹下所有资源
 */
export function getAqscglList(data) {
  return $http.post(api.getAqscglList, data)
}

/**
 * 安全生产管理资源信息接口
 * 根据id获取当前资源信息
 */
export function getSourceInfo(data) {
  return $http.post(api.getSourceInfo, data)
}

/**
 * 安全生产管理-新建文件夹接口
 */
export function addFolder(data) {
  return $http.post(api.addFolder, data)
}

/**
 * 安全生产管理-文件上传接口
 */
export function aqscglUploadFile(data) {
  return $http.postUpLoadFile(api.aqscglUploadFile, data)
}

/**
 * 安全生产管理-资源删除接口
 * 根据id删除
 */
export function deleteAqscgl(data) {
  return $http.post(api.deleteAqscgl, data)
}

/**
 * 安全生产管理-修改资源名称接口
 * 根据id修改
 */
export function updateSourceName(data) {
  return $http.post(api.updateSourceName, data)
}
