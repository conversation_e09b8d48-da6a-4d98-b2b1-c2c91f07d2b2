/* 基础管理-基础信息（全部工程） */

import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  // 基本信息列表
  getAllBaseInfoList: apiUrl.defaultUrl + "/moreProject/getAllBaseInfoList",
  // 基本信息列表-多工程基本信息总览
  getBasicInfoOverview: apiUrl.defaultUrl + "/moreProject/getBasicInfoOverview",
};
/**
 * 基础信息-基本信息列表
 */
export function getAllBaseInfoList(data) {
  return $http.post(api.getAllBaseInfoList, data);
}

/**
 * 基础信息-多工程基本信息总览
 */
export function getBasicInfoOverview(data) {
  return $http.post(api.getBasicInfoOverview,data);
}
