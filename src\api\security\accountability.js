/* 安全管理-责任制 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  list: apiUrl.defaultUrl + '/tability/findPage',
  detailById: apiUrl.defaultUrl + '/tability/findTabById',
  save: apiUrl.defaultUrl + '/tability/editTabInfo',
  charts: apiUrl.defaultUrl + '/tability/charts',
  overviewList: apiUrl.defaultUrl + '/tability/overviewList'
}

/**
 * 责任制-详情
 * id
 */
export function detailById (params) {
  return $http.get(api.detailById, params)
}

/**
 * 责任制列表查询
 * "pageSize": "number",
 * "pageNum": "number"
 */
export function list (data) {
  return $http.post(api.list, data)
}

/**
 * 责任制-保存
 */
export function save (data) {
  return $http.post(api.save, data)
}

/**
 * 责任制-图表统计
 */
export function charts (data) {
  return $http.post(api.charts, data)
}

/**
 * 责任制-省市账号-按区划统计总览列表
 */
export function overviewList (data) {
  return $http.post(api.overviewList, data)
}
