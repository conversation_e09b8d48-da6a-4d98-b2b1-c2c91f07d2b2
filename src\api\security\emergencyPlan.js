/* 安全管理-应急预案 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  list: apiUrl.defaultUrl + '/czgz/plan/findFaYaPlanList',
  detailById: apiUrl.defaultUrl + '/czgz/plan/findCzgsFayaPage',
  save: apiUrl.defaultUrl + '/czgz/plan/addCzgsFaya',
  edit: apiUrl.defaultUrl + '/czgz/plan/editCzgsFaya',
  deleteById: apiUrl.defaultUrl + '/czgz/plan/deleteczgsFayaById',
  charts: apiUrl.defaultUrl + '/czgz/plan/charts',
  overviewList: apiUrl.defaultUrl + '/czgz/plan/overviewList',
  exportList: apiUrl.defaultUrl + '/czgz/plan/findCzgsFayaPageExport'
}

/**
 * 应急预案-详情
 * id
 */
export function detailById(params) {
  return $http.get(api.detailById, params)
}

/**
 * 应急预案
 * "pageSize": "number",
 * "pageNum": "number"
 * "appKey": "FLX"
 */
export function list(params) {
  return $http.get(api.list, params)
}

/**
 * 应急预案-保存
 */
export function save(data) {
  return $http.post(api.save, data)
}

/**
 * 应急预案-图表统计
 */
export function charts(data) {
  return $http.post(api.charts, data)
}

/**
 * 应急预案-省市账号-按区划统计总览列表
 */
export function overviewList(data) {
  return $http.post(api.overviewList, data)
}

/**
 * 应急预案-编辑
 */
export function edit(data) {
  return $http.post(api.edit, data)
}

/**
 * 应急预案-删除
 */
export function deleteById(data) {
  return $http.post(api.deleteById, data)
}

/**
 * 应急预案-导出
 */
export function exportList(data, contentType, responseType) {
  return $http.getDownLoad(api.exportList, data, contentType, responseType)
}
