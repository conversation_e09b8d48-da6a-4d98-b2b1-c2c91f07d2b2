<template>
  <h3 :class="['basic-msg-title', isSlot ? '' : 'basic-msg-title-between']">
    <span class="basic-msg-name">{{ title }}</span>
    <slot></slot>
    <span class="basic-msg-time">{{ date }}</span>
  </h3>
</template>

<script>
export default {
  name: 'BasicMsgTitle',
  props: {
    title: {
      type: String,
      default: ''
    },
    date: {
      type: String,
      default: ''
    },
    isSlot: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-msg-title {
  height: 54px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #E2EBF8;
  position: relative;
  margin-bottom: 20px;

  &.basic-msg-title-between {
    justify-content: space-between;
  }

  @mixin contentStyle {
    content: '';
    display: block;
    position: absolute;
    height: 3px;
    width: 20px;
    background-color: #0A4EAD;
    bottom: -2px;
  }

  &::before {
    @include contentStyle;
    left: 20px;
  }

  &::after {
    @include contentStyle;
    right: 20px;
  }

  .basic-msg-name {
    width: 306px;
    height: 54px;
    line-height: 54px;
    font-size: 22px;
    padding-left: 20px;
    color: #0A4EAD;
    font-weight: 700;
    background: url('@/assets/basic/titleBg.png') no-repeat;
    background-size: 100% 100%;
  }

  .basic-msg-time {
    font-size: 18px;
    color: #222F40;
    padding-right: 20px;
  }
}
</style>
