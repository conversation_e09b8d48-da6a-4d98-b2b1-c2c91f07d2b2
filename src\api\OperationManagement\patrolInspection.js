/* 运行管护-巡检巡查 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  curMothCounting:
    apiUrl.defaultUrl + "/operaDfPatrolInspection/curMothCounting",
  list: apiUrl.defaultUrl + "/operaDfPatrolInspection/list",
  deleteById: apiUrl.defaultUrl + "/operaDfPatrolInspection/deleteById",
  save: apiUrl.defaultUrl + "/operaDfPatrolInspection/save",
  importExcel: apiUrl.defaultUrl + "/operaDfPatrolInspection/importExcel",
  exportExcel: apiUrl.defaultUrl + "/operaDfPatrolInspection/exportExcel",
  // 路线
  routeLineCounting: apiUrl.defaultUrl + "/operaDfInspectionRoute/routeLineCounting",
  queryRouteByProjectId: apiUrl.defaultUrl + "/operaDfInspectionRoute/queryRouteByProjectId",
  // 打卡
  personCounting: apiUrl.defaultUrl + "/operaDfClockInRecords/personCounting",
  statisticsList: apiUrl.defaultUrl + "/operaDfClockInRecords/statisticsList",
  clockExportExcel: apiUrl.defaultUrl + "/operaDfClockInRecords/exportExcel",
};

/**
 * 巡检巡查 - 本月巡查统计
 * projectId
 */
export function curMothCounting(data) {
  return $http.post(api.curMothCounting, data);
}

/**
 * 巡检巡查-列表查询
 */
export function list(data) {
  return $http.post(api.list + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data);
}

/**
 * 巡检巡查-删除
 */
export function deleteById(data) {
  return $http.get(api.deleteById, data);
}

/**
 * 巡检巡查-保存
 */
export function save(data) {
  return $http.post(api.save, data);
}

/**
 * 巡检巡查-保存
 */
export function importExcel(data) {
  return $http.postUpLoadFile(api.importExcel, data)
}

/**
 * 巡检巡查-导出
 */
export function exportExcel(data) {
  return $http.postDownLoad(api.exportExcel, data, '', 'arraybuffer')
}

/**
 * 巡检路线-线路统计
 */
export function routeLineCounting(data) {
  return $http.post(api.routeLineCounting, data);
}
/**
 * 巡检路线-根据工程id查询巡查路线列表
 */
export function queryRouteByProjectId(data) {
  return $http.post(api.queryRouteByProjectId, data);
}
/**
 * 打卡统计-人数统计
 */
export function personCounting(data) {
  return $http.post(api.personCounting, data);
}
/**
 * 打卡统计-分页查询
 */
export function statisticsList(data) {
  return $http.post(api.statisticsList, data);
}
/**
 * 打卡统计-导出
 */
export function clockExportExcel(data) {
  return $http.postDownLoad(api.clockExportExcel, data, '', 'arraybuffer')
}
