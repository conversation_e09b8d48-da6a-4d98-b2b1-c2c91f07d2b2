import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  add: apiUrl.defaultUrl + '/supplies/add',
  deleteById: apiUrl.defaultUrl + '/supplies/deleteById',
  importExcel: apiUrl.defaultUrl + '/supplies/importExcel',
  materialexportExcel: apiUrl.defaultUrl + '/supplies/materialexportExcel',
  pagelist: apiUrl.defaultUrl + '/supplies/pagelist',
  update: apiUrl.defaultUrl + '/supplies/update'
}

/**
 * 安全管理-物资仓库接口
 * @param data
 */
// 新增物资信息
export function add (data) {
  return $http.post(api.add, data)
}
// 删除物资信息
export function deleteById (data) {
  return $http.post(api.deleteById, data)
}
// 导入物资信息
export function importExcel (data) {
  return $http.postUpLoadFile(api.importExcel, data)
}
// 查询物资仓库信息
export function pagelist (data) {
  return $http.post(api.pagelist + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}
// 编辑物资信息
export function update (data) {
  return $http.post(api.update, data)
}
// 导出物资信息
export function materialexportExcel (data,contentType, responseType) {
  return $http.postDownLoad(api.materialexportExcel, data,  contentType, responseType)
}
