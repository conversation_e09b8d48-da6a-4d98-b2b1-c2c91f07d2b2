/* 安全管理-水闸管理情况 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  getGlqkList: apiUrl.defaultUrl + '/projectData/getGlqkList',
  saveGlqk: apiUrl.defaultUrl + '/projectData/saveGlqk',
  getGlqkPage: apiUrl.defaultUrl + '/projectData/getGlqkPage'
}

/**
 * 管理情况列表
 */
export function getGlqkList (params) {
  return $http.post(api.getGlqkList, params)
}

/**
 * 管理情况编辑
 */
export function saveGlqk (params) {
  return $http.post(api.saveGlqk, params)
}

/**
 * 管理情况——项目列表
*/
export function getGlqkPage (data,params) {
  return $http.post(api.getGlqkPage + `?pageNum=${params.pageNum}&pageSize=${params.pageSize}`, data)
}
