/* 运行管护-白蚁防治 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  list: apiUrl.defaultUrl + '/control/list',
  details: apiUrl.defaultUrl + '/control/findControlDetails',
  save: apiUrl.defaultUrl + '/control/addControlDetail',
  checkList: apiUrl.defaultUrl + '/control/findControlRecord',
  detailCheck: apiUrl.defaultUrl + '/control/ControlRecordViews',
  deleteCheckById: apiUrl.defaultUrl + '/control/delRecordDateils',
  addCheck: apiUrl.defaultUrl + '/control/addTermiteRecord',
  editCheck: apiUrl.defaultUrl + '/control/editRecordDateils',
  getCharts: apiUrl.defaultUrl + '/control/charts',
  overviewList: apiUrl.defaultUrl + '/control/overviewList',
  getTermiteDeviceListUrl: apiUrl.defaultUrl + '/termite/device/findPageList'
}

/**
 * 白蚁防治-列表记录查询
 * "pageNum": number,
 * "pageSize": number
 */
export function list(data) {
  return $http.post(api.list, data)
}

/**
 * 白蚁防治-查看详情
 * "id": number,
 */
export function details(params) {
  return $http.get(api.details, params)
}

/**
 * 白蚁防治-记录保存
 *  "id"ap
 * "termitePlan"
 * "tracePic"
 *  "termitePlanFiles"
 */
export function save(data) {
  return $http.post(api.save, data)
}

/**
 * 白蚁防治-检查记录查询
 * "pageNum": number,
 * "pageSize": number
 */
export function checkList(params) {
  return $http.get(api.checkList, params)
}

/**
 * 白蚁防治-检查记录查看详情
 * "id": number,
 */
export function detailCheck(params) {
  return $http.get(api.detailCheck, params)
}

/**
 * 白蚁防治-检查记录删除
 * id
 */
export function deleteCheckById(params) {
  return $http.get(api.deleteCheckById, params)
}

/**
 * 白蚁防治-检查记录新增
 * id
 */
export function addCheck(data) {
  return $http.post(api.addCheck, data)
}

/**
 * 白蚁防治-检查记录编辑
 * id
 */
export function editCheck(data) {
  return $http.post(api.editCheck, data)
}
/**
 * 白蚁防治-图表统计
 *
 */
export function getCharts(data) {
  return $http.post(api.getCharts, data)
}
/**
 * 白蚁防治-省市级总览
 *
 */
export function overviewList(data) {
  return $http.post(api.overviewList, data)
}

/**
 * 白蚁防治-白蚁设备列表
 *
 */
export function getTermiteDeviceList(data) {
  return $http.post(api.getTermiteDeviceListUrl, data)
}
