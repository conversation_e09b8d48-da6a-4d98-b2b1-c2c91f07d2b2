import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getWarning: apiUrl.defaultUrl + '/xpqz/mnt/getWarning',
  getWarningDetail: apiUrl.defaultUrl + '/xpqz/mnt/getWarningDetail',
  getWaterLevelChart: apiUrl.defaultUrl + '/xpqz/mnt/getWaterLevelChart',
  getBasinRainfall: apiUrl.defaultUrl + '/xpqz/mnt/getBasinRainfall',
  getGateStatus: apiUrl.defaultUrl + '/xpqz/mnt/getGateStatus',
  getRealTimeRain: apiUrl.defaultUrl + '/xpqz/mnt/getRealTimeRain',
  getPpHistory: apiUrl.defaultUrl + '/xpqz/mnt/getPpHistory',
  getPpHistoryChart: apiUrl.defaultUrl + '/xpqz/mnt/getPpHistoryChart',
  getRealTimeWaterLevel: apiUrl.defaultUrl + '/xpqz/mnt/getRealTimeWaterLevel',
  getRzHistory: apiUrl.defaultUrl + '/xpqz/mnt/getRzHistory',
  getRzHistoryChart: apiUrl.defaultUrl + '/xpqz/mnt/getRzHistoryChart',
  getDamList: apiUrl.defaultUrl + '/xpqz/mnt/getDamList',
  getSafetyMonitoringList:
    apiUrl.defaultUrl + '/xpqz/mnt/getSafetyMonitoringList',
  getDamSafetyClassify: apiUrl.defaultUrl + '/xpqz/dsc/getDamSafetyClassify',
  getSafetyMonitoringChartData:
    apiUrl.defaultUrl + '/xpqz/mnt/getSafetyMonitoringChartData',
  safetyMonitoringExport:
    apiUrl.defaultUrl + '/xpqz/mnt/safetyMonitoringExport',
  gateMonitor: apiUrl.defaultUrl + '/xpqz/mnt/gateMonitor',
  gateMonitorHistory: apiUrl.defaultUrl + '/xpqz/mnt/gateMonitorHistory',
  monitorHistoryExport: apiUrl.defaultUrl + '/xpqz/mnt/monitorHistoryExport',
  getPointList: apiUrl.defaultUrl + '/xpqz/mnt/getPointList',
  getHotSpot: apiUrl.defaultUrl + '/xpqz/mnt/getHotSpot'
}

/**
 * 总体态势-预警接口
 */
export function getWarning () {
  return $http.get(api.getWarning)
}

/**
 * 总体态势-预警隐患时长接口
 */
export function getWarningDetail (params) {
  return $http.postParams(api.getWarningDetail, undefined, params)
}

/**
 * 总体态势-水位图接口
 */
export function getWaterLevelChart (params) {
  return $http.postParams(api.getWaterLevelChart, undefined, params)
}

/**
 * 总体态势-流域降雨量接口
 */
export function getBasinRainfall (params) {
  return $http.postParams(api.getBasinRainfall, undefined, params)
}

/**
 * 总体态势-闸门状态接口
 */
export function getGateStatus (params) {
  return $http.get(api.getGateStatus, params)
}

/**
 * 水雨情-雨量实况接口
 */
export function getRealTimeRain (data) {
  return $http.post(api.getRealTimeRain, data)
}

/**
 * 水雨情-雨量历史查询接口
 */
export function getPpHistory (data, params) {
  return $http.postParams(api.getPpHistory, data, params)
}

/**
 * 水雨情-雨量历史图表查询接口
 */
export function getPpHistoryChart (data) {
  return $http.postParams(api.getPpHistoryChart, data, undefined)
}

/**
 * 水雨情-水位实况接口
 */
export function getRealTimeWaterLevel (params) {
  return $http.get(api.getRealTimeWaterLevel, params)
}

/**
 * 水雨情-水位历史查询接口
 */
export function getRzHistory (data, params) {
  return $http.postParams(api.getRzHistory, data, params)
}

/**
 * 水雨情-水位历史图表查询接口
 */
export function getRzHistoryChart (data) {
  return $http.postParams(api.getRzHistoryChart, data, undefined)
}

/**
 * 安全监测——获取监测设备接口
 */
export function getDamList () {
  return $http.get(api.getDamList)
}

/**
 * 安全监测-列表数据(分页)接口
 */
export function getSafetyMonitoringList (data, params) {
  return $http.postParams(api.getSafetyMonitoringList, data, params)
}

/**
 * 安全监测-获取大坝类型分类统计超过预警数量接口
 */
export function getDamSafetyClassify () {
  return $http.get(api.getDamSafetyClassify)
}

/**
 * 安全监测-图表数据接口
 */
export function getSafetyMonitoringChartData (data) {
  return $http.post(api.getSafetyMonitoringChartData, data)
}

/**
 * 安全监测-导出接口
 */
export function safetyMonitoringExport (params, responseType) {
  return $http.postDownLoad(
    api.safetyMonitoringExport,
    params,
    '',
    responseType
  )
}

/**
 * 闸门监视-图表数据接口
 */
export function gateMonitor () {
  return $http.get(api.gateMonitor)
}

/**
 * 闸门监视-历史数据查询接口
 */
export function gateMonitorHistory (data, params) {
  return $http.postParams(api.gateMonitorHistory, data, params)
}

/**
 * 闸门监视-导出接口
 */
export function monitorHistoryExport (params, responseType) {
  return $http.postDownLoad(api.monitorHistoryExport, params, '', responseType)
}

// 安全监测-测点下拉列表

export function getPointList (params) {
  return $http.get(api.getPointList, params)
}

/**
 * 全景图热点数据接口
 */
export function getHotSpot () {
  return $http.get(api.getHotSpot)
}
