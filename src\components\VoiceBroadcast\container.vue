<template>
  <!-- 大禹知水 -->
  <div v-show="visible" class="voice-wrap">
    <!-- 关闭按钮 -->
    <span class="close-btn flx-center" @click="visible = false">
      <el-icon>
        <CloseBold />
      </el-icon>
    </span>

    <div class="voice-content">
      <!-- 问题框 -->
      <div class="voice-content-item pt0">
        <div class="voice-content-item-title ask"></div>
        <div class="voice-content-item-content ask">
          <div
            class="voice-content-item-content-text"
            v-show="showConversationDialog"
            ref="voiceTextPrintAsk"
          >
            {{ conversation.question }}
          </div>
          <span class="talk-icon ask" v-show="showConversationDialog"></span>
          <span class="arrow ask" v-show="showConversationDialog"></span>
        </div>
      </div>
      <!-- 回答框 -->
      <div class="voice-content-item flx-1">
        <div class="voice-content-item-title answer"></div>
        <div class="voice-content-item-content answer overflow-hide">
          <div
            class="voice-content-item-content-text answer"
            v-show="showConversationDialog"
            ref="voiceTextPrintAnswerRef"
          >
            <div v-if="processStatus" class="mb10 process-box">
              <div class="flx-justify-between">
                <div v-if="processStatus === 'pending'" class="flx-align-center">
                  分析中，请稍候
                  <div class="dot-flashing mt3"></div>
                </div>
                <div v-else-if="processStatus === 'succeeded'" class="flx-align-center">
                  <el-icon class="mt2" color="#42E75B"><SuccessFilled /></el-icon>
                  分析完成
                </div>
                <div v-else-if="processStatus === 'failed'" class="flx-align-center">
                  <el-icon class="mt2" color="#FF4B4B"><WarningFilled /></el-icon>
                  查询失败，请重试
                </div>
                <div v-else class="flx-align-center">
                  <el-icon class="mt2" color="#FFD060"><Opportunity /></el-icon>
                  未找到匹配答案
                </div>
              </div>
            </div>
            <!-- <span>{{ conversation.answer }}</span> -->
            <MdPreview
              class="md-preview"
              id="VoiceBroadcast"
              :noHighlight="true"
              :modelValue="conversation.answer"
            />

            <template v-if="conversation.intrestQuestions">
              <div
                class="intrest-questions"
                v-for="(item, index) in conversation.intrestQuestions"
                :key="item"
              >
                {{ index + 1 }}、{{ item }}
              </div>
            </template>
          </div>
          <span class="talk-icon answer" v-show="showConversationDialog"></span>
          <span class="arrow answer" v-show="showConversationDialog"></span>
        </div>
      </div>
      <!-- 输入框 -->
      <div class="ask-input-box">
        <div class="ask-input">
          <el-input
            class="ask-input-dom"
            :class="currentMole"
            type="textarea"
            ref="orderInputRef"
            v-model="orderInputVal"
            @input="debounceInputChange"
            @blur="handleBlur"
            @keyup.enter="sendOrder"
          />

          <div class="btns">
            <div class="voice-text-btn">
              <a
                @click="handleToggleMole('voice')"
                class="voice"
                :class="{ active: currentMole == 'voice' }"
                href="javascript:void(0);"
              >
                语音
              </a>
              <span class="split-line"></span>
              <a
                @click="handleToggleMole('text')"
                class="text"
                :class="{ active: currentMole == 'text' }"
                href="javascript:void(0);"
              >
                文字
              </a>
            </div>

            <div @click="sendOrder" class="send-btn" v-show="currentMole === 'text'">
              <img src="@/assets/images/voice/send-icon.png" alt="" /> 发送
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { debounce, throttle } from 'lodash-es'
import { MdPreview } from 'md-editor-v3'
import 'md-editor-v3/lib/preview.css'
import { isSymbol } from './utils'

defineOptions({ name: 'VoiceBroadcastContainer' })
// 对话记录
type Conversation = {
  question: string
  answer: string
  intrestQuestions?: string[]
}
// ai回答进度
type ProcessStatus = 'pending' | 'succeeded' | 'failed' | 'waiting'
const props = withDefaults(
  defineProps<{ conversation: Conversation; stopFocus?: boolean; processStatus?: ProcessStatus }>(),
  {
    stopFocus: false
  }
)

const visible = defineModel({ type: Boolean, default: false })

const emits = defineEmits(['send'])

const orderInputRef = useTemplateRef('orderInputRef')
const voiceTextPrintAnswerRef = useTemplateRef('voiceTextPrintAnswerRef')

// 语音及输入框相关
const orderInputVal = ref('')
// 当前模式，分 语音 和 文字
const currentMole = ref('text')

const showConversationDialog = computed(() => {
  return props.conversation.question.trim() !== ''
})

const handleToggleMole = val => {
  currentMole.value = val
  nextTick(() => {
    orderInputRef.value?.focus()
  })
}

// 语音输入模式、文字输入模式点击发送按钮
const sendOrder = () => {
  let question = orderInputVal.value.trim()
  if (!question) return

  // 剔除语音输入后面的符号
  if (isSymbol(question.slice(-1))) {
    question = question.substring(0, question.length - 1)
  }

  emits('send', question)
  orderInputVal.value = ''
  nextTick(() => {
    orderInputRef.value?.focus()
  })
}
const voiceInput = () => {
  // 如果当前是文字输入模式，return
  if (currentMole.value === 'text') return
  sendOrder()
}
const debounceInputChange = debounce(voiceInput, 1500)

/**
 * @description 监听输入框失焦
 */
const handleBlur = () => {
  if (props.stopFocus) {
    // 像是通过大禹知水打开的弹窗有输入框，要停止失焦事件，避免弹窗的输入框无法操作
    return
  }
  nextTick(() => {
    orderInputRef.value?.focus()
  })
}

const scrollToBottomMethod = () => {
  setTimeout(() => {
    if (voiceTextPrintAnswerRef.value) {
      // 回答框滚动条滚动到底部
      voiceTextPrintAnswerRef.value.scrollTop = voiceTextPrintAnswerRef.value.scrollHeight
    }
  }, 100)
}
const scrollToBottom = throttle(scrollToBottomMethod, 100)

// 监听语音模型开启关闭
watch(visible, val => {
  if (!val) {
    orderInputVal.value = ''
  }
})

defineExpose({ scrollToBottom })
</script>
<style lang="scss" scoped>
.voice-wrap {
  position: fixed;
  top: 98px;
  right: 45px;
  bottom: 46px;
  z-index: 1999;
  box-sizing: border-box;
  width: 470px;
  padding: 35px 0 0;
  font-size: 14px;
  line-height: 22px;
  color: #fff;
  white-space: pre-wrap;
  background-image: url('@/assets/images/voice/voice-content-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .voice-search-text {
    font-weight: 600;
    color: #fd7;
  }

  // 关闭按钮
  .close-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 22px;
    height: 22px;
    color: #7ff;
    cursor: pointer;
    background: #ffffff1a;
    border-radius: 4px;
  }

  .voice-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .voice-content-item {
      box-sizing: border-box;
      display: flex;
      flex-shrink: 0;
      flex-direction: column;
      height: 269px;
      padding: 18px;
      overflow: hidden;

      &:first-of-type {
        border-bottom: 1px solid #39b0ffcc;
      }
    }

    .ask-input-box {
      display: flex;
      flex-direction: column;
      justify-content: end;
      background: linear-gradient(181deg, #0048afa6 -7.85%, #0044868f 99.09%);
    }

    .ask-input {
      position: relative;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      padding: 12px;
      background: linear-gradient(178deg, #1638e800 -30.28%, #0088ff8c 98.39%);

      .ask-input-dom {
        --el-input-border-color: #39b0ff66;

        height: 117px;
        margin-bottom: 8px;

        &.voice {
          position: absolute;
          bottom: 72px;
          left: 12px;
          width: calc(100% - 24px);
          margin-bottom: unset;
          opacity: 0;
        }
      }

      :deep(.el-textarea__inner) {
        box-sizing: border-box;
        min-height: 100% !important;
        padding: 12px;
        font-weight: 600;
        color: #fff;
        background: #0003;
        border: 1px solid #39b0ff66;
      }

      .btns {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .voice-text-btn {
          display: flex;
          align-items: center;

          a {
            // height: 16px;
            padding-left: 24px;
            color: #fff;
            text-decoration: none;
            background-repeat: no-repeat;
            background-position: left center;
            background-size: 16px 16px;

            &.voice {
              background-image: url('@/assets/images/voice/voice-icon.png');
            }

            &.text {
              background-image: url('@/assets/images/voice/text-icon.png');
            }

            &.active {
              color: #58faf7;

              &.voice {
                background-image: url('@/assets/images/voice/voice-icon--active.png');
              }

              &.text {
                background-image: url('@/assets/images/voice/text-icon--active.png');
              }
            }
          }

          .split-line {
            width: 1px;
            height: 38px;
            margin: 0 25px;
            background: #5cacff8a;
          }
        }

        .send-btn {
          box-sizing: border-box;
          display: inline-flex;
          gap: 8px;
          align-items: center;
          justify-content: center;
          width: 76px;
          height: 32px;
          cursor: pointer;
          background: radial-gradient(106.99% 117.88% at 0% 0%, #09fc 0%, #0099ff52 100%);
          border: 1px solid #2fb3e9;
          border-radius: 4px;
          box-shadow: 0 0 7px 0 #00b2ff inset;
        }
      }
    }

    .voice-content-item-title {
      width: 106px;
      height: 36px;
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &.ask {
        background-image: url('@/assets/images/voice/ask.png');
      }

      &.answer {
        background-image: url('@/assets/images/voice/answer.png');
      }
    }

    .voice-content-item-content {
      position: relative;
      box-sizing: border-box;
      flex: 1;

      &.ask {
        padding: 18px 70px 0 0;
      }

      &.answer {
        padding: 18px 0 0 70px;
      }

      .talk-icon {
        position: absolute;
        top: 15px;
        width: 50px;
        height: 50px;
        background-repeat: no-repeat;
        background-size: 100% 100%;

        &.ask {
          right: 0;
          background-image: url('@/assets/images/voice/talk-user.png');
        }

        &.answer {
          left: 0;
          background-image: url('@/assets/images/voice/talk-robot.png');
        }
      }

      .arrow {
        position: absolute;
        top: 40px;
        width: 10px;
        height: 10px;
        background: radial-gradient(316.4% 140.53% at 98.34% 105.97%, #007ce8cc 0%, #52b1ff52 100%);

        &.ask {
          right: 60px;
          clip-path: polygon(0 0, 100% 50%, 0 100%); /* 创建三角形 */
        }

        &.answer {
          left: 60px;
          background: radial-gradient(345.71% 141.56% at 0% 0%, #fccb5575 0%, #ba9848cc 100%);
          clip-path: polygon(100% 0, 0 50%, 100% 100%); /* 创建三角形 */
        }
      }

      .voice-content-item-content-text {
        box-sizing: border-box;
        min-height: 50px;
        max-height: 100%;
        padding: 12px 15px;
        overflow-y: auto;
        scroll-behavior: smooth;
        font-weight: 500;
        line-height: 22px;
        background: radial-gradient(316.4% 140.53% at 98.34% 105.97%, #007ce8cc 0%, #52b1ff52 100%);
        border-radius: 4px;

        &.answer {
          background: radial-gradient(345.71% 141.56% at 0% 0%, #fccb5575 0%, #ba9848cc 100%);
        }
      }
    }
  }
}

$dot-size: 6px;
$dot-margin: 9px;

.dot-flashing {
  position: relative;
  width: $dot-size;
  height: $dot-size;
  margin-left: 12px;
  color: #fff;
  background-color: #fff;
  border-radius: 50%;
  animation: dot-flashing 1s infinite linear alternate;
  animation-delay: 0.5s;

  &::before {
    left: -$dot-margin;
    animation: dot-flashing 1s infinite alternate;
    animation-delay: 0s;
  }

  &::after {
    left: $dot-margin;
    animation: dot-flashing 1s infinite alternate;
    animation-delay: 1s;
  }

  &::before,
  &::after {
    position: absolute;
    top: 0;
    display: inline-block;
    width: $dot-size;
    height: $dot-size;
    color: #fff;
    content: '';
    background-color: #fff;
    border-radius: 50%;
  }
}

@keyframes dot-flashing {
  0% {
    background-color: #fff;
  }

  100% {
    background-color: #ffffff4d;
  }
}

.process-box {
  .expand-icon {
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      transform: rotate(180deg);
    }
  }

  .process-list {
    display: grid;
    grid-template-rows: 0fr;
    overflow: hidden;
    transition: 250ms grid-template-rows ease-in-out;

    &.show {
      grid-template-rows: 1fr;
    }
  }
}

.intrest-questions {
  color: #ffde90;
  text-decoration: underline;
}

.md-preview {
  --md-color: #fff;
  --md-bk-color: transparent;

  font-family: SourceHanSansSC-Regular, sans-serif;

  :deep(.md-editor-preview) {
    font-size: 14px;
    line-height: 1;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    p,
    ul,
    ol,
    ol li,
    ul li {
      margin: 0;
      line-height: 1;
    }

    p,
    ol li,
    ul li {
      line-height: 1.5;
    }
  }
}
</style>
