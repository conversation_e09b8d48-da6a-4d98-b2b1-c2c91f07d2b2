/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    ArrowTitle: typeof import('./components/ArrowTitle/index.vue')['default']
    BaseButton: typeof import('./components/BaseButton/index.vue')['default']
    BaseCollapse: typeof import('./components/BaseCollapse/index.vue')['default']
    BaseCollapseItem: typeof import('./components/BaseCollapse/BaseCollapseItem.vue')['default']
    BaseDialog: typeof import('./components/BaseDialog/index.vue')['default']
    BaseEmpty: typeof import('./components/BaseEmpty/index.vue')['default']
    BaseInfo: typeof import('./components/ReservoirInfoDialog/components/BaseInfo.vue')['default']
    BaseLegend: typeof import('./components/BaseLegend/index.vue')['default']
    BaseRecord: typeof import('./components/BaseRecord/index.vue')['default']
    BaseTabs: typeof import('./components/BaseTabs/index.vue')['default']
    BaseTitle: typeof import('./components/BaseTitle/index.vue')['default']
    Container: typeof import('./components/VoiceBroadcast/container.vue')['default']
    DamInfo: typeof import('./components/ReservoirInfoDialog/components/DamInfo.vue')['default']
    DescRecord: typeof import('./components/DescRecord/index.vue')['default']
    DetailDialog: typeof import('./components/PlanSelect/components/PlanSelectDialog/components/DetailDialog.vue')['default']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCalendar: typeof import('element-plus/es')['ElCalendar']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCarousel: typeof import('element-plus/es')['ElCarousel']
    ElCarouselItem: typeof import('element-plus/es')['ElCarouselItem']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSegmented: typeof import('element-plus/es')['ElSegmented']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    FilePreview: typeof import('./components/FilePreview/index.vue')['default']
    FilePreviewDialog: typeof import('./components/FilePreviewDialog/index.vue')['default']
    HydroInfo: typeof import('./components/ReservoirInfoDialog/components/HydroInfo.vue')['default']
    LeftRegion: typeof import('./layouts/components/LeftRegion.vue')['default']
    LegendDepth: typeof import('./components/BaseLegend/legendDepth.vue')['default']
    LegendMonitor: typeof import('./components/BaseLegend/legendMonitor.vue')['default']
    LegendRain: typeof import('./components/BaseLegend/legendRain.vue')['default']
    Map3d: typeof import('./components/Map3d/index.vue')['default']
    MapTools: typeof import('./components/Map3d/MapTools.vue')['default']
    Pdf: typeof import('./components/Preview/pdf.vue')['default']
    PdfPreview: typeof import('./components/PdfPreview/index.vue')['default']
    PlanCollapse: typeof import('./components/PlanSelect/components/PlanSelectDialog/components/PlanCollapse/index.vue')['default']
    PlanCollapseItem: typeof import('./components/PlanSelect/components/PlanSelectDialog/components/PlanCollapse/PlanCollapseItem.vue')['default']
    PlanSelect: typeof import('./components/PlanSelect/index.vue')['default']
    PlanSelectDialog: typeof import('./components/PlanSelect/components/PlanSelectDialog/index.vue')['default']
    ProgressBar: typeof import('./components/ProgressBar/index.vue')['default']
    RealTimeForecast: typeof import('./components/PlanSelect/components/PlanSelectDialog/components/RealTimeForecast.vue')['default']
    ReservoirInfoDialog: typeof import('./components/ReservoirInfoDialog/index.vue')['default']
    ReservoirTitle: typeof import('./components/ReservoirInfoDialog/components/ReservoirTitle.vue')['default']
    RightRegion: typeof import('./layouts/components/RightRegion.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SpillwayInfo: typeof import('./components/ReservoirInfoDialog/components/SpillwayInfo.vue')['default']
    TunnelInfo: typeof import('./components/ReservoirInfoDialog/components/TunnelInfo.vue')['default']
    VoiceBroadcast: typeof import('./components/VoiceBroadcast/index.vue')['default']
    WangEditor: typeof import('./components/WangEditor/index.vue')['default']
    WorkflowProcess: typeof import('./components/VoiceBroadcast/components/workflowProcess.vue')['default']
  }
  export interface ComponentCustomProperties {
    vInfiniteScroll: typeof import('element-plus/es')['ElInfiniteScroll']
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
