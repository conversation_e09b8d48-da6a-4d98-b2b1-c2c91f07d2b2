//planId : imgList
const satelliteCacheMap = ref<
  Record<
    string,
    {
      preloadedSatelliteImages: string[]
      preloadedRadarImages: string[]
    }
  >
>({})

// url : blob url
const urlCacheMap = ref<Record<string, string>>({})

// 撤销blob url以防止内存泄漏
const revokeImageUrls = (urls: string[]) => {
  for (const url of urls) {
    if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url)
    }
  }
}

const revokeAllImageUrls = () => {
  for (let pid in satelliteCacheMap.value) {
    const cache = satelliteCacheMap.value[pid]
    revokeImageUrls(cache.preloadedSatelliteImages)
    revokeImageUrls(cache.preloadedRadarImages)
  }
  satelliteCacheMap.value = {}
  urlCacheMap.value = {}
}

const initCache = (planId?: string | number) => {
  if (!planId) {
    return {
      preloadedSatelliteImages: [],
      preloadedRadarImages: []
    }
  }
  if (satelliteCacheMap.value[planId]) {
    return satelliteCacheMap.value[planId]
  }
  satelliteCacheMap.value[planId] = {
    preloadedSatelliteImages: [],
    preloadedRadarImages: []
  }
  return satelliteCacheMap.value[planId]
}

const processUrl = async (
  url: string,
  pid: number,
  target: keyof (typeof satelliteCacheMap.value)[string]
) => {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${url}, status: ${response.status}`)
    }
    const blob = await response.blob()
    const blobUrl = URL.createObjectURL(blob)
    const targetArray = satelliteCacheMap.value[pid][target]
    targetArray.push(blobUrl)
    urlCacheMap.value[url] = blobUrl
  } catch (error) {
    console.warn(`Failed to download or process image: ${url}`, error)
  }
}

export const useSatelliteCache = () => {
  return {
    satelliteCacheMap,
    revokeImageUrls,
    revokeAllImageUrls,
    initCache,
    urlCacheMap,
    processUrl
  }
}
