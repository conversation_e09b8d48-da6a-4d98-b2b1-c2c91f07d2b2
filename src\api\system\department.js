import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  // 机构列表
  getOrgLists: apiUrl.defaultUrl + "/org/list",
  // 获取部门树形结构
  getDepartmentList: apiUrl.defaultUrl + "/org/tree",
  // 获取部门详情
  getDepartmentDetail: apiUrl.defaultUrl + "/system/department/detail",
  // 添加部门
  addDepartment: apiUrl.defaultUrl + "/org/add",
  // 修改部门
  updateDepartment: apiUrl.defaultUrl + "/org/update",
  // 删除部门
  deleteDepartment: apiUrl.defaultUrl + "/org/delete"
};

// 获取部门列表
export function getOrgLists(pageNum,pageSize,params) {
  return $http.post(api.getOrgLists + `?pageNum=${pageNum}&pageSize=${pageSize}`,params);
}
// 获取部门列表
export function getDepartmentList(params) {
  return $http.post(api.getDepartmentList,params);
}

// 获取部门详情
export function getDepartmentDetail(id) {
  return $http.get(api.getDepartmentDetail + '/' + id);
}

// 添加部门
export function addDepartment(data) {
  return $http.post(api.addDepartment, data);
}

// 修改部门
export function updateDepartment(data) {
  return $http.post(api.updateDepartment, data);
}

// 删除部门
export function deleteDepartment(data) {
  return $http.post(api.deleteDepartment, data);
}