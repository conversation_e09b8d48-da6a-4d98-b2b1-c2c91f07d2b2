import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  pagelist: apiUrl.defaultUrl + '/szDispatchRecord/pagelist',
  deleteById: apiUrl.defaultUrl + '/szDispatchRecord/deleteById',
  save: apiUrl.defaultUrl + '/szDispatchRecord/save'
}

/**
 * 运行管护-水闸调度记录
 * @param data
 */
// 查询水闸调度记录信息
export function pagelist (data) {
  return $http.post(api.pagelist, data)
}
// 删除水闸调度记录
export function deleteById (data) {
  return $http.post(api.deleteById, data)
}
// 保存水闸调度记录
export function save (data) {
  return $http.post(api.save, data)
}
