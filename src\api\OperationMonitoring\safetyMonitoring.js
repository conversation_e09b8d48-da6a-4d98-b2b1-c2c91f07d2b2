import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getDamList: apiUrl.defaultUrl + '/skmnt/getDamList',
  getDamMntHistory: apiUrl.defaultUrl + '/skmnt/getDamMntHistory',
  getDamMntAllList: apiUrl.defaultUrl + '/skmnt/getDamMntAllList',
  getDamSafetyClassify: apiUrl.defaultUrl + '/skdsc/getDamSafetyClassify',
  safetyMonitoringExport: apiUrl.defaultUrl + '/mnt/safetyMonitoringExport',
  safetyMonitoringImport: apiUrl.defaultUrl + '/skmnt/importDamSafety',
  getProjectDamSafetyPage: apiUrl.defaultUrl + '/moreProjectMonitor/getProjectDamSafetyPage',
  getProjectDamSafetyChart: apiUrl.defaultUrl + '/moreProjectMonitor/projectDamSafetyChart',
  getSafeProjectOverviewVo: apiUrl.defaultUrl + '/moreProjectMonitor/getSafeProjectOverviewVo',
}

/**
 * 运行监控-安全检测接口
 * @param data
 */
// 测站类型
export function getDamList(data) {
  return $http.get(api.getDamList, data)
}
export function getDamMntHistory(data) {
  return $http.post(api.getDamMntHistory + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}
export function getDamMntAllList(data) {
  return $http.post(api.getDamMntAllList, data)
}
export function getDamSafetyClassify(data) {
  return $http.get(api.getDamSafetyClassify, data)
}

// 安全监测导出
export function safetyMonitoringExport(data, contentType, responseType) {
  return $http.postDownLoad(api.safetyMonitoringExport, data, contentType, responseType, false, true)
}
// 安全监测导入
export function safetyMonitoringImport(data) {
  return $http.postUpLoadFile(api.safetyMonitoringImport, data)
}
// 多工程运行监控-安全监测列表
export function getProjectDamSafetyPage(data) {
  return $http.post(api.getProjectDamSafetyPage, data)
}
// 多工程运行监控-安全监测列表统计数据
export function getProjectDamSafetyChart(data) {
  return $http.post(api.getProjectDamSafetyChart, data)
}
// 多工程安全监测总览--省市区用户
export function getSafeProjectOverviewVo(data) {
  return $http.post(api.getSafeProjectOverviewVo, data)
}
