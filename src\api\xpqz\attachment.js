import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteFile: apiUrl.defaultUrl + '/xpqz/common/attachment/del',
  getFileList: apiUrl.defaultUrl + '/xpqz/common/attachment/list',
  getAllOptionMap: apiUrl.defaultUrl + '/xpqz/common/option/getAllOptionMap'
}

export const downloadUrl =
  apiUrl.defaultUrl + '/xpqz/common/attachment/downLoad'

export const uploadUrl = apiUrl.defaultUrl + '/xpqz/common/attachment/upload'

export const previewUrl =
  apiUrl.defaultUrl + '/xpqz/common/attachment/show?path='

/**
 * 上传文件接口
 * @param data
 */
export function upload (data) {
  return $http.postUpLoadFile(uploadUrl, data)
}

/**
 * 下载文件接口
 */
export function downLoad (params, type) {
  return $http.getDownLoad(downloadUrl, params, undefined, type)
}

/**
 * 图片预览接口
 */
export function preview (params) {
  return $http.get(previewUrl, params)
}

/**
 * 删除文件接口
 */
export function deleteFile (data) {
  return $http.get(api.deleteFile, data)
}

/**
 * 下拉框集合接口
 */
export function getAllOptionMap (data) {
  return $http.get(api.getAllOptionMap, data)
}

/**
 * 查询附件接口
 * type: 传上传时的module值
 * name: 附件名称
 */
export function getFileList (data) {
  return $http.get(api.getFileList, data)
}
