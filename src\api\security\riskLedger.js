/* 安全管理-隐患台账 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  statisticsYhtz: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/statisticsYhtz',
  getYhtzList: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/getYhtzList',
  saveYhtz: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/saveYhtz',
  deleteYhtz: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/deleteYhtz',
  exportYhtz: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/getYhtzListExport',
  getAllHiddenDanger: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/getAllHiddenDanger',
  hiddenDangerLine: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/hiddenDangerLine',
  overviewList: apiUrl.defaultUrl + '/safetyHiddenDangerLedger/overviewList'
}

/**
 * 隐患台账-统计图表
 */
export function statisticsYhtz(data) {
  return $http.post(api.statisticsYhtz, data)
}

/**
 * 隐患台账-省市账号-按区划统计总览列表
 */
export function overviewList(data) {
  return $http.post(api.overviewList, data)
}

/**
 * 隐患台账-列表
 */
export function getYhtzList(data) {
  return $http.post(
    `${api.getYhtzList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  )
}

/**
 * 隐患台账-保存
 */
export function saveYhtz(data) {
  return $http.post(api.saveYhtz, data, '', 'arraybuffer')
}

/**
 * 隐患台账-导出
 */
export function exportYhtz(data, contentType, responseType) {
  return $http.postDownLoad(api.exportYhtz, data, contentType, responseType)
}

/**
 * 隐患台账-删除
 */
export function deleteYhtz(data) {
  return $http.post(api.deleteYhtz, data)
}

/**
 * 隐患台账-全部隐患(堤防)
 */
export function getAllHiddenDanger(data) {
  return $http.post(api.getAllHiddenDanger, data)
}

/**
 * 隐患台账-隐患统计线形图(堤防)
 */
export function hiddenDangerLine(data) {
  return $http.post(api.hiddenDangerLine, data)
}
