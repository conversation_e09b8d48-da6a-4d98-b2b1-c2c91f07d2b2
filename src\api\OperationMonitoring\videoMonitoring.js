import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  getVideoAreaList: apiUrl.defaultUrl + "/videoMonitor/getVideoAreaList",
  getVideoList: apiUrl.defaultUrl + "/videoMonitor/getVideoList",
  collect: apiUrl.defaultUrl + "/monitorCollect/collect",
  getVideoInfo: apiUrl.defaultUrl + "/videoMonitor/getVideoInfo",
  getMoreVideoList: apiUrl.defaultUrl + "/moreProjectMonitor/getVideoList",
  getVideoProjectOverviewVo:
    apiUrl.defaultUrl + "/moreProjectMonitor/getVideoProjectOverviewVo",
};

/**
 * 运行监控-视频监测接口
 * @param data
 */
// 1、视频列表
export function getVideoAreaList(data) {
  return $http.get(api.getVideoAreaList, data);
}
// 2、站点列表查询
export function getVideoList(data) {
  return $http.post(api.getVideoList, data);
}
// 3、收藏
export function collect(data) {
  return $http.post(api.collect, data);
}
// 4、获取站点信息
export function getVideoInfo(data) {
  return $http.get(api.getVideoInfo, data);
}
// 多工程运行监控-视频监控列表
export function getMoreVideoList(data) {
  return $http.post(api.getMoreVideoList, data);
}
// 多工程视频监测总览--省市区用户
export function getVideoProjectOverviewVo(data) {
  return $http.post(api.getVideoProjectOverviewVo, data);
}
