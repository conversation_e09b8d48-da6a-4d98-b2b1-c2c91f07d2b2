<template>
  <div class="left-nav stdLeftNav">
    <template v-for="(val, index) of menu">
      <el-menu-item
        :class="[
          'theme-menu-item',
          val.meta.title + val.meta.id === $store.state.menu.subActiveId
            ? 'is-active'
            : '',
        ]"
        v-if="!val.children"
        :index="val.meta.title + val.meta.id"
        :key="val.meta.id"
        @click="navigationTo(val, index)"
      >
        <div class="theme-menu-item-tilte">
          <img
            v-if="val.meta.icon"
            :src="require(`@/assets/${val.meta.icon}.png`)"
            class="theme-menu-icon"
          />
          <span slot="title">{{ val.meta.title }}</span>
        </div>
      </el-menu-item>
      <el-submenu
        v-else
        :index="val.meta.title + val.meta.id"
        :key="'submenu' + index"
      >
        <template slot="title">
          <img
            v-if="val.meta.icon"
            :src="require(`@/assets/${val.meta.icon}.png`)"
            class="theme-menu-icon"
          />
          <span>{{ val.meta.title }}</span>
        </template>
        <Aside
          v-bind="$attrs"
          v-on="$listeners"
          class="theme-submenu"
          :menu="val.children"
        />
      </el-submenu>
    </template>
  </div>
</template>

<script>
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Aside",
  data() {
    return {};
  },
  props: {
    menu: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    // 路由导航
    navigationTo(item, index) {
      if (item.meta.isExternalUrl) {
        window.open(item.meta.path);
      } else {
        this.$emit("click", item, index);
      }
    },
  },
  onMounted() {
    console.log("this.menu", this.menu);
  },
};
</script>

<style lang="scss" scoped>
.left-nav {
  background-color: #e9f2ff;
  height: 100%;
  overflow-y: auto;

  .theme-menu-icon {
    width: 20px;
    height: 20px;
    user-select: none;
    margin-right: 12px;
  }

  .theme-menu-item-tilte {
    font-size: 22px;
    color: #222f40;
  }

  ::v-deep(.is-active) {
    .theme-menu-item-tilte {
      color: #0a4ead;
    }
  }

  ::v-deep(.el-menu-item) {
    margin-top: 24px;
    font-size: 22px;
  }

  ::v-deep(.el-submenu) {
    margin-top: 24px;
    .el-submenu__title {
      font-size: 22px;
    }
  }
}
</style>
