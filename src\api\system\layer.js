import $http from "@/utils/request";
import { withBaseUrl } from "@/utils/apiUrl";
import { uploadUrl } from "../attachment";

export const uploadShpOrGeojson = (data, t, uoloadProcessFn) => {
  return $http.postUpLoadFile(
    t === "geojson" ? uploadUrl : withBaseUrl("/gis/uploadGeom"),
    data,
    false,
    uoloadProcessFn,
    60 * 1000
  );
};

/**
 * POST
 * /shpFileManagement/pageList
 * 系统管理-shp文件分页查询
 * @param {Object} data
 * @param {string} [data.category]
 * @param {string} [data.layerName]
 * @param {number} [data.uploadid]
 * @param {string} [data.createdBy]
 * @param {string} [data.updatedBy]
 * @param {number} [data.pageNum]
 * @param {number} [data.pageSize]
 */
export const getShpFilePageList = (data) => {
  const { pageNum, pageSize } = data || {};
  delete data.pageNum;
  delete data.pageSize;
  return $http.post(
    withBaseUrl(
      `/shpFileManagement/pageList?pageNum=${pageNum}&pageSize=${pageSize}`
    ),
    data
  );
};

/**
 * 系统管理-shp文件新增
 * POST
 * /shpFileManagement/add
 * @param {Object} data
 * @param {string} data.category
 * @param {string} data.layerName
 * @param {string} data.uploadid
 * @param {string} data.createdBy 创建人,传用户id
 */
export const addShpFile = (data) => {
  return $http.post(withBaseUrl("/shpFileManagement/add"), data);
};

/**
 * 系统管理-shp文件删除
 * GET
 * /shpFileManagement/delete
 * @param {number} id
 */
export const deleteShpFile = (id) => {
  return $http.get(withBaseUrl(`/shpFileManagement/delete?id=${id}`));
};

/**
 * 系统管理-shp文件修改
 * POST
 * /shpFileManagement/update
 * @param {Object} data
 * @param {number} data.id
 * @param {string} data.updatedBy 修改人,传用户id
 * @param {string} [data.category]
 * @param {string} [data.layerName]
 * @param {string} [data.uploadid]
 */
export const updateShpFile = (data) => {
  return $http.post(withBaseUrl("/shpFileManagement/update"), data);
};
