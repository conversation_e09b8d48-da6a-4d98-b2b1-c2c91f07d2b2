 /* 数据字典管理 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  // 获取字典类型列表
  getDictionaryTreeList: apiUrl.defaultUrl + "/dict/list",
  // 获取字典数据列表
  getDictDataList: apiUrl.defaultUrl + "/dict/page",
  // 添加字典数据
  addDictData: apiUrl.defaultUrl + "/dict/add",
  // 数据字典批量新增
  addBatchData: apiUrl.defaultUrl + "/dict/batch-add",
  // 修改字典数据
  updateDictData: apiUrl.defaultUrl + "/dict/update",
  // 删除字典数据
  deleteDictData: apiUrl.defaultUrl + "/dict/delete",
  // 删除字典数据
  getDictData: apiUrl.defaultUrl + '/dict/find-by-code-enabled'
};

// 数据字典树形结构
export function getDictionaryTreeList(data) {
  return $http.get(api.getDictionaryTreeList,data);
}

// 获取字典数据列表
export function getDictDataList(data) {
  return $http.get(api.getDictDataList, data);
}

// 添加字典数据
export function addDictData(data) {
  return $http.post(api.addDictData, data);
}
// 批量添加字典数据
export function addBatchData(data) {
  return $http.post(api.addBatchData, data);
}

// 修改字典数据
export function updateDictData(data) {
  return $http.post(api.updateDictData, data);
}

// 删除字典数据
export function deleteDictData(ids) {
  return $http.post(api.deleteDictData, ids);
}

// 获取数据字典
export function getDictData (params) {
  return $http.get(api.getDictData, params)
}
