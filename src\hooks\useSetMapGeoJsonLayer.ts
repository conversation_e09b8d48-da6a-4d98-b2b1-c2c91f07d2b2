import { useMap3dStore } from '@/stores/modules/map3d'
import { Color } from '@/stores/interface/map3d'
import { getGeoJson, getLayerInfo } from '@/alova_api/methods/gis'
import { getGeojsonFileByCategory } from '@/api/module/fourManagement/ensureSafety'

const { removeLayer, addCustomGeojsonLayer } = useMap3dStore()

export type LayerCategoryConfig = {
  /**
   * 分类字典值
   */
  category: string
  /**
   * 文字颜色
   */
  textColor: Color
  /**
   * 边框颜色
   */
  borderColor: Color
  /**
   * 标签属性
   */
  labelKey?: string
  /**
   * 要素id属性
   */
  featureIdAttr?: string
  /**
   * 设置屏幕空间中距此标签原点的像素偏移量
   */
  pixelOffset?: [number, number]
  /**
   * 文字
   */
  text?: string
  /**
   * 是否缩放
   */
  zoom?: boolean
}

export const getLayerGeojsonByInfo = async (item: {
  attachmentType: string
  category: string
}): Promise<Record<string, any> | undefined> => {
  if (item.attachmentType === '1') {
    //geojson
    return (await getGeojsonFileByCategory(item.category))?.data
  } else {
    //shp
    return getGeoJson(item.category, false)
  }
}

export class useSetMapGeoJsonLayer {
  layerIds: string[]
  constructor() {
    this.layerIds = []
  }
  geojsonRender(json: Record<string, any>, opt: LayerCategoryConfig) {
    const { textColor, borderColor, labelKey, featureIdAttr, text, zoom, pixelOffset } = opt
    const id = 'protect-geojson-layer_' + Math.floor(Math.random() * 1000) + 1
    addCustomGeojsonLayer({
      id,
      // data: URL.createObjectURL(new Blob([JSON.stringify(json)], { type: 'application/json' })),
      data: json,
      symbol: {
        enableFeatureLabel: true,
        featureLabel: {
          featureIdAttr: featureIdAttr || labelKey,
          fillColor: textColor,
          outlineColor: { r: 255, g: 255, b: 255, a: 1 }, //字体描边颜色
          outlineWidth: 3, //描边宽度
          font: '20px', //字体设置
          showBackground: true, //是否显示背景颜色
          pixelOffset: pixelOffset || [0, 0], //设置屏幕空间中距此标签原点的像素偏移量
          text: function (properties) {
            return text || properties[labelKey || 'name']
          }
        },
        polygon: {
          color: { r: 255, g: 255, b: 255, a: 0.5 },
          outlineColor: borderColor,
          outlineWidth: 4,
          outline: true,
          fill: false,
          minLevel: 4, //最小地图显示层级
          maxLevel: 20, //最大地图显示层级
          zIndex: 101,
          clampToGround: true //设置不要贴地
        }
      },
      zoom: typeof zoom === 'undefined' ? false : zoom
    })
    this.layerIds.push(id)
  }
  async layerRender(opts: LayerCategoryConfig[] = []) {
    try {
      const layerInfos = await Promise.allSettled(opts.map(o => getLayerInfo(o.category)))
      const failedConfs = layerInfos.filter(item => item.status === 'rejected')
      if (failedConfs.length) {
        console.error('图层渲染失败', failedConfs)
      }

      const successConfs = layerInfos
        .filter(item => item.status === 'fulfilled')
        .map(item => item.value)
      for (const item of successConfs) {
        const json = await getLayerGeojsonByInfo(item)
        if (json) {
          this.geojsonRender(json, opts.find(o => o.category === item.category)!)
        } else {
          console.error('图层渲染失败', item)
        }
      }
    } catch (error) {
      console.error('图层渲染失败', error)
    }
  }
  removeLayerByName(layerName: string) {
    if (layerName) {
      removeLayer(layerName)
      this.layerIds.splice(this.layerIds.indexOf(layerName), 1)
    }
  }

  removeAllLayer() {
    if (Array.isArray(this.layerIds) && this.layerIds.length) {
      for (const id of this.layerIds) {
        removeLayer(id)
      }
      this.layerIds = []
    }
  }
}
