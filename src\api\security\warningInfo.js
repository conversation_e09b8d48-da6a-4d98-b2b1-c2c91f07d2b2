/* 安全管理-预警信息 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  warnInfoList: apiUrl.defaultUrl + '/warningInfo/warnInfoList',
  exportWarnInfo: apiUrl.defaultUrl + '/warningInfo/exportWarnInfo',
  charts: apiUrl.defaultUrl + '/warningInfo/charts',
  overviewList: apiUrl.defaultUrl + '/warningInfo/overviewList'
}

/**
 * 预警信息-列表
 */
export function warnInfoList(data) {
  return $http.post(api.warnInfoList, data)
}

/**
 * 预警信息-图表统计
 */
export function charts(data) {
  return $http.post(api.charts, data)
}

/**
 * 预警信息-省市账号-按区划统计总览列表
 */
export function overviewList(data) {
  return $http.post(api.overviewList, data)
}

/**
 * 预警信息-导出
 */
export function exportWarnInfo(data) {
  return $http.postDownLoad(api.exportWarnInfo, data, '', 'arraybuffer', false, true)
}
