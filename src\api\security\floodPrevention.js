/* 安全管理-防汛管理 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  // 防汛检查
  findCheckPageList: apiUrl.defaultUrl + '/flood/check/findCheckPageList',
  findCheckInfoById: apiUrl.defaultUrl + '/flood/check/findCheckInfoById',
  checkAdd: apiUrl.defaultUrl + '/flood/check/add',
  checkUpdate: apiUrl.defaultUrl + '/flood/check/update',
  checkDelete: apiUrl.defaultUrl + '/flood/check/delete',
  // 防汛演练
  findDrillPageList: apiUrl.defaultUrl + '/flood/drill/findDrillPageList',
  findDrillById: apiUrl.defaultUrl + '/flood/drill/findDrillById',
  drillAdd: apiUrl.defaultUrl + '/flood/drill/add',
  drillUpdate: apiUrl.defaultUrl + '/flood/drill/update',
  drillDelete: apiUrl.defaultUrl + '/flood/drill/delete',
  // 防汛物资
  findMaterialsPageList: apiUrl.defaultUrl + '/flood/materials/list',
  findMaterials: apiUrl.defaultUrl + '/flood/materials/loadByNameAndSpec',
  materialsAdd: apiUrl.defaultUrl + '/flood/materials/add',
  materialsUpdate: apiUrl.defaultUrl + '/flood/materials/updateMaterials',
  materialsDelete: apiUrl.defaultUrl + '/flood/materials/delete',
  materialsDeleteById: apiUrl.defaultUrl + '/flood/materials/deleteById',
  materialsExport: apiUrl.defaultUrl + '/flood/materials/listExport',
  // 总览图表
  curMonthCharts: apiUrl.defaultUrl + '/flood/check/curMonthCharts',
  overviewList: apiUrl.defaultUrl + '/flood/check/overviewList',
  // 防汛队伍
  findTeamPageList: apiUrl.defaultUrl + '/flood/team/findTeamPageList',
  findTeamById: apiUrl.defaultUrl + '/flood/team/findTeamById',
  teamAdd: apiUrl.defaultUrl + '/flood/team/add',
  teamUpdate: apiUrl.defaultUrl + '/flood/team/update',
  teamDelete: apiUrl.defaultUrl + '/flood/team/delete',
  // 防汛专家
  findExpertPageList: apiUrl.defaultUrl + '/flood/expert/findExpertPageList',
  findExpertById: apiUrl.defaultUrl + '/flood/expert/findExpertById',
  expertAdd: apiUrl.defaultUrl + '/flood/expert/add',
  expertUpdate: apiUrl.defaultUrl + '/flood/expert/update',
  expertDelete: apiUrl.defaultUrl + '/flood/expert/delete',

  // 防汛仓库
  findWarehousePageList: apiUrl.defaultUrl + '/flood/warehouse/findPageList',
  warehouseAdd: apiUrl.defaultUrl + '/flood/warehouse/add',
  warehouseUpdate: apiUrl.defaultUrl + '/flood/warehouse/update',
  warehouseDelete: apiUrl.defaultUrl + '/flood/warehouse/delete',
  queryWarehouseListAll: apiUrl.defaultUrl + '/flood/warehouse/all'

}

/**
 * 防汛管理-防汛检查-列表
 */
export function findCheckPageList(data) {
  return $http.post(
    `${api.findCheckPageList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  )
}

/**
 * 防汛管理-防汛检查-编辑详情
 */
export function findCheckInfoById(params) {
  return $http.get(api.findCheckInfoById, params)
}

/**
 * 防汛管理-防汛检查-新增
 */
export function checkAdd(data) {
  return $http.post(api.checkAdd, data)
}
/**
 * 防汛管理-防汛检查-编辑
 */
export function checkUpdate(data) {
  return $http.post(api.checkUpdate, data)
}
/**
 * 防汛管理-防汛检查-删除
 */
export function checkDelete(data) {
  return $http.post(api.checkDelete, data)
}

/**
 * 防汛管理-防汛演练-列表
 */
export function findDrillPageList(data) {
  return $http.post(
    `${api.findDrillPageList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  )
}

/**
 * 防汛管理-防汛演练-编辑详情
 */
export function findDrillById(params) {
  return $http.get(api.findDrillById, params)
}

/**
 * 防汛管理-防汛演练-新增
 */
export function drillAdd(data) {
  return $http.post(api.drillAdd, data)
}
/**
 * 防汛管理-防汛演练-编辑
 */
export function drillUpdate(data) {
  return $http.post(api.drillUpdate, data)
}
/**
 * 防汛管理-防汛演练-删除
 */
export function drillDelete(data) {
  return $http.post(api.drillDelete, data)
}

/**
 * 防汛管理-防汛物资-列表
 */
export function findMaterialsPageList(data) {
  return $http.post(
    `${api.findMaterialsPageList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  )
}

/**
 * 防汛管理-防汛物资-编辑详情
 */
export function findMaterials(params) {
  return $http.get(api.findMaterials, params)
}

/**
 * 防汛管理-防汛物资-新增
 */
export function materialsAdd(data) {
  return $http.post(api.materialsAdd, data)
}
/**
 * 防汛管理-防汛物资-编辑
 */
export function materialsUpdate(data) {
  return $http.post(api.materialsUpdate, data)
}
/**
 * 防汛管理-防汛物资-删除
 * name 名称
 * spec 规格
 */
export function materialsDelete(params) {
  return $http.get(api.materialsDelete, params)
}
/**
 * 防汛管理-防汛物资-删除
 * id
 */
export function materialsDeleteById(params) {
  return $http.get(api.materialsDeleteById, params)
}

/**
 * 防汛管理-防汛物资-导出
 */
export function materialsExport(data, contentType, responseType) {
  return $http.postDownLoad(api.materialsExport, data, contentType, responseType, false, false)
}
// 防汛管理 - 当月检查、演练完成情况图表统计
export function curMonthCharts(data) {
  return $http.post(api.curMonthCharts, data)
}

// 防汛管理 - 省市账号-按区划统计总览列表
export function overviewList(data) {
  return $http.post(api.overviewList, data)
}

/**
 * 防汛管理-防汛队伍-列表
 */
export function findTeamPageList(data) {
  return $http.post(
    `${api.findTeamPageList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  )
}

/**
 * 防汛管理-防汛队伍-编辑详情
 */
export function findTeamById(params) {
  return $http.get(api.findTeamById, params)
}

/**
 * 防汛管理-防汛队伍-新增
 */
export function teamAdd(data) {
  return $http.post(api.teamAdd, data)
}

/**
 * 防汛管理-防汛队伍-编辑
 */
export function teamUpdate(data) {
  return $http.post(api.teamUpdate, data)
}

/**
 * 防汛管理-防汛队伍-删除
 */
export function teamDelete(data) {
  return $http.get(api.teamDelete, data)
}

/**
 * 防汛管理-防汛专家-列表
 */
export function findExpertPageList(data) {
  return $http.post(
    `${api.findExpertPageList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  )
}

/**
 * 防汛管理-防汛专家-编辑详情
 */
export function findExpertById(params) {
  return $http.get(api.findExpertById, params)
}

/**
 * 防汛管理-防汛专家-新增
 */
export function expertAdd(data) {
  return $http.post(api.expertAdd, data)
}

/**
 * 防汛管理-防汛专家-编辑
 */
export function expertUpdate(data) {
  return $http.post(api.expertUpdate, data)
}

/**
 * 防汛管理-防汛专家-删除
 */
export function expertDelete(data) {
  return $http.get(api.expertDelete, data)
}


/**
 * 防汛管理-防汛仓库-列表
 */
export function findWarehousePageList(data) {
  return $http.post(
    `${api.findWarehousePageList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  )
}

/**
 * 防汛管理-防汛仓库-新增
 */
export function warehouseAdd(data) {
  return $http.post(api.warehouseAdd, data)
}

/**
 * 防汛管理-防汛仓库-编辑
 */
export function warehouseUpdate(data) {
  return $http.post(api.warehouseUpdate, data)
}

/**
 * 防汛管理-防汛仓库-删除
 */
export function warehouseDelete(data) {
  return $http.get(api.warehouseDelete, data)
}

/**
 * 防汛管理-防汛仓库-所有列表
 */
export function queryWarehouseListAll(data) {
  return $http.get(api.queryWarehouseListAll, data)
}
