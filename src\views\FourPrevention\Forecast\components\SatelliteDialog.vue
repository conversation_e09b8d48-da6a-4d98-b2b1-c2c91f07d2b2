<template>
  <base-dialog
    v-model:showDialog="dialogVisible"
    width="80vw"
    title="气象卫星"
    align-center
    :append-to-body="true"
    :show-footer="false"
  >
    <div class="satellite-content">
      <!-- 左侧图片预览区域 -->
      <div class="image-preview">
        <div class="preview-container">
          <el-image
            v-if="currentImg?.imgUrl"
            :src="currentImg.imgUrl"
            :preview-src-list="previewList"
            :initial-index="currentPreviewIndex"
            fit="contain"
            class="preview-image"
            :preview-teleported="true"
          >
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
          <div v-else class="no-image">
            <el-icon><Picture /></el-icon>
            <span>请选择图片</span>
          </div>
        </div>
      </div>

      <!-- 右侧控制区域 -->
      <div class="control-panel">
        <!-- 上方：切换组件 -->
        <div class="tab-section">
          <el-segmented
            class="c-el-segmented"
            v-model="satelliteTabIndex"
            :options="[
              { label: '卫星云图', value: 0 },
              { label: '雷达图', value: 1 }
            ]"
            @change="changeSatelliteTab"
          />
        </div>

        <!-- 下方：时间列表 -->
        <div class="time-list-section">
          <div
            v-for="(img, index) in currentImgList"
            :key="img.id"
            class="time-item"
            :class="{ active: activeItem === img.id }"
            @click="selectImage(img.id, index)"
          >
            <div class="time-text">{{ img.time }}</div>
            <div class="time-index">{{ index + 1 }}/{{ currentImgList.length }}</div>
          </div>

          <div v-if="currentImgList.length === 0" class="empty-list">
            <el-icon><Picture /></el-icon>
            <span>暂无图片数据</span>
          </div>
        </div>
      </div>
    </div>
  </base-dialog>
</template>
<script lang="ts" setup>
import { getSatelliteImage } from '@/alova_api/methods/forecast'
import { useSatelliteCache } from '../useSatelliteCache'
import { useWatcher } from 'alova/client'
import { satelliteShowUrl } from '@/api/module/attachment'

const dialogVisible = defineModel<boolean>({
  default: false
})
const props = defineProps({
  planId: {
    type: Number,
    default: 0
  }
})
const { urlCacheMap } = useSatelliteCache()
const { data } = useWatcher(() => getSatelliteImage(props.planId), [() => props.planId], {
  initialData: {}
})
// 气象卫星数据
const satelliteTabIndex = ref(0)
const activeItem = ref('')
const currentPreviewIndex = ref(0)

const changeSatelliteTab = () => {
  // 切换标签时重置选中项
  nextTick(() => {
    if (currentImgList.value.length) {
      activeItem.value = currentImgList.value[0].id
    }
  })
}

// 选择图片
const selectImage = (id: string, index: number) => {
  activeItem.value = id
  currentPreviewIndex.value = index
}

// 预览图片
const previewImage = (index: number) => {
  currentPreviewIndex.value = index
  // 触发 el-image 的预览功能
  const img = currentImgList.value[index]
  if (img) {
    activeItem.value = img.id
  }
}

const currentImgList = computed(() => {
  if (satelliteTabIndex.value === 0) {
    // 气象卫星
    return (
      data.value.satelliteCloudImages?.map(item => {
        return {
          imgUrl:
            urlCacheMap.value[satelliteShowUrl + item.localUrl] || satelliteShowUrl + item.localUrl,
          time: item.dataTime,
          id: item.sid
        }
      }) || []
    )
  }
  return (
    data.value.radarPuzzleImages?.map(item => {
      return {
        imgUrl:
          urlCacheMap.value[satelliteShowUrl + item.localUrl] || satelliteShowUrl + item.localUrl,
        time: item.dataTime,
        id: item.sid
      }
    }) || []
  )
})

const currentImg = computed(() => currentImgList.value.find(i => i.id === activeItem.value))

// 预览图片列表
const previewList = computed(() => currentImgList.value.map(img => img.imgUrl))

// 监听图片列表变化，自动选择第一张图片
watch(
  currentImgList,
  newList => {
    if (newList.length > 0 && !activeItem.value) {
      activeItem.value = newList[0].id
      currentPreviewIndex.value = 0
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.satellite-content {
  display: flex;
  gap: 20px;
  height: 600px;
  margin-top: 16px;
}

// 左侧图片预览区域
.image-preview {
  flex: 1;

  .preview-container {
    width: 100%;
    height: 100%;
    border: 1px solid #0b4eb3;
    border-radius: 8px;
    overflow: hidden;
    background: rgb(16 54 98 / 20%);
    display: flex;
    align-items: center;
    justify-content: center;

    .preview-image {
      width: 100%;
      height: 100%;

      :deep(.el-image__inner) {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .image-error,
    .no-image {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
      color: #c5e3ff;
      font-size: 16px;

      .el-icon {
        font-size: 64px;
        color: #7ff;
      }
    }
  }
}

// 右侧控制面板
.control-panel {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  // 上方：切换组件区域
  .tab-section {
    .c-el-segmented {
      width: 100%;

      .el-segmented__item {
        flex: 1;
        justify-content: center;
      }
    }
  }

  // 下方：时间列表区域
  .time-list-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    border: 1px solid #0b4eb3;
    background: rgb(16 54 98 / 20%);
    border-radius: 8px;

    .time-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: 4px;
      padding: 12px 16px;
      border-bottom: 1px solid rgb(11 78 179 / 30%);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgb(24 130 251 / 20%);
      }

      &.active {
        background: radial-gradient(106.99% 117.88% at 0% 0%, #09fc 0%, #0099ff52 100%);
        border-color: #2fb3e9;
        box-shadow: 0 0 7px 0 #00b2ff inset;

        .time-text {
          color: #7ff;
        }

        .time-index {
          color: #c5e3ff;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      .time-text {
        font-size: 14px;
        color: #fff;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .time-index {
        font-size: 12px;
        color: #c5e3ff;
        opacity: 0.8;
      }
    }

    .empty-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #c5e3ff;
      font-size: 16px;
      gap: 12px;

      .el-icon {
        font-size: 64px;
        color: #7ff;
      }
    }
  }
}
</style>
