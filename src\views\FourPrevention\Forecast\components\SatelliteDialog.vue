<template>
  <base-dialog
    v-model:showDialog="dialogVisible"
    width="70vw"
    title="气象卫星"
    align-center
    :append-to-body="true"
    :show-footer="false"
  >
    <base-tabs
      class="mt11"
      :active-index="satelliteTabIndex"
      :list="IMG_TYPE_TAB_LIST"
      @change-tab="changeSatelliteTab"
      :disabled="!planId"
      @click-disabled="handleClickDisabled"
    ></base-tabs>
    <div class="img-container">
      <el-image :src="currentImg?.imgUrl"></el-image>
      <div class="time-list">
        <div
          class="time-item"
          v-for="img in currentImgList"
          :key="img.id"
          @click="activeItem = img.id"
        >
          {{ img.time }}
        </div>
      </div>
    </div>
  </base-dialog>
</template>
<script lang="ts" setup>
import { getSatelliteImage } from '@/alova_api/methods/forecast'
import { IMG_TYPE_TAB_LIST } from '../constant'
import { useSatelliteCache } from '../useSatelliteCache'
import { useWatcher } from 'alova/client'
import { satelliteShowUrl } from '@/api/module/attachment'

const dialogVisible = defineModel<boolean>({
  default: false
})
const props = defineProps({
  planId: {
    type: Number,
    default: 0
  }
})
const { urlCacheMap } = useSatelliteCache()
const { data } = useWatcher(() => getSatelliteImage(props.planId), [() => props.planId], {
  initialData: {}
})
// 气象卫星数据
const satelliteTabIndex = ref(0)
const activeItem = ref('')

const changeSatelliteTab = (index: number) => {
  satelliteTabIndex.value = index
}

const handleClickDisabled = () => {
  ElMessage.warning('请先选择预报方案')
}

const currentImgList = computed(() => {
  if (satelliteTabIndex.value === 0) {
    // 气象卫星
    return (
      data.value.satelliteCloudImages?.map(item => {
        return {
          imgUrl: urlCacheMap.value[item.localUrl] || item.localUrl,
          time: item.dataTime,
          id: item.sid
        }
      }) || []
    )
  }
  return (
    data.value.radarPuzzleImages?.map(item => {
      return {
        imgUrl: urlCacheMap.value[satelliteShowUrl + item.localUrl] || item.localUrl,
        time: item.dataTime,
        id: item.sid
      }
    }) || []
  )
})

const currentImg = computed(() => currentImgList.value.find(i => i.id === activeItem.value))
</script>

<style lang="scss" scoped></style>
