<template>
  <base-dialog
    v-model:showDialog="dialogVisible"
    width="80vw"
    title="气象卫星"
    align-center
    :append-to-body="true"
    :show-footer="false"
  >
    <el-segmented
      class="sub-segmented c-el-segmented have-padding"
      v-model="satelliteTabIndex"
      :options="[
        { label: '卫星云图', value: 0 },
        { label: '雷达图', value: 1 }
      ]"
      @change="changeSatelliteTab"
    />

    <div class="satellite-content">
      <!-- 左侧图片预览区域 -->
      <div class="image-preview">
        <el-image
          v-if="currentImg?.imgUrl"
          :src="currentImg.imgUrl"
          :preview-src-list="previewList"
          :initial-index="currentPreviewIndex"
          fit="contain"
          class="preview-image"
          :preview-teleported="true"
        >
          <template #error>
            <div class="image-error">
              <el-icon><Picture /></el-icon>
              <span>图片加载失败</span>
            </div>
          </template>
        </el-image>
        <div v-else class="no-image">
          <el-icon><Picture /></el-icon>
          <span>请选择图片</span>
        </div>
      </div>
      <!-- 右侧图片列表 -->
      <div class="image-list">
        <div class="list-header">
          <span class="list-title">图片列表</span>
          <span class="list-count">({{ currentImgList.length }})</span>
        </div>

        <div class="list-container">
          <div
            v-for="(img, index) in currentImgList"
            :key="img.id"
            class="list-item"
            :class="{ active: activeItem === img.id }"
            @click="selectImage(img.id, index)"
          >
            <div class="item-thumbnail">
              <el-image :src="img.imgUrl" loading="lazy" fit="cover" class="thumbnail-image">
                <template #error>
                  <div class="thumbnail-error">
                    <el-icon><Picture /></el-icon>
                  </div>
                </template>
              </el-image>
            </div>
            <div class="item-info">
              <span class="item-time">{{ img.time }}</span>
            </div>

            <div class="item-actions">
              <el-button type="primary" size="small" text @click.stop="previewImage(index)">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </div>
          </div>

          <div v-if="currentImgList.length === 0" class="empty-list">
            <el-icon><Picture /></el-icon>
            <span>暂无图片数据</span>
          </div>
        </div>
      </div>
    </div>
  </base-dialog>
</template>
<script lang="ts" setup>
import { getSatelliteImage } from '@/alova_api/methods/forecast'
import { useSatelliteCache } from '../useSatelliteCache'
import { useWatcher } from 'alova/client'
import { satelliteShowUrl } from '@/api/module/attachment'

const dialogVisible = defineModel<boolean>({
  default: false
})
const props = defineProps({
  planId: {
    type: Number,
    default: 0
  }
})
const { urlCacheMap } = useSatelliteCache()
const { data } = useWatcher(() => getSatelliteImage(props.planId), [() => props.planId], {
  initialData: {}
})
// 气象卫星数据
const satelliteTabIndex = ref(0)
const activeItem = ref('')
const currentPreviewIndex = ref(0)

const changeSatelliteTab = () => {
  // 切换标签时重置选中项
  nextTick(() => {
    if (currentImgList.value.length) {
      activeItem.value = currentImgList.value[0].id
    }
  })
}

// 选择图片
const selectImage = (id: string, index: number) => {
  activeItem.value = id
  currentPreviewIndex.value = index
}

// 预览图片
const previewImage = (index: number) => {
  currentPreviewIndex.value = index
  // 触发 el-image 的预览功能
  const img = currentImgList.value[index]
  if (img) {
    activeItem.value = img.id
  }
}

const currentImgList = computed(() => {
  if (satelliteTabIndex.value === 0) {
    // 气象卫星
    return (
      data.value.satelliteCloudImages?.map(item => {
        return {
          imgUrl:
            urlCacheMap.value[satelliteShowUrl + item.localUrl] || satelliteShowUrl + item.localUrl,
          time: item.dataTime,
          id: item.sid
        }
      }) || []
    )
  }
  return (
    data.value.radarPuzzleImages?.map(item => {
      return {
        imgUrl:
          urlCacheMap.value[satelliteShowUrl + item.localUrl] || satelliteShowUrl + item.localUrl,
        time: item.dataTime,
        id: item.sid
      }
    }) || []
  )
})

const currentImg = computed(() => currentImgList.value.find(i => i.id === activeItem.value))

// 预览图片列表
const previewList = computed(() => currentImgList.value.map(img => img.imgUrl))

// 监听图片列表变化，自动选择第一张图片
watch(
  currentImgList,
  newList => {
    if (newList.length > 0 && !activeItem.value) {
      activeItem.value = newList[0].id
      currentPreviewIndex.value = 0
    }
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.satellite-content {
  display: flex;
  gap: 20px;
  height: 600px;
  margin-top: 16px;
}

// 左侧图片预览区域
.image-preview {
  flex: 1;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;

  .preview-image {
    width: 100%;
    height: 100%;

    :deep(.el-image__inner) {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .image-error,
  .no-image {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: #909399;
    font-size: 14px;

    .el-icon {
      font-size: 48px;
    }
  }
}

// 右侧图片列表
.image-list {
  width: 240px;
  display: flex;
  flex-direction: column;

  .list-header {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f5f7fa;
    border: 1px solid #e4e7ed;
    border-radius: 8px 8px 0 0;

    .list-title {
      font-weight: 600;
      color: #303133;
      font-size: 16px;
    }

    .list-count {
      color: #909399;
      font-size: 14px;
    }
  }

  .list-container {
    flex: 1;
    border: 1px solid #e4e7ed;
    border-top: none;
    border-radius: 0 0 8px 8px;
    overflow-y: auto;
    background: #fff;

    .list-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f2f5;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f5f7fa;
      }

      &.active {
        background: #e6f4ff;
        border-color: #1890ff;

        .item-thumbnail {
          border-color: #1890ff;
        }
      }

      &:last-child {
        border-bottom: none;
      }

      .item-thumbnail {
        width: 60px;
        height: 45px;
        border: 2px solid #e4e7ed;
        border-radius: 6px;
        overflow: hidden;
        flex-shrink: 0;

        .thumbnail-image {
          width: 100%;
          height: 100%;

          :deep(.el-image__inner) {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }

        .thumbnail-error {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background: #f5f7fa;
          color: #c0c4cc;

          .el-icon {
            font-size: 20px;
          }
        }
      }

      .item-actions {
        flex-shrink: 0;

        .el-button {
          padding: 4px;

          .el-icon {
            font-size: 16px;
          }
        }
      }
    }

    .empty-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #909399;
      font-size: 14px;
      gap: 8px;

      .el-icon {
        font-size: 48px;
      }
    }
  }
}

// 响应式适配
@media (max-width: 1200px) {
  .satellite-content {
    height: 500px;
  }

  .image-list {
    width: 280px;
  }
}
</style>
