import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  roleList: apiUrl.defaultUrl + '/xpqz/role/list',
  addRole: apiUrl.defaultUrl + '/xpqz/role/add',
  updateRole: apiUrl.defaultUrl + '/xpqz/role/update',
  deleteRole: apiUrl.defaultUrl + '/xpqz/role/delete',
  updateRoleStatus: apiUrl.defaultUrl + '/xpqz/role/updateStatus'
}

/**
 * 角色列表接口
 * @param data
 */
export function roleList (data) {
  return $http.post(api.roleList, data)
}

/**
 * 增加角色接口
 */
export function addRole (data) {
  return $http.post(api.addRole, data)
}

/**
 * 修改角色接口
 */
export function updateRole (data) {
  return $http.post(api.updateRole, data)
}

/**
 * 删除角色接口
 */
export function deleteRole (data) {
  return $http.post(api.deleteRole, data)
}

/**
 * 批量修改角色状态接口
 */
export function updateRoleStatus (data) {
  return $http.post(api.updateRoleStatus, data)
}
