/* 基础管理-标识标牌 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteById: apiUrl.defaultUrl + '/scoreIdentificationCards/deleteById',
  detail: apiUrl.defaultUrl + '/scoreIdentificationCards/details',
  list: apiUrl.defaultUrl + '/scoreIdentificationCards/list',
  save: apiUrl.defaultUrl + '/scoreIdentificationCards/save',
  placardsCounting: apiUrl.defaultUrl + '/scoreIdentificationCards/placardsCounting',
  breastplateList: apiUrl.defaultUrl + '/moreProject/getIdentificationCardOverview',
}

/**
 * 标识标牌-删除
 * id
 */
export function deleteById (data) {
  return $http.post(api.deleteById, data)
}

/**
 * 标识标牌-查看详情
 * id
 */
export function detail (params) {
  return $http.get(api.detail, params)
}

/**
 * 标识标牌-分页查询
 * "areaCode": "string",
 * "projectName": "string",
 * "projectScale": 0,
 * "projectType": "string"
 */
export function list (data, parmas) {
  return $http.postParams(api.list, data, parmas)
}

/**
 * 标识标牌-保存
 * "gcjjbsp": "string", 工程简介标识牌文件id
 * "gcjjbspTime": "2023-08-14", 文件上传时间
 * "ghfwgsp": "string", 管护范围公示牌文件id
 * "ghfwgspTime": "2023-08-14", 文件上传时间
 * "hpjsp": "string", 滑坡警示牌文件id
 * "hpjspTime": "2023-08-14", 文件上传时间
 * "id": 0, 主键
 * "jcssmcp": "string", 监测设施名称牌文件id
 * "jcssmcpTime": "2023-08-14", 文件上传时间
 * "jsbx": "string", 警示标线文件id
 * "jsbxTime": "2023-08-14", 文件上传时间
 * "jz": "string", 界桩文件id
 * "jzTime": "2023-08-14", 文件上传时间
 * "projectId": 0, 工程id
 * "sfgxcp": "string", 水法规宣传牌文件id
 * "sfgxcpTime": "2023-08-14", 文件上传时间
 * "ssjsp": "string", 深水警示牌文件id
 * "ssjspTime": "2023-08-14", 文件上传时间
 * "xcgzxlzyp": "string", 巡查工作线路指引牌文件id
 * "xcgzxlzypTime": "2023-08-14", 文件上传时间
 * "xxswbx": "string", 汛限水位标线文件id
 * "xxswbxTime": "2023-08-14", 文件上传时间
 * "zljsp": "string", 坠落警示牌文件id
 * "zljspTime": "2023-08-14", 文件上传时间
 * "zrrbp": "string", 责任人标牌文件id
 * "zrrbpTime": "2023-08-14" 文件上传时间
 */
export function save (data) {
  return $http.post(api.save, data)
}

/**
 * 数量统计
 * "areaCode": "string",
  "projectName": "string",
  "projectScale": 0,
  "projectType": "string"
 */
export function placardsCounting (data) {
  return $http.post(api.placardsCounting, data)
}
/*
  标识标牌-总览列表
*/
export function breastplateList (data) {
  return $http.post(api.breastplateList, data)
}
