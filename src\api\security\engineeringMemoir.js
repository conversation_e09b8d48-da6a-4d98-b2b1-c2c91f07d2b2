/* 安全管理-工程大事记 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  list: apiUrl.defaultUrl + '/project-memoir/list',
  summary: apiUrl.defaultUrl + '/project-memoir/getMemoirTitle',
  save: apiUrl.defaultUrl + '/project-memoir/add',
  edit: apiUrl.defaultUrl + '/project-memoir/update',
  deleteById: apiUrl.defaultUrl + '/project-memoir/deleteMemoirById',
  exports: apiUrl.defaultUrl + '/project-memoir/exportExcelForMemoir',
  imports: apiUrl.defaultUrl + '/project-memoir/importExcelForMemoir'
}

/**
 * 工程大事记-列表
 * "pageSize": "number",
 * "pageNum": "number"
 * "memoirName":"特大洪水事件",
 * "startTime":"2023-07-01",
 * "endTime":"2023-09-01",
 * "type":2,
 * "areaCode":"4405",
 */
export function list (data) {
  return $http.post(api.list, data)
}

/**
 * 工程大事记-统计
 * "projectType": number,
 * "areaCode":"4405",
 */
export function summary (data) {
  return $http.get(api.summary, data)
}


/**
 * 工程大事记-保存
 */
export function save (data) {
  return $http.post(api.save, data)
}

/**
 * 工程大事记-编辑
 */
export function edit (data) {
  return $http.post(api.edit, data)
}

/**
 * 工程大事记-删除
 */
export function deleteById (data) {
  return $http.get(api.deleteById, data)
}

/**
 * 工程大事记-导出
 */
export function exportsList (data) {
  return $http.postDownLoad(api.exports, data, "", "arraybuffer")
}

/**
 * 工程大事记-导入
 */
export function importList (data) {
  return $http.postUpLoadFile(api.imports, data)
}
