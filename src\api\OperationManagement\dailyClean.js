/* 运行管护-日常保洁 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  list: apiUrl.defaultUrl + "/cleaning/pageList",
  deleteById: apiUrl.defaultUrl + "/cleaning/del",
  details: apiUrl.defaultUrl + "/cleaning/findClearningById",
  save: apiUrl.defaultUrl + "/cleaning/add",
  edit: apiUrl.defaultUrl + "/cleaning/editClearning",
  curMonthNotWorkList: apiUrl.defaultUrl + "/cleaning/curMonthNotWorkList",
  overviewNotWorkList: apiUrl.defaultUrl + "/cleaning/overviewNotWorkList",
};
/**
 * 日常保洁-列表记录查询
 * "pageNum": number
 * "pageSize": number
 * "dailyUserName": string
 * "dailyStartTime": string
 * "dailyEndTime": string

 */
export function list(data) {
  return $http.post(api.list, data);
}

/**
 * 日常保洁-删除
 * id
 */
export function deleteById(data) {
  return $http.get(api.deleteById, data);
}

/**
 * 日常保洁-查看详情
 * "id": 0,
 */
export function details(params) {
  return $http.get(api.details, params);
}

/**
 * 日常保洁-保存
 * "dailyUserName":"string"
 * "dailyStartTime":"string"
 * "dailyEndTime":"string"
 * "areas":[
 *    {
 *      "dailyArea":"string"
 *      "areaSmalls":[
 *        {
 *          "name":"string"
 *         }
 *       ],
 *       "attachments":[
 *          {"id":1},
 *        ]
 *       }]
 */
export function save(data) {
  return $http.post(api.save, data);
}

/**
 * 日常保洁 - 编辑
 * @param {*} data
 * @returns
 */
export function edit(data) {
  return $http.post(api.edit, data);
}
/*
日常保洁-总览列表
*/
export function curMonthNotWorkList(data) {
  return $http.post(api.curMonthNotWorkList, data);
}
/*
日常保洁-省市账号-本月未护理工程列表
*/
export function overviewNotWorkList(data) {
  return $http.post(api.overviewNotWorkList, data);
}
