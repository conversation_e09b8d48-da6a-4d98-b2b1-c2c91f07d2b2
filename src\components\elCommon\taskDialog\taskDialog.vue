<template>
  <!-- 封装弹框 -->
  <el-dialog
    :top="dialogTop"
    class="std-theme c-el-dialog-line"
    :append-to-body="true"
    :title="dialogTitle"
    :modal="modal"
    :visible.sync="dialogVisible"
    :width="popupWidth"
    :before-close="handleClose"
  >
    <dialog-title-icon
      slot="title"
      :title="dialogTitle"
      :iconUrl="!titleIcon ? imgIcon : ''"
      :elIcon="titleIcon"
    ></dialog-title-icon>
    <slot>
      <p>弹框自定义的内容</p>
    </slot>
    <div v-if="customeBtn && showBtn" slot="footer" class="dialog-footer">
      <el-button
        v-for="(btn, btnKey) in btns"
        :key="btnKey"
        :type="btn.type && btn.type !== '' ? btn.type : 'primary'"
        :icon="btn.icon ? btn.icon : ''"
        @click="btn.clickEvent(btn.text)"
        >{{ btn.text }}</el-button
      >
    </div>
    <dialog-footer-btn
      v-if="!customeBtn && showBtn"
      slot="footer"
      @cancelClick="Cancel"
      @confirmClick="Save"
    ></dialog-footer-btn>
  </el-dialog>
</template>

<script>
import DialogTitleIcon from "@/components/elCommon/DialogTitleIcon.vue";
import DialogFooterBtn from "@/components/elCommon/DialogFooterBtn.vue";
export default {
  components: {
    DialogTitleIcon,
    DialogFooterBtn,
  },
  props: {
    dialogTitle: {
      type: String,
      default: "标题",
    },
    modal: {
      type: Boolean,
      default: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    popupWidth: {
      type: String,
      default: "550px",
    },
    titleIcon: {
      type: String,
      default: "",
    },
    imgIcon: {
      type: String,
      default: "",
    },
    dialogTop: {
      type: String,
      default: "0",
    },
    customeBtn: {
      type: Boolean,
      default: false,
    },
    showBtn: {
      type: Boolean,
      default: true,
    },
    btns: {
      type: Array,
      default: () => [],
      require: true,
    },
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        // 当visible改变的时候，触发父组件的 updateVisible方法，在该方法中更改传入子组件的 centerDialogVisible的值
        this.$emit("updateVisible", val);
      },
    },
  },
  methods: {
    Cancel() {
      this.dialogVisible = false;
      this.$emit("resetPopupData");
    },
    Save() {
      this.$emit("submitPopupData");
    },
    handleClose() {
      this.dialogVisible = false;
      this.$emit("handleClose");
    },
  },
};
</script>

<style lang="scss" scoped></style>
