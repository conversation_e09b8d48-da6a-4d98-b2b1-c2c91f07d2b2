<template>
  <div class="them-header header">
    <!-- 系统名称 -->
    <h1 class="them-header-title">{{ systemTitle }}</h1>
    <!-- 用户设置 -->
    <div class="them-header-menu">
      <!-- 菜单 -->
      <div class="them-header-menu-container">
        <div
          :class="[
            'them-header-menu-item',
            activeId === val.meta.id ? 'active' : ''
          ]"
          v-for="val of menu"
          :key="val.meta.id"
          @click="navigationTo(val)"
        >
          <img
            class="theme-nav-icon"
            v-if="val.meta.icon"
            :src="require(`@/assets/${val.meta.icon}.png`)"
          />
          {{ val.meta.title }}
        </div>
      </div>

      <!-- 用户设置 -->
      <el-dropdown class="them-header-user" @command="handleCommand">
        <div class="them-header-dropdown-link">
          <span class="mr10 font-blod">{{ userInfo.username }}</span>
          <i class="el-icon-s-tools el-icon--right"></i>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="toMatrixPage">矩阵平台</el-dropdown-item>
          <el-dropdown-item command="showPasswordBox">修改密码</el-dropdown-item>
          <el-dropdown-item command="logout">退出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <task-dialog
      dialogTop="0vh"
      popup-width="650px"
      @updateVisible="cancel"
      @submitPopupData="save"
      :visible="UpdatePasswordVisible"
      dialog-title="修改密码"
    >
      <div class="form">
        <task-form
          ref="taskForm"
          :data="resetPasswordData"
          :options="resetPasswordObj"
          formType="EDIT"
        >
        </task-form>
      </div>
    </task-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from 'vuex'
import { getStorage } from '@/utils/storage'
import { resetPassword } from '@/api/user'
import TaskDialog from '@/components/elCommon/taskDialog/taskDialog'
import TaskForm from '@/components/elCommon/taskForm/taskForm'
export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: 'Header',
  components: {
    TaskDialog,
    TaskForm
  },
  data () {
    return {
      // 是否展开密码框
      UpdatePasswordVisible: false,
      activeId: 0,
      resetPasswordData: {},
      resetPasswordObj: {
        labelWidth: '100px',
        span: 24,
        fieldList: [
          {
            label: '新密码',
            value: 'newPass',
            type: 'el-input',
            attrs: { 'show-password': true }
          },
          {
            label: '确认密码',
            value: 'newPassAgain',
            type: 'el-input',
            attrs: { 'show-password': true }
          },
          {
            label: '旧密码',
            value: 'orginalPass',
            type: 'el-input',
            attrs: { 'show-password': true }
          }
        ],
        rules: {
          newPass: [
            { required: true, message: '请输入新密码', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value.length < 8) {
                  callback(new Error('新密码不得少于8位'))
                } else if (!(/^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$/).test(value)) {
                  callback(new Error('新密码必须同时包含数字及字母，不得包含其他字符，且不少于8位'))
                } else {
                  callback()
                }
              },
              trigger: 'blur'
            }
          ],
          newPassAgain: [
            { required: true, message: '请再输入新密码', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value !== this.resetPasswordData.newPass) {
                  callback(new Error('两次输入密码不一致!'))
                } else {
                  callback()
                }
              },
              trigger: 'blur'
            }
          ],
          orginalPass: [
            { required: true, message: '请输入旧密码', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value === this.resetPasswordData.newPass) {
                  callback(new Error('新旧密码不能一致!'))
                } else {
                  callback()
                }
              },
              trigger: 'blur'
            }
          ]
        },
        listTypeInfo: {}
      }
    }
  },
  computed: {
    ...mapState({
      menu: state => {
        // const projectInfo = JSON.stringify(state.project.projectInfo) === '{}' ? getStorage('std-projectInfo') : state.project.projectInfo
        const routes = Array.isArray(state.permission.routes) && state.permission.routes.length > 0 ? state.permission.routes : getStorage('std-routes')
        const children = routes[0] && Array.isArray(routes[0].children) ? routes[0].children : []
        const menuData = children.filter(val => {
          return val.meta.show
        })

        return menuData
      },
      userInfo: state => state.user.userInfo,
      systemTitle: state => {
        const t = state.user.title || getStorage('std-systemTitle') || '青山嘴水库矩阵管理系统'
        return t
      },
      title: state =>
        Array.isArray(state.permission.addRoutes)
          ? state.permission.addRoutes[0]
            ? state.permission.addRoutes[0].meta.title
            : ''
          : ''
    })
  },
  watch: {
    $route: {
      handler (val) {
        if (val) {
          this.activeId = val.meta.id
        }
      },
      deep: true,
      immediate: true
    },
    menu: {
      handler (val) {
        if (val) {
          let hasMenu = false
          for (const v of val) {
            if (v.meta.id === this.activeId) {
              hasMenu = true
              break
            }
          }
          if (!hasMenu) {
            this.$nextTick(() => {
              this.navigationTo(val[0])
            })
          }
        }
      },
      deep: true
    }
  },
  created () {
    const submenu = getStorage('std-submenu')
    const activeId = getStorage('std-activeId')
    if (activeId) {
      this.activeId = activeId
    }
    if (Array.isArray(submenu)) {
      this.submenuChange(submenu)
    }
  },
  mounted () {
    this.menuChange(this.menu)
  },
  methods: {
    ...mapMutations('menu', {
      menuChange: 'MENU_CHANGE',
      submenuChange: 'SUBMENU_CHANGE'
    }),
    ...mapActions('user', {
      userlogout: 'logout'
    }),
    showPasswordBox () {
      this.UpdatePasswordVisible = true
    },
    cancel () {
      this.UpdatePasswordVisible = false
      this.$refs.taskForm.$refs.form.resetFields()
    },
    save () {
      this.$refs.taskForm.$refs.form.validate(valid => {
        if (valid) {
          resetPassword(this.resetPasswordData)
            .then(res => {
              if (res.status === 200) {
                this.$message({
                  type: 'success',
                  message: '修改密码成功，请重新登陆',
                  duration: 1500,
                  onClose: () => {
                    this.logout()
                  }
                })
              }
            })
            .catch(e => {
              console.log(e)
              this.$message.error('修改密码失败，请稍后再试')
            })
        }
      })
    },
    handleCommand (command) {
      typeof this[command] === 'function' && this[command]()
    },
    navigationTo (item) {
      if (item.meta.isExternalUrl) {
        window.open(item.meta.path)
      } else {
        // this.activeId = item.meta.id
        // setStorage('std-activeId', item.meta.id)
        this.submenuChange(item.children || [])
        this.$router.push({
          path: item.path
        })
      }
    },
    // 退出登录，回到登录界面
    logout () {
      this.userlogout().then(() => {
        this.$router.push({
          path: '/login'
        })
      })
    },
    toMatrixPage () {
      let url = `${window.location.origin}/qsz/matrix/`
      if (process.env.NODE_ENV === 'development') {
        url = 'https://zhgs.digitwater.com:20000/qsz/matrix/'
      }
      window.open(url, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  ::v-deep .el-header {
    padding: 0;
  }
}

::v-deep .el-dropdown-menu__item {
  line-height: 50px;
}

.them-header {
  height: 102px;
  width: 100%;
  background-color: #045BD5;
}

// 头部标题
.them-header-title {
  height: 102px;
  line-height: 102px;
  font-size: 40px;
  color: #ffffff;
  background: url('@/assets/header/nav-title-bg.png') no-repeat;
  background-size: 100% 100%;
  padding: 0 166px 0 60px;
  font-size: 38px;
  letter-spacing: 3px;
}

// 头部按钮区域
.them-header-menu {
  height: 102px;
  width: 100%;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 头部按钮容器
.them-header-menu-container {
  display: flex;
}

// 头部按钮
.them-header-menu-item {
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('@/assets/header/nav-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  width: 168px;
  height: 50px;
  line-height: 50px;
  font-size: 22px;
  color: #ffffff;
  cursor: pointer;

  &.active {
    background-image: url('@/assets/header/nav-bg-active.png');
  }

  &:hover {
    background-image: url('@/assets/header/nav-bg-active.png');
  }
}

// 头部用户区域
.them-header-user {
  color: white;
  padding-right: 20px;
  cursor: pointer;
  .theme-header-dropdown-link {
    font-size: 26px;
  }
}

.them-header-dropdown-link {
  display: flex;
  align-items: center;
  font-size: 24px;
}
</style>
