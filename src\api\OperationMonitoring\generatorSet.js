import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getDzReal: apiUrl.defaultUrl + '/skmnt/getDzReal',
  getAllOptionList: apiUrl.defaultUrl + '/common/option/getAllOptionList',
  getDzHistory: apiUrl.defaultUrl + '/skmnt/getDzHistory',
  fdjExcel: apiUrl.defaultUrl + '/skmnt/fdjExcel',
  getGeneratingSetPage: apiUrl.defaultUrl + '/moreProjectMonitor/getProjectGeneratingSetPage',
  queryProjectTreeOfGeneratingSet: apiUrl.defaultUrl + '/moreProjectMonitor/queryProjectTreeOfGeneratingSet',
  getGeneratingSetProjectOverviewVo: apiUrl.defaultUrl + '/moreProjectMonitor/getGeneratingSetProjectOverviewVo',
}

/**
 * 运行监控-发电机组接口
 * @param data
 */
// 测站类型
export function getDzReal (data) {
  return $http.get(api.getDzReal, data)
}

export function getAllOptionList (data) {
  return $http.get(api.getAllOptionList, data)
}
export function getDzHistory (data) {
  return $http.post(api.getDzHistory + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}


export function fdjExcel (data,contentType, responseType) {
  return $http.postDownLoad(api.fdjExcel, data,  contentType, responseType)
}
//多工程运行监控-发电机组列表
export function getGeneratingSetPage (data) {
  return $http.post(api.getGeneratingSetPage, data)
}
//多工程运行监控-发电机组工程树 工程类型只有水库和水闸
export function queryProjectTreeOfGeneratingSet (data) {
  return $http.post(api.queryProjectTreeOfGeneratingSet, data)
}
//多工程发电机组监测总览--省市区用户
export function getGeneratingSetProjectOverviewVo (data) {
  return $http.post(api.getGeneratingSetProjectOverviewVo, data)
}
