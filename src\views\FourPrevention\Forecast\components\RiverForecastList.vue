<template>
  <div class="scroll-container" :style="{ overflowY: data.length > 0 ? 'auto' : 'hidden' }">
    <el-empty
      :image-size="117"
      v-show="data.length === 0"
      description="暂无数据"
      :image="EMPTY_NO_DATA"
    />
    <div
      class="river-forecast-item"
      :class="item.type === '河道站' ? '' : 'dm'"
      v-for="item in data"
      :key="item.sname"
      @click="
        () =>
          locateTo({
            lat: item.lttd,
            lon: item.lgtd,
            height: 5000
          })
      "
    >
      <div class="icon">
        <div class="img"></div>
        {{ item.type || '断面' }}
      </div>
      <div class="info">
        <div class="name">{{ item.sname }}</div>
        <div class="data">
          <div class="data-text">预报水位：{{ item.minZ || 0 }} - {{ item.maxZ || 0 }} m</div>
          <div class="data-text">警戒水位：{{ item.warningLevel || 0 }} m</div>
          <div class="data-text">
            预报流量：{{ item.minFlow || 0 }} - {{ item.maxFlow || 0 }} m³/s
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { StationWaterForecast } from '@/alova_api/type'
import { useMap3dStore } from '@/stores/modules/map3d'
import { EMPTY_NO_DATA } from '@/utils/constant/icon'

const { locateTo } = useMap3dStore()
defineProps({
  data: {
    type: Array as PropType<StationWaterForecast[]>,
    default: () => []
  }
})
</script>

<style lang="scss" scoped>
.river-forecast-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  border-bottom: 1px dashed #ffffff57;
  padding-bottom: 4px;
  box-sizing: border-box;
  color: #7ff;
  cursor: pointer;
  &.dm {
    color: #ffd060;
    .icon {
      border-color: #ffd06055;
    }
    .icon .img {
      background: url('@/assets/images/forecast/yb-item-2.png') no-repeat center;
      background-size: 100% 100%;
    }
  }
  .icon {
    width: 63px;
    height: 56px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-right: 11px;
    position: relative;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    border-radius: 2px;
    overflow: hidden;
    border: 1px solid #77ffff55;

    .img {
      width: 28px;
      height: 22px;
      background: url('@/assets/images/forecast/yb-item-1.png') no-repeat center;
      background-size: 100% 100%;
    }
    &::after {
      content: '';
      z-index: 0;
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: currentColor;
      opacity: 0.2;
    }
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    .name {
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 24px;
    }
    .data {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      .data-text {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        color: #fff;
        margin-right: 21px;
      }
    }
  }
}
</style>
