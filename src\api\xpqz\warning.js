import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  warningInfoList: apiUrl.defaultUrl + '/xpqz/yjgl/warnInfoList',
  warnInfoDetailList: apiUrl.defaultUrl + '/xpqz/yjgl/warnInfoDetailList',
  warnRuleList: apiUrl.defaultUrl + '/xpqz/yjgl/warnRuleList',
  getYjUsers: apiUrl.defaultUrl + '/xpqz/yjgl/getYjUsers',
  setWarnRule: apiUrl.defaultUrl + '/xpqz/yjgl/setWarnRule',
  warnNoticeList: apiUrl.defaultUrl + '/xpqz/yjgl/warnNoticeList',
  exceptionInfoList: apiUrl.defaultUrl + '/xpqz/yjgl/exceptionInfoList',
  exceptionRuleList: apiUrl.defaultUrl + '/xpqz/yjgl/exceptionRuleList',
  setExceptionRule: apiUrl.defaultUrl + '/xpqz/yjgl/setExceptionRule',
  exportWarnInfo: apiUrl.defaultUrl + '/xpqz/yjgl/exportWarnInfo',
  exportWarnNotice: apiUrl.defaultUrl + '/xpqz/yjgl/exportWarnNotice',
  exportWarnRule: apiUrl.defaultUrl + '/xpqz/yjgl/exportWarnRule',
  exportExceptionInfo: apiUrl.defaultUrl + '/xpqz/yjgl/exportExceptionInfo',
  exportExceptionRule: apiUrl.defaultUrl + '/xpqz/yjgl/exportExceptionRule'
}

/**
 * 预警信息列表接口
 * @param data
 */
export function warningInfoList (data) {
  return $http.post(api.warningInfoList, data)
}

/**
 * 预警信息详情列表接口
 * @param data
 */
export function warnInfoDetailList (data) {
  return $http.post(api.warnInfoDetailList, data)
}

/**
 * 预警规则列表接口
 * @param data
 */
export function warnRuleList (data) {
  return $http.post(api.warnRuleList, data)
}

/**
 * 预警通知人下拉框接口
 */
export function getYjUsers (params) {
  return $http.get(api.getYjUsers, params)
}

/**
 * 预警规则编辑接口
 * @param data
 */
export function setWarnRule (data) {
  return $http.post(api.setWarnRule, data)
}

/**
 * 预警规则编辑接口
 * @param data
 */
export function warnNoticeList (data) {
  return $http.post(api.warnNoticeList, data)
}

/**
 * 预警异常数据列表接口
 * @param data
 */
export function exceptionInfoList (data) {
  return $http.post(api.exceptionInfoList, data)
}

/**
 * 预警异常规则列表接口
 * @param data
 */
export function exceptionRuleList (data) {
  return $http.post(api.exceptionRuleList, data)
}

/**
 * 预警异常规则编辑接口
 * @param data
 */
export function setExceptionRule (data) {
  return $http.post(api.setExceptionRule, data)
}

/**
 * 预警信息导出接口
 * @param data
 */
export function exportWarnInfo (data) {
  return $http.postDownLoad(api.exportWarnInfo, data, '', 'arraybuffer')
}

/**
 * 预警通知导出接口
 * @param data
 */
export function exportWarnNotice (data) {
  return $http.postDownLoad(api.exportWarnNotice, data, '', 'arraybuffer')
}

/**
 * 预警规则导出接口
 * @param data
 */
export function exportWarnRule (data) {
  return $http.postDownLoad(api.exportWarnRule, data, '', 'arraybuffer')
}

/**
 * 预警异常数据导出接口
 * @param data
 */
export function exportExceptionInfo (data) {
  return $http.postDownLoad(api.exportExceptionInfo, data, '', 'arraybuffer')
}

/**
 * 预警异常规则导出接口
 * @param data
 */
export function exportExceptionRule (data) {
  return $http.postDownLoad(api.exportExceptionRule, data, '', 'arraybuffer')
}
