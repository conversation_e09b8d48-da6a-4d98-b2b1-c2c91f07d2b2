/* 基础管理-规章制度 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  details: apiUrl.defaultUrl + '/scoreRules/details',
  list: apiUrl.defaultUrl + '/scoreRules/list',
  save: apiUrl.defaultUrl + '/scoreRules/save',
  getRulesOverview: apiUrl.defaultUrl + '/moreProject/getRulesOverview',
}

/**
 * 基础管理-规章制度-详情
 * id
 */
export function details (params) {
  return $http.get(api.details, params)
}

/**
 * 基础管理-规章制度-分页查询
 * "areaCode": "string",
 * "projectName": "string",
 * "projectScale": 0,
 * "projectType": "string"
 *
 * "pageNum": 1
 * "pageSize": 10
 */
export function list (data, params) {
  return $http.postParams(api.list, data, params)
}

/**
 * 基础管理-规章制度-保存
 * "aqjczd": 0, // 安全监测制度
 * "aqjczdFileId": "string", // 安全监测制度-文件id
 * "aqsczd": 0, // 安全生产制度
 * "aqsczdFileId": "string", // 安全生产制度-文件id
 * "czyxzd": 0, // 操作运行制度
 * "czyxzdFileId": "string", // 操作运行制度-文件id
 * "daglzd": 0, // 档案管理制度
 * "daglzdFileId": "string", // 档案管理制度-文件id
 * "ddyxzd": 0, // 调度运行制度
 * "ddyxzdFileId": "string", // 调度运行制度-文件id
 * "dsjzd": 0, // 大事记制度
 * "dsjzdFileId": "string", // 大事记制度-文件id
 * "fxwzglzd": 0, // 防汛物资管理制度
 * "fxwzglzdFileId": "string", // 防汛物资管理制度-文件id
 * "fxzbzd": 0, // 防汛值班制度
 * "fxzbzdFileId": "string", // 防汛值班制度-文件id
 * "gwzrzd": 0, // 岗位责任制度
 * "gwzrzdFileId": "string", // 岗位责任制度-文件id
 * "hddwfzzd": 0, // 害堤动物防治制度
 * "hddwfzzdFileId": "string", // 害堤动物防治制度-文件id
 * "id": 0, // 主键
 * "jypxzd": 0, // 教育培训制度
 * "jypxzdFileId": "string", // 教育培训制度-文件id
 * "projectId": 0, // 工程id
 * "wlptaqglzd": 0, // 网络平台安全管理制度
 * "wlptaqglzdFileId": "string", // 网络平台安全管理制度-文件id
 * "wxyhzd": 0, // 维修养护制度
 * "wxyhzdFileId": "string", // 维修养护制度-文件id
 * "xsjczd": 0, // 巡视检查制度
 * "xsjczdFileId": "string", // 巡视检查制度-文件id
 * "yjqxjbgzd": 0, // 应急抢险及报告制度
 * "yjqxjbgzdFileId": "string" // 应急抢险及报告制度-文件id
 */
export function save (data) {
  return $http.post(api.save, data)
}
/*
  多工程规则制度管理总览--省市区用户
*/
export function getRulesOverview (data) {
  return $http.post(api.getRulesOverview, data)
}
