<template>
  <!-- 页面中间下方导航栏 -->
  <div class="factor-nav">
    <!-- 内容框 -->
    <div v-show="contentVisible" class="detail-content flx-column">
      <img
        class="close-btn"
        src="@/assets/images/fullFactor/close.png"
        alt="x"
        @click="contentVisible = false"
      />
      <div class="flx-1 detail-content-text" v-html="describe"></div>
      <!-- 下游按钮 -->
      <ul v-if="selectedNav === 'downstream'" class="flx-center gap3 mt8">
        <li
          class="tab-item flx-center"
          :class="{ active: selectedDsTabs.includes(item.key) }"
          v-for="item in downstreamTabs"
          :key="item.key"
          @click="handleDownstreamClick(item)"
        >
          <img
            v-show="!selectedDsTabs.includes(item.key)"
            class="tab-item-icon"
            :src="item.icon"
            alt=""
          />
          <img
            v-show="selectedDsTabs.includes(item.key)"
            class="tab-item-icon"
            :src="item.activeIcon"
            alt=""
          />
          <span class="tab-item-label">{{ item.label }}</span>
        </li>
      </ul>
      <!-- 工程按钮 -->
      <ul v-if="selectedNav === 'project'" class="flx-center gap3 mt8">
        <li
          class="tab-item flx-center"
          :class="{ active: selectedProjTab === item.label }"
          v-for="item in projectTabs"
          :key="item.key"
          @click="handleProjectClick(item)"
        >
          <img
            v-show="selectedProjTab !== item.label"
            class="tab-item-icon"
            :src="item.icon"
            alt=""
          />
          <img
            v-show="selectedProjTab === item.label"
            class="tab-item-icon"
            :src="item.activeIcon"
            alt=""
          />
          <span class="tab-item-label">{{ item.label }}</span>
        </li>
      </ul>
    </div>

    <!-- 工程描述 -->
    <div class="project-desc" v-if="projectDescVisible">
      <img
        class="close-btn"
        src="@/assets/images/fullFactor/close.png"
        alt="x"
        @click="projectDescVisible = false"
      />
      <h3 class="desc-title mb12">{{ selectedProjTab }}</h3>
      <template v-if="['主坝', '副坝'].includes(selectedProjTab)">
        <ul class="flx-align-center">
          <li class="desc-item flx-1">
            <div class="desc-item-label">{{ selectedProjTab }}坝型</div>
            <div class="desc-item-value">
              {{ formatDict('DAM_STRUCTURE_TYPE', projectData.damTypeStr) }}
            </div>
          </li>
          <li class="desc-item">
            <div class="desc-item-label">最大坝高</div>
            <div class="desc-item-value">{{ projectData.damMaxHeig }}m</div>
          </li>
          <li class="desc-item flx-1">
            <div class="desc-item-label">坝顶高程</div>
            <div class="desc-item-value">{{ projectData.damTopElev }}m</div>
          </li>
        </ul>
        <ul class="flx-align-center mt16">
          <li class="desc-item flx-1">
            <div class="desc-item-label">防浪墙高</div>
            <div class="desc-item-value">{{ projectData.wvwltPel }}m</div>
          </li>
          <li class="desc-item">
            <div class="desc-item-label">坝顶长</div>
            <div class="desc-item-value">{{ projectData.damTopLen }}m</div>
          </li>
          <li class="desc-item flx-1">
            <div class="desc-item-label">坝顶宽度</div>
            <div class="desc-item-value">{{ projectData.damTopWid }}m</div>
          </li>
        </ul>
      </template>
      <div class="desc-content" v-else-if="selectedProjTab === '溢洪道'">
        溢洪道布置于右岸垭口与副坝之间，为开敞式有闸控制，堰型为
        <span>{{ formatDict('WEIR_TYPE', projectData.ofwrst) }}</span>
        ，堰宽
        <span>{{ projectData.wrcrntwd }}</span>
        m，底板高程
        <span>{{ projectData.wrcrel }}</span>
        m，
        <span>{{ projectData.gtam }}</span>
        孔出流，设置
        <span>{{ projectData.gtam }}</span>
        道
        <span>{{ projectData.gtsz }}</span>
        <span>{{ formatDict('GATE_TYPE', projectData.gtst) }}</span>
        控制泄流量。溢洪道最大下泄流量
        <span>{{ projectData.maxds }}</span>
        m³/s(P=0.05%)。
      </div>
      <div v-else v-html="projectDesc"></div>
    </div>

    <!-- 导航 -->
    <ul class="flx-align-center gap12">
      <li
        class="nav-item flx-hc"
        :class="{ active: selectedNav === item.key }"
        v-for="item in navOptions"
        :key="item.key"
        @click="handleNavClick(item)"
      >
        <span class="nav-item-label">{{ item.label }}</span>
      </li>
    </ul>

    <base-dialog
      v-model:showDialog="forestDialogVisible"
      title="森林资源"
      align-center
      :append-to-body="true"
      :show-footer="false"
    >
      <el-table
        class="c-dialog-el-table flx-1"
        ref="forestTableRef"
        border
        highlight-current-row
        max-height="70vh"
        :data="forestResourceList"
      >
        <el-table-column prop="name" label="管户人" align="center"></el-table-column>
        <el-table-column prop="area" label="森林资源(亩)" align="center"></el-table-column>
        <el-table-column prop="protectDateStart" label="管护日期" align="center">
          <template #default="scope">
            {{ scope.row.protectDateStart }} ~ {{ scope.row.protectDateEnd }}
          </template>
        </el-table-column>
        <el-table-column prop="fileName" label="文件" align="center">
          <template #default="scope">
            <el-link type="primary" @click="handleFilePreview(scope.row.attachment)">
              {{ scope.row.fileName }}
            </el-link>
          </template>
        </el-table-column>
      </el-table>
    </base-dialog>

    <file-preview-dialog ref="filePreviewDialogRef"></file-preview-dialog>
  </div>
</template>
<script lang="ts" setup>
import DOMPurify from 'dompurify'
import { getAssetsImg } from '@/utils/index'
import { useMap3dStore } from '@/stores/modules/map3d'
import { markerConfig, getPopupHtml } from '@/hooks/useMapMarker'
import { useDictionary } from '@/stores/modules/dictionary'
import { getMatrixDisplayList } from '@/alova_api/methods/display'
import { getSubmergeArea, getForestResourceList } from '@/api/module/fullFactor'
import type { IForestResourceList } from '@/api/interface/fullFactor/index'
import FilePreviewDialog from '@/components/FilePreviewDialog/index.vue'

const props = withDefaults(defineProps<{ projectInfo: Record<string, any>[] }>(), {
  projectInfo: () => []
})

const emits = defineEmits(['setPoints', 'setPanoramaState', 'choosePanorama'])
const {
  pointHeight,
  addComPointLayer,
  removeLayer,
  addCustomGeojsonLayer,
  addLayer,
  createTransferMgr,
  removeTransferMgr
} = useMap3dStore()
const { formatDict, initDictionary } = useDictionary()
initDictionary(['DAM_STRUCTURE_TYPE', 'WEIR_TYPE', 'GATE_TYPE'])

const filePreviewDialogRef = useTemplateRef('filePreviewDialogRef')
const forestTableRef = useTemplateRef('forestTableRef')

const contentVisible = ref(false)
const describe = ref('')

const selectedNav = ref('')
const navOptions = reactive([
  {
    label: '上游',
    key: 'upstream',
    displayTitle: '四全-全要素-上游介绍',
    text: ''
  },
  {
    label: '下游',
    key: 'downstream',
    displayTitle: '四全-全要素-下游介绍',
    text: ''
  },
  {
    label: '库区',
    key: 'reservoir',
    displayTitle: '四全-全要素-库区介绍',
    text: ''
  },
  {
    label: '工程',
    key: 'project',
    displayTitle: '四全-全要素-工程介绍',
    text: ''
  },
  {
    label: '森林资源',
    key: 'forest'
  }
])

const selectedDsTabs = ref<string[]>([])
const downstreamTabs = [
  {
    label: '重点保护对象',
    key: 'keyProtectedObject',
    icon: getAssetsImg('fullFactor/icon-flag.png'),
    activeIcon: getAssetsImg('fullFactor/icon-flag-active.png'),
    marker: 'RESCUE_TEAM',
    data: []
  },
  {
    label: '安置区',
    key: 'relocationArea',
    icon: getAssetsImg('fullFactor/icon-settlement.png'),
    activeIcon: getAssetsImg('fullFactor/icon-settlement-active.png'),
    marker: 'SETTLEMENT',
    data: []
  },
  {
    label: '转移路线',
    key: 'evacuationRoute',
    icon: getAssetsImg('fullFactor/icon-line.png'),
    activeIcon: getAssetsImg('fullFactor/icon-line-active.png'),
    marker: '',
    data: []
  }
]

const selectedProjTab = ref('主坝')
const projectDesc = ref('')
const projectTabs = [
  {
    label: '主坝',
    key: 'dam',
    icon: getAssetsImg('fullFactor/icon-dam.png'),
    activeIcon: getAssetsImg('fullFactor/icon-dam-active.png'),
    panoramaName: '主坝'
  },
  {
    label: '副坝',
    key: 'auxDam',
    icon: getAssetsImg('fullFactor/icon-aux-dam.png'),
    activeIcon: getAssetsImg('fullFactor/icon-aux-dam-active.png'),
    panoramaName: '副坝'
  },
  {
    label: '溢洪道',
    key: 'spillway',
    icon: getAssetsImg('fullFactor/icon-spillway.png'),
    activeIcon: getAssetsImg('fullFactor/icon-spillway-active.png'),
    panoramaName: '副坝'
  },
  {
    label: '坝后电站',
    key: 'power',
    icon: getAssetsImg('fullFactor/icon-power.png'),
    activeIcon: getAssetsImg('fullFactor/icon-power-active.png'),
    panoramaName: '库区',
    displayTitle: '四全-全要素-坝后电站'
  }
]

const handleNavClick = async (row: (typeof navOptions)[0]) => {
  if (selectedNav.value === row.key) {
    if (row.key !== 'forest') {
      contentVisible.value = true
    }
    // selectedNav.value = ''
    // emits('setPanoramaState', false)
    return
  }
  projectDescVisible.value = false
  selectedProjTab.value = '主坝'
  selectedDsTabs.value = []
  removeLayer('keyProtectedObject')
  removeLayer('relocationArea')
  removeLayer('forest')
  removeLayer('forestDetail')
  removeTransferMgr(transferId)

  // 使用DOMPurify进行HTML内容安全处理
  describe.value = DOMPurify.sanitize(row.text || '')
  selectedNav.value = row.key
  emits('setPanoramaState', ['reservoir', 'project'].includes(row.key))
  if (row.key !== 'forest') {
    contentVisible.value = true
    if (!row.text && row.displayTitle) {
      loadMatrixList(row.displayTitle)
    }

    if (row.key === 'upstream') {
      emits('setPoints', 'RR')
    } else if (row.key === 'downstream') {
      const hasData = downstreamTabs.some(item => item.data.length)
      if (!hasData) {
        await loadSubmergeArea()
      }
      handleDownstreamClick(downstreamTabs[0])
    } else if (row.key === 'project') {
      handleProjectClick(projectTabs[0])
    }
  } else {
    contentVisible.value = false
    loadForestResourceList()
  }
}

// 下游
const transferId = 'evacuationRoute'
const handleDownstreamClick = (row: (typeof downstreamTabs)[0]) => {
  if (selectedDsTabs.value.includes(row.key)) {
    selectedDsTabs.value = selectedDsTabs.value.filter(item => item !== row.key)
    if (row.marker) {
      removeLayer(row.key)
    } else {
      removeTransferMgr(transferId)
    }
    return
  }
  selectedDsTabs.value.push(row.key)

  if (row.data.length && row.marker) {
    addComPointLayer({
      id: row.key,
      data: row.data,
      idAttr: 'name',
      posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
      symbol: {
        label: {
          text: '{name}',
          maxRange: 1250
        },
        billboard: {
          image: markerConfig[row.marker]
        }
      },
      zoom: {
        minDist: 20000
      }
    })
  } else {
    const originRow = downstreamTabs[0]
    const targetRow = downstreamTabs[1]
    if (!selectedDsTabs.value.includes(originRow.key)) {
      handleDownstreamClick(originRow)
    }
    if (!selectedDsTabs.value.includes(targetRow.key)) {
      handleDownstreamClick(targetRow)
    }
    const options = {
      id: transferId, //方案ID
      target: {
        data: targetRow.data, //目标点,，在人员转移业务中为安置点数据
        idAttr: 'id',
        posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
        symbol: {
          label: { text: '{name}' }, //标签类配置，详见点图层相关设置
          billboard: {
            image: markerConfig[targetRow.marker],
            width: 22,
            height: 22
          }
        }
      },
      origin: {
        //起点，在人员转移业务中为风险点数据
        data: originRow.data,
        idAttr: 'id',
        posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
        targetIdAttr: 'targetId', //关联目标点的字段
        symbol: {
          label: { text: '{name}' }, //标签类配置
          billboard: {
            image: markerConfig[originRow.marker],
            width: 22,
            height: 22
          }
          // conditionSetDef: function (data) {
          //   //条件设置，目前提供以下四种默认样式，也可以参照点图层接口自行配置
          //   if (data.level == '0') {
          //     return { type: 'origin_settle1' }
          //   }
          //   if (data.level == 'low') {
          //     return { type: 'transfer_low' }
          //   }
          //   if (data.level == 'mid') {
          //     return { type: 'transfer_mid' }
          //   }
          //   if (data.level == 'high') {
          //     return { type: 'transfer_high' }
          //   }
          // }
        }
      },
      transferInfo: {
        //转移信息设置
        symbol: {
          //当需要自定义转移路线时，在转移点信息中添加route字段，放置转移路线路线（geojson数据）
          deltaHeight: 1000, //当没有路线数据，采用转移示意线时，示意线的最高点
          //line:{//转移示意线样式设置，默认是箭头图片流动线
          //color:{r:0,g:255,b:153,a:1},//颜色
          //width:10.0,//线宽
          //speed:5;//流动速度
          //},
          info: {
            //转移信息设置
            //timeInterval:100,//信息位置移动的时间间隔
            label: { text: '{transInfo}' } //标签类配置，详见点图层相关设置
          }
        }
      }
    }
    createTransferMgr(options)
  }
}

// 工程
const projectDescVisible = ref(false)
const projectData = ref<Record<string, any>>({})
const handleProjectClick = (row: (typeof projectTabs)[0]) => {
  if (row.panoramaName) {
    emits('choosePanorama', row.panoramaName)
  }
  projectDescVisible.value = true
  if (['dam', 'auxDam', 'spillway'].includes(row.key)) {
    projectData.value = props.projectInfo.find(item => item.title === row.label)?.data || {}
  }
  if (row.displayTitle) {
    getMatrixDisplayList(row.displayTitle).then(res => {
      const data = res[0] || {}
      // 使用DOMPurify进行HTML内容安全处理
      projectDesc.value = DOMPurify.sanitize(data.displayContent || '')
    })
  }
  if (selectedProjTab.value === row.label) {
    return
  }
  selectedProjTab.value = row.label
}

// 描述信息
const loadMatrixList = (displayTitle: string) => {
  getMatrixDisplayList(displayTitle).then(res => {
    const data = res[0] || {}
    // 使用DOMPurify进行HTML内容安全处理
    describe.value = DOMPurify.sanitize(data.displayContent || '')

    for (const item of navOptions) {
      if (item.displayTitle === displayTitle) {
        item.text = data.displayContent || ''
        break
      }
    }
  })
}

// 下游点位信息
const loadSubmergeArea = () => {
  return getSubmergeArea('千年一遇').then(res => {
    const data = res.data?.[0]
    try {
      if (data) {
        for (const item of downstreamTabs) {
          if (data[item.key]) {
            item.data = JSON.parse(data[item.key])
            if (item.marker) {
              item.data.forEach(item => (item.h = pointHeight))
            }
          }
        }
      }
    } catch (error) {
      console.log('下游点位：', error)
    }
  })
}

const forestDialogVisible = ref(false)
const forestOptions = [
  // {
  //   label: '管户人',
  //   key: 'person'
  // },
  {
    label: '森林资源',
    key: 'area',
    unit: '亩'
  },
  // {
  //   label: '东边界',
  //   key: 'eastBoundary'
  // },
  // {
  //   label: '南边界',
  //   key: 'southBoundary'
  // },
  // {
  //   label: '西边界',
  //   key: 'westBoundary'
  // },
  // {
  //   label: '北边界',
  //   key: 'northBoundary'
  // },
  {
    label: '管护日期',
    key: 'protectDateStart'
  }
]
const getDetailHtml = (data: Record<string, any>) => {
  return forestOptions
    .map(item => {
      if (item.key === 'protectDateStart') {
        return `<div class="map-popup-item">
          <div class="item-label">${item.label}：</div>
          <div class="item-value">${data.protectDateStart} ~ ${data.protectDateEnd}</div>
        </div>`
      }
      return `<div class="map-popup-item">
        <div class="item-label">${item.label}：</div>
        <div class="item-value">${data[item.key] || ''} ${item.unit || ''}</div>
      </div>`
    })
    .join('')
}
const forestResourceList = ref<IForestResourceList[]>([])
const loadForestResourceList = async () => {
  if (!forestResourceList.value.length) {
    await getForestResourceList().then(res => {
      forestResourceList.value = (res.data?.list || []).map(i => {
        const fileName = i.attachment?.mc || ''
        return {
          ...i,
          fileName
        }
      })
    })
  }

  const features = forestResourceList.value.reduce<Record<string, any>>((acc, cur) => {
    const { id, geojson, protectDateStart, protectDateEnd, name } = cur
    if (geojson) {
      const geo = JSON.parse(geojson)
      if (Array.isArray(geo?.features)) {
        geo.features.forEach(item => {
          item.properties.protectDateStart = protectDateStart
          item.properties.protectDateEnd = protectDateEnd
          item.properties.person = name
          item.properties.id = id
          acc.push(item)
        })
      }
    }
    return acc
  }, [])

  if (features.length) {
    const geojson = {
      type: 'FeatureCollection',
      name: 'forestResource',
      features
    }

    const opt = {
      id: 'forest',
      data: geojson,
      order: 100,
      symbol: {
        enableFeatureLabel: true,
        featureLabel: {
          featureIdAttr: 'name',
          fillColor: { r: 255, g: 255, b: 255, a: 1 },
          outlineColor: { r: 0, g: 0, b: 0, a: 1 }, //字体描边颜色
          outlineWidth: 3, //描边宽度
          font: '14px', //字体设置
          showBackground: true, //是否显示背景颜色
          pixelOffset: [0, 0], //设置屏幕空间中距此标签原点的像素偏移量
          text: function (properties) {
            //设置标签内容的回调函数
            return properties.person
          },
          minRange: 5000, //最小可视距离
          maxRange: 200000000 //最大可视距离
        },

        polygon: {
          color: { r: 255, g: 204, b: 0, a: 0.5 },
          outlineColor: { r: 255, g: 204, b: 0, a: 1 },
          outlineWidth: 4,
          outline: true,
          fill: true,
          //minRange:2000.0,//最近可视距离
          //maxRange:5000000.0,//最远可视距离
          minLevel: 4, //最小地图显示层级
          maxLevel: 20, //最大地图显示层级
          zIndex: 101,
          // height: 1820, //设置整体高度,
          clampToGround: true //设置不要贴地
        }
      },
      zoom: true,
      zoomParams: {
        // keepAV: true,
        expand: { E: 0.04, W: 0.04, N: 0.04, S: 0.04 } //从东西南北4个方向往外扩展0.1度
      },
      mouseover: function (e) {
        const data = e.overlay.attr
        if (!data || !data.name) {
          return
        }
        const html = getPopupHtml(getDetailHtml(data), data.person || '', '', '', true)
        addLayer({
          id: 'forestDetail',
          data: [e.wgs84SurfacePosition],
          posAttr: { x: 'lng', y: 'lat', z: 'alt' },
          symbol: {
            html
          }
        })
      },
      mouseout: function () {
        removeLayer('forestDetail')
      },
      click: function (e) {
        const data = e.overlay.attr
        if (!data || !data.name) {
          return
        }

        forestDialogVisible.value = true
        nextTick(() => {
          const row = forestResourceList.value.find(item => item.id === data.id)
          if (row) {
            forestTableRef.value?.setCurrentRow(row)
          }
        })
      }
    }

    addCustomGeojsonLayer(opt)
  }
}

const handleFilePreview = (row: any) => {
  if (row) {
    filePreviewDialogRef.value?.preview(row, 0)
  }
}

onBeforeUnmount(() => {
  removeLayer('keyProtectedObject')
  removeLayer('relocationArea')
  removeLayer('forest')
  removeLayer('forestDetail')
  removeTransferMgr(transferId)
})
</script>
<style lang="scss" scoped>
.factor-nav {
  position: fixed;
  bottom: 24px;
  left: 50%;
  color: #fff;
  pointer-events: auto;
  transform: translateX(-50%);
}

.detail-content {
  position: fixed;
  bottom: 75px;
  left: 50%;
  box-sizing: border-box;
  width: 830px;
  height: 234px;
  padding: 36px 56px 40px;
  line-height: 23px;
  background: url('@/assets/images/fullFactor/section-bg.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100% 100%;
  transform: translateX(-50%);

  .close-btn {
    position: absolute;
    top: 5px;
    right: 32px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .detail-content-text {
    overflow: auto;
  }

  .tab-item {
    width: 145px;
    height: 44px;
    margin-bottom: -12px;
    cursor: pointer;
    background: url('@/assets/images/fullFactor/tab-bg.png');
    background-repeat: no-repeat;
    background-position: 0 0;
    background-size: 100% 100%;

    .tab-item-icon {
      width: 18px;
      height: 18px;
      margin-right: 5px;
    }

    &.active {
      .tab-item-label {
        color: transparent;
        background: linear-gradient(0deg, #ffd060, #ffeec5);
        background-clip: text;
      }
    }
  }
}

.nav-item {
  position: relative;
  align-items: flex-start;
  width: 109px;
  height: 32px;
  font-family: YouSheBiaoTiHei, sans-serif;
  font-size: 22px;
  cursor: pointer;
  background: url('@/assets/images/fullFactor/nav-bg.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100% 100%;

  &.active {
    background-image: url('@/assets/images/fullFactor/nav-bg-active.png');

    &::before {
      position: absolute;
      top: -23px;
      left: 50%;
      width: 26px;
      height: 20px;
      content: '';
      background: url('@/assets/images/fullFactor/nav-arrow.png');
      background-repeat: no-repeat;
      background-position: 0 0;
      background-size: 100% 100%;
      transform: translateX(-50%);
    }

    .nav-item-label {
      color: transparent;
      background: linear-gradient(0deg, #ffd060, #ffeec5);
      background-clip: text;
    }
  }
}

.project-desc {
  position: fixed;
  right: -100px;
  bottom: 408px;
  box-sizing: border-box;
  width: 384px;
  height: 222px;
  padding: 30px 20px;

  // line-height: 1;
  background: url('@/assets/images/fullFactor/desc-bg.png');
  background-repeat: no-repeat;
  background-position: 0 0;
  background-size: 100% 100%;

  .close-btn {
    position: absolute;
    top: -1px;
    right: -1px;
    width: 20px;
    height: 20px;
    cursor: pointer;
  }

  .desc-title {
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 24px;
    color: #7ff;
    text-align: center;
  }

  .desc-item:nth-of-type(2) {
    box-sizing: border-box;
    width: 30%;
    padding-left: 22px;
    border-right: 1px solid #62a4e6;
    border-left: 1px solid #62a4e6;
  }

  .desc-item:nth-of-type(3) {
    box-sizing: border-box;
    padding-left: 22px;
  }

  .desc-item-value {
    font-size: 16px;
    color: #7ff;
  }

  .desc-content span {
    color: #7ff;
  }
}
</style>
