import $dayjs from 'dayjs'
import useInitECharts from '@/hooks/useEcharts'
import { fontSize } from '@/utils/index'
import useStaion from '../useStaion'
import type { StationRecord } from '@/api/module/fourFull/fullWeather/index.d'

export const factorOptions = [
  {
    label: '水温',
    key: 'wtmp',
    unit: '℃'
  },
  {
    label: 'pH值',
    key: 'ph',
    unit: ''
  },
  {
    label: '电导率',
    key: 'cond',
    unit: 'μS/cm'
  },
  {
    label: '浑浊度',
    key: 'turb',
    unit: 'NTU'
  },
  {
    label: '溶解氧',
    key: 'dox',
    unit: 'mg/L'
  },
  {
    label: '高锰酸盐指数',
    key: 'codmn',
    unit: 'mg/L'
  },
  {
    label: '化学需氧量',
    key: 'codcr',
    unit: 'mg/L'
  },
  {
    label: '总氮',
    key: 'tn',
    unit: 'mg/L'
  },
  {
    label: '氨氮',
    key: 'nh3n',
    unit: 'mg/L'
  },
  {
    label: '亚硝酸氮',
    key: 'no2',
    unit: 'mg/L'
  },
  {
    label: '硝酸盐氮',
    key: 'no3',
    unit: 'mg/L'
  },
  {
    label: '总磷',
    key: 'tp',
    unit: 'mg/L'
  }
]

export default function useDrawChart(domRef: Ref<HTMLDivElement | null>) {
  const { drawChart } = useInitECharts()
  const { loadStaionRecord } = useStaion()
  const list = ref<StationRecord.ResItem[]>([])

  const initChart = (row: (typeof factorOptions)[0]) => {
    const gridLeft = fontSize(60)
    const gridRight = fontSize(50)
    const gridY = fontSize(40)
    const textFontSize = fontSize(12)

    const xData: string[] = []
    const seriesData: any[] = []

    for (const item of list.value) {
      xData.push(item.tm)
      seriesData.push(item[row.key])
    }

    const LABEL_COLOR = '#fff'
    const LINE_COLOR = '#ffffff33'
    const options = {
      textStyle: {
        color: LABEL_COLOR,
        fontSize: textFontSize
      },
      color: ['#FFD060', '#01FFFF'],
      grid: {
        bottom: gridY,
        top: gridY,
        left: gridLeft,
        right: gridRight
        // containLabel: true
      },
      legend: {
        top: fontSize(4),
        textStyle: {
          color: LABEL_COLOR
        }
      },
      tooltip: {
        trigger: 'axis',
        confine: true,
        valueFormatter: value => (value ?? '--') + 'm'
      },
      xAxis: {
        type: 'category',
        data: xData,
        axisLabel: {
          color: LABEL_COLOR,
          fontSize: textFontSize,
          rotate: 45,
          formatter: function (value) {
            return $dayjs(value)?.format('HH:mm')
          }
        },
        axisTick: {
          show: true,
          lineStyle: {
            color: LINE_COLOR
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: LINE_COLOR
          }
        }
      },
      yAxis: [
        {
          type: 'value',
          name: row.unit ? `${row.label}(${row.unit})` : row.label,
          nameTextStyle: {
            color: LABEL_COLOR
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: LINE_COLOR
            }
          },
          splitLine: { show: true, lineStyle: { color: LINE_COLOR, type: 'dashed' } },
          axisLabel: {
            color: LABEL_COLOR,
            fontSize: textFontSize
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: LINE_COLOR
            }
          },
          scale: true
        }
      ],
      series: [
        {
          name: row.label,
          data: seriesData,
          type: 'line',
          smooth: true,
          showSymbol: true,
          connectNulls: true
        }
      ],
      graphic: xData.length
        ? undefined
        : [
            {
              type: 'text',
              left: 'center',
              top: 'center',
              z: 100,
              style: {
                fill: '#fff',
                text: '暂无数据',
                font: `normal ${textFontSize}px SourceHanSansSC-Regular`
              }
            }
          ]
    }

    nextTick(() => {
      drawChart(domRef.value!, options)
    })
  }

  const loadData = async (stcd: string, startTm?: string, endTm?: string) => {
    const timeRange = {
      startTm: startTm || $dayjs().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
      endTm: endTm || $dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
    }
    const res = await loadStaionRecord({ sttp: 'WQ', stcd, ...timeRange })
    list.value = (res?.data || []).sort((a, b) => $dayjs(a.tm).valueOf() - $dayjs(b.tm).valueOf())
  }

  return { list, initChart, loadData }
}
