/* 运行监控-水雨情 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  areaTree: apiUrl.defaultUrl + '/waterRainMonitor/getAreaList',
  getExistsPjAreaList: apiUrl.defaultUrl + '/waterRainMonitor/getExistsPjAreaList',
  list: apiUrl.defaultUrl + '/waterRainMonitor/queryWaterRainList',
  detailById: apiUrl.defaultUrl + '/waterRainMonitor/queryPointList',
  collectById: apiUrl.defaultUrl + '/monitorCollect/collect',
  getPpHistory: apiUrl.defaultUrl + '/mnt/getPpHistory',
  getPpHistoryChart: apiUrl.defaultUrl + '/mnt/getPpHistoryChart',
  getRzHistory: apiUrl.defaultUrl + '/mnt/getRzHistory',
  getRzHistoryChart: apiUrl.defaultUrl + '/mnt/getRzHistoryChart',
  queryPointByArea: apiUrl.defaultUrl + '/waterRainMonitor/queryPointByArea',
  getPointList: apiUrl.defaultUrl + '/waterRainMonitor/queryPointListOfPanorama',
}

/**
 * 水雨情-区划列表查询
 */
export function areaTree(param) {
  return $http.get(api.areaTree, param)
}

/**
 * 水雨情-区划列表查询2
 */
export function getExistsPjAreaList(param) {
  return $http.get(api.getExistsPjAreaList, param)
}

/**
 * 水雨情-测点列表按区划查询
 * @param { areaCode: '', pointType: '' } data
 */
export function queryPointByArea(data) {
  return $http.post(api.queryPointByArea, data)
}

/**
 * 水雨情列表查询
 * "pageSize": "number",
 * "pageNum": "number"
 */
export function list(data) {
  return $http.post(api.list, data)
}
/**
 * 水雨情-收藏
 * collectType
 * projectId
 */
export function collectById(data) {
  return $http.post(api.collectById, data)
}

/**
 * 水雨情 测站列表查询
 */
export function detailById(data) {
  return $http.post(api.detailById, data)
}

/**
 * 水雨情-雨量历史查询接口
 */
export function getPpHistory(data, params) {
  return $http.postParams(api.getPpHistory, data, params)
}

/**
 * 水雨情-雨量历史图表查询接口
 */
export function getPpHistoryChart(data) {
  return $http.post(api.getPpHistoryChart, data)
}

/**
 * 水雨情-水位历史查询接口
 */
export function getRzHistory(data, params) {
  return $http.postParams(api.getRzHistory, data, params)
}

/**
 * 水雨情-水位历史图表查询接口
 */
export function getRzHistoryChart(data) {
  return $http.post(api.getRzHistoryChart, data)
}

/**
 * 水雨情-获取点位列表
 */
export function getPointList(data) {
  return $http.post(api.getPointList, data)
}
