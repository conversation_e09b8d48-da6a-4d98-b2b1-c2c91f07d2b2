import type { FRStation, FutureForecast } from '@/alova_api/type'
import { markerConfig } from '@/hooks/useMapMarker'

export const STATIC_WATER_LABELS: {
  title: string
  key: keyof FutureForecast
  unit: string
  color: string
}[] = [
  { title: '累计降雨', key: 'totalRainfall', unit: 'mm', color: '#0099FFFF' },
  { title: '最高水位', key: 'maxWaterLevel', unit: 'm', color: '#77FFFFFF' },
  { title: '总入库洪量', key: 'totalIn', unit: 'm³', color: '#42E75BFF' },
  { title: '总下泄流量', key: 'totalOut', unit: 'm³', color: '#FFD060FF' }
]

export const IMG_TYPE_TAB_LIST = [
  { title: '卫星云图', value: 0 },
  { title: '雷达图', value: 1 }
]

export const POWER_DATA_CONFIG = [
  { label: '当前库容(万方)', valueKey: 'currentKuRong' },
  { label: '纳雨能力(毫米)', valueKey: 'rainAbsorptionAbility' },
  { label: '汛限库容(万方)', valueKey: 'xunXianKuRong' }
]

// 图例
export const LEGEND_LIST = [
  {
    icon: markerConfig.RR,
    minDist: 32000,
    text: '水库',
    type: 'RR' as const,
    data: [] as FRStation[]
  },
  {
    icon: markerConfig.SECTION,
    minDist: 220000,
    text: '下游断面',
    type: 'SECTION' as const,
    data: [] as FRStation[]
  }
]

export const LEGEND_POP_CONFIG = {
  RR: {
    titleKey: 'stnm',
    configList: [
      { label: '预报水位：', key: 'z' },
      { label: '出库流量：', key: 'q' }
    ]
  },
  SECTION: {
    titleKey: 'stnm',
    configList: [
      { label: '预报水位：', key: 'z' },
      { label: '预报流量：', key: 'q' }
    ]
  }
}
