<template>
  <base-dialog
    v-model:showDialog="videoDialogVisible"
    append-to-body
    :show-footer="false"
    align-center
    top="0"
    title="履职情况"
    style="--el-text-color-regular: #fff"
    width="70vw"
  >
    <div class="content">
      <arrow-title class="mb10 mb10">大坝安全责任人</arrow-title>
      <desc-record
        :column-num="5"
        :data="fxzrrRecord"
        :field-list="zrrListField"
        label-width="5%"
      />
      <desc-record :column-num="2" :field-list="FX_PUBLICITY_FIELD_LIST" label-align="left">
        <template #picture="{ item }">
          <el-image
            fit="contain"
            class="img"
            :preview-src-list="[getDownLoadUrlById(pictureRecord[item.value])]"
            :initial-index="0"
            preview-teleported
            :src="getDownLoadUrlById(pictureRecord[item.value])"
          />
        </template>
      </desc-record>
      <arrow-title class="mt10">防汛责任人</arrow-title>
      <desc-record :column-num="5" :data="dbRecord" :field-list="zrrListField" label-width="5%" />
      <desc-record :column-num="2" :field-list="DB_PUBLICITY_FIELD_LIST" label-align="left">
        <template #picture="{ item }">
          <el-image
            fit="contain"
            class="img"
            :preview-src-list="[getDownLoadUrlById(pictureRecord[item.value])]"
            :initial-index="0"
            preview-teleported
            :src="getDownLoadUrlById(pictureRecord[item.value])"
          />
        </template>
      </desc-record>
    </div>
  </base-dialog>
</template>
<script lang="ts" setup>
import { getResponsibilityInfo } from '@/alova_api/methods/reservoir'
import { useRequest } from 'alova/client'
import { DB_PUBLICITY_FIELD_LIST, DETAIL_FIELD_LIST, FX_PUBLICITY_FIELD_LIST } from '../constant'
import { DescRecordItem } from '@/components/DescRecord/type'
import { getDownLoadUrlById } from '@/api/module/attachment'

const videoDialogVisible = defineModel<boolean>({
  default: false
})

const { data: responsibilityInfo } = useRequest(getResponsibilityInfo, {
  initialData: {},
  immediate: true
})

const pictureRecord = computed(() => {
  return {
    floodDamAttachmentId: responsibilityInfo.value?.floodDamAttachments?.[0]?.id,
    floodMediumAttachmentId: responsibilityInfo.value?.floodMediumAttachments?.[0]?.id,
    damSafelyAttachmentId: responsibilityInfo.value?.damSafelyAttachments?.[0]?.id,
    damMediumAttachmentId: responsibilityInfo.value?.damMediumAttachments?.[0]?.id
  }
})

const zrrListField = computed(() => {
  return DETAIL_FIELD_LIST.reduce<DescRecordItem[]>(
    (acc, cur, curIndex) => {
      for (let i = 0; i < 3; i++) {
        acc[i * DETAIL_FIELD_LIST.length + curIndex] = {
          ...cur,
          value: cur.value + i
        }
      }
      return acc
    },
    new Array(DETAIL_FIELD_LIST.length * 3).fill({})
  )
})

const fxzrrList = computed(() => {
  return (
    responsibilityInfo.value?.attWrpPers?.filter(
      item => item.pertp !== 'F' && !['A', 'B', 'C'].includes(item.pertp || '')
    ) || []
  ).sort((a, b) => {
    //pertp值按GDE排序，G在前，D次之，E最后
    const pertpOrder = {
      G: 1,
      D: 2,
      E: 3
    }
    return pertpOrder[a.pertp || ''] - pertpOrder[b.pertp || '']
  })
})

const fxzrrRecord = computed(() => {
  return fxzrrList.value.reduce<Record<string, any>>((acc, cur, curIndex) => {
    acc['pertp' + curIndex] = cur.pertp
    acc['pername' + curIndex] = cur.pername
    acc['post' + curIndex] = cur.post
    acc['unit' + curIndex] = cur.unit
    acc['tel' + curIndex] = cur.tel
    return acc
  }, {})
})

const dbList = computed(() => {
  return (
    responsibilityInfo.value?.attWrpPers?.filter(
      item => item.pertp !== 'F' && ['A', 'B', 'C'].includes(item.pertp || '')
    ) || []
  )
})

const dbRecord = computed(() => {
  return dbList.value.reduce<Record<string, any>>((acc, cur, curIndex) => {
    acc['pertp' + curIndex] = cur.pertp
    acc['pername' + curIndex] = cur.pername
    acc['post' + curIndex] = cur.post
    acc['unit' + curIndex] = cur.unit
    acc['tel' + curIndex] = cur.tel
    return acc
  }, {})
})
</script>

<style lang="scss" scoped>
.content {
  padding: 10px;
  height: 80vh;
  overflow-y: auto;
}
.img {
  // width: 500px;
  height: 300px;
}
</style>
