import $http from "@/utils/request";
import { withBaseUrl } from "@/utils/apiUrl";

export const getWaterQualityList = (params) => {
  return $http.post(withBaseUrl("/water-quality/pageList?pageNum=" + params.page || 1), params);
}

export const addWaterQualityList = (params) => {
  return $http.post(withBaseUrl("/water-quality/add"), params);
}

export const editWaterQualityList = (params) => {
  return $http.post(withBaseUrl("/water-quality/update"), params);
}

export const deleteWaterQualityList = (params) => {
  return $http.post(withBaseUrl("/water-quality/delete"), params);
}