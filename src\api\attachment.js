import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteFile: apiUrl.defaultUrl + '/common/attachment/del',
  getFileList: apiUrl.defaultUrl + '/common/attachment/list',
  getAllOptionMap: apiUrl.defaultUrl + '/common/option/getAllOptionMap',
  getAllOptionList: apiUrl.defaultUrl + '/common/option/getAllOptionList',
  getAllOptionTree: apiUrl.defaultUrl + '/common/option/getTreeOption',

}

export const downloadUrl =
  apiUrl.defaultUrl + '/common/attachment/downLoad'

export const uploadUrl = apiUrl.defaultUrl + '/common/attachment/upload'

export const uploadShowUrl = apiUrl.defaultUrl + '/gis/uploadGeom'

export const previewUrl =
  apiUrl.defaultUrl + '/common/attachment/show?path='

/**
 * 上传文件接口
 * @param data
 */
export function upload (data, fn) {
  return $http.postUpLoadFile(uploadUrl, data, false, fn)
}

export function uploadShp (data, fn) {
  return $http.postUpLoadFile(uploadShowUrl, data, false, fn, 60 * 1000)
}

/**
 * 下载文件接口
 */
export function downLoad (params, type) {
  return $http.getDownLoad(downloadUrl, params, undefined, type)
}

/**
 * 图片预览接口
 */
export function preview (params) {
  return $http.get(previewUrl, params)
}

/**
 * 删除文件接口
 */
export function deleteFile (data) {
  return $http.get(api.deleteFile, data)
}

/**
 * 下拉框集合接口
 */
export function getAllOptionMap (data) {
  return $http.get(api.getAllOptionMap, data)
}

/**
 * 指定类型下拉框列表接口
 * "type": string
 */
export function getAllOptionList (data) {
  return $http.get(api.getAllOptionList, data)
}
/**
 * 指定类型树型数据接口
 * "type": string
 */
export function getAllOptionTree (data) {
  return $http.get(api.getAllOptionTree, data)
}

/**
 * 查询附件接口
 * type: 传上传时的module值
 * name: 附件名称
 */
export function getFileList (data) {
  return $http.get(api.getFileList, data)
}

