/* 基础管理-基础信息（堤防） */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  // 工程状况
  queryEsPage: apiUrl.defaultUrl + '/dikeBasicInfo/queryEsPage',
  // 基本信息
  baseInfoList: apiUrl.defaultUrl + '/dikeBasicInfo/list',
  baseInfoSave: apiUrl.defaultUrl + '/dikeBasicInfo/save',
  baseInfoDeleteById: apiUrl.defaultUrl + '/dikeBasicInfo/deleteById',
  // 水文特征
  hydrologyList: apiUrl.defaultUrl + '/dikeWaterFeature/list',
  hydrologySave: apiUrl.defaultUrl + '/dikeWaterFeature/save',
  hydrologyDeleteById: apiUrl.defaultUrl + '/dikeWaterFeature/deleteById',
  // 工程效益
  benefitList: apiUrl.defaultUrl + '/dikeProjectBenefit/list',
  benefitSave: apiUrl.defaultUrl + '​​/dikeProjectBenefit​/save',
  benefitDeleteById: apiUrl.defaultUrl + '/dikeProjectBenefit/deleteById',
  // 穿堤建筑物
  structuresList: apiUrl.defaultUrl + '/dikePiercingStructures/list',
  structuresSave: apiUrl.defaultUrl + '/dikePiercingStructures/save',
  structuresDeleteById: apiUrl.defaultUrl + '/dikePiercingStructures/deleteById',
  // 管理情况
  managementList: apiUrl.defaultUrl + '/dikeManagementSituation/list',
  managementSave: apiUrl.defaultUrl + '/dikeManagementSituation/save',
  managementDeleteById: apiUrl.defaultUrl + '/dikeManagementSituation/deleteById',
  // 修改记录列表查询
  getBasicModifyRecord: apiUrl.defaultUrl + '/reservoirProjectData/getBasicModifyRecord'
}

/**
 * 基础信息-工程状况列表查询
 */
export function queryEsPage (data) {
  return $http.post(api.queryEsPage + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}

/**
 * 基础信息-基本信息列表
 */
export function baseInfoList (data) {
  return $http.post(api.baseInfoList, data)
}
/**
 * 基础信息-基本信息保存
 */
export function baseInfoSave (data) {
  return $http.post(api.baseInfoSave, data)
}
/**
 * 基础信息-基本信息删除
 */
export function baseInfoDeleteById (data) {
  return $http.post(api.baseInfoDeleteById, data)
}

/**
 * 基础信息-水文特征列表
 */
export function hydrologyList (data) {
  return $http.post(api.hydrologyList, data)
}
/**
 * 基础信息-水文特征保存
 */
export function hydrologySave (data) {
  return $http.post(api.hydrologySave, data)
}
/**
 * 基础信息-水文特征删除
 */
export function hydrologyDeleteById (data) {
  return $http.post(api.hydrologyDeleteById, data)
}

/**
 * 基础信息-工程效益列表
 */
export function benefitList (data) {
  return $http.post(api.benefitList, data)
}
/**
 * 基础信息-工程效益保存
 */
export function benefitSave (data) {
  return $http.post(api.benefitSave, data)
}
/**
 * 基础信息-工程效益删除
 */
export function benefitDeleteById (data) {
  return $http.post(api.benefitDeleteById, data)
}

/**
 * 基础信息-穿堤建筑物列表
 */
export function structuresList (data) {
  return $http.post(api.structuresList, data)
}
/**
 * 基础信息-穿堤建筑物保存
 */
export function structuresSave (data) {
  return $http.post(api.structuresSave, data)
}
/**
 * 基础信息-穿堤建筑物删除
 */
export function structuresDeleteById (data) {
  return $http.post(api.structuresDeleteById, data)
}

/**
 * 基础信息-管理情况列表
 */
export function managementList (data) {
  return $http.post(api.managementList, data)
}
/**
 * 基础信息-管理情况保存
 */
export function managementSave (data) {
  return $http.post(api.managementSave, data)
}
/**
 * 基础信息-管理情况删除
 */
export function managementDeleteById (data) {
  return $http.post(api.managementDeleteById, data)
}
/**
 * 基础信息-修改记录列表查询
 */
export function getBasicModifyRecord (params) {
  return $http.get(api.getBasicModifyRecord, params)
}
