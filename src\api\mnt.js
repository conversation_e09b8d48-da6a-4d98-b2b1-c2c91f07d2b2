import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getWarning: apiUrl.defaultUrl + '/mnt/getWarning',
  getWarningDetail: apiUrl.defaultUrl + '/mnt/getWarningDetail',
  getWaterLevelChart: apiUrl.defaultUrl + '/mnt/getWaterLevelChart',
  getBasinRainfall: apiUrl.defaultUrl + '/mnt/getBasinRainfall',
  getGateStatus: apiUrl.defaultUrl + '/mnt/getGateStatus',
  getRealTimeRain: apiUrl.defaultUrl + '/mnt/getRealTimeRain',
  getPpHistory: apiUrl.defaultUrl + '/mnt/getPpHistory',
  getPpHistoryChart: apiUrl.defaultUrl + '/mnt/getPpHistoryChart',
  getRealTimeWaterLevel: apiUrl.defaultUrl + '/mnt/getRealTimeWaterLevel',
  getRzHistory: apiUrl.defaultUrl + '/mnt/getRzHistory',
  getRzHistoryChart: apiUrl.defaultUrl + '/mnt/getRzHistoryChart',
  getDamList: apiUrl.defaultUrl + '/mnt/getDamList',
  getSafetyMonitoringList:
    apiUrl.defaultUrl + '/mnt/getSafetyMonitoringList',
  getDamSafetyClassify: apiUrl.defaultUrl + '/dsc/getDamSafetyClassify',
  getSafetyMonitoringChartData:
    apiUrl.defaultUrl + '/mnt/getSafetyMonitoringChartData',
  safetyMonitoringExport:
    apiUrl.defaultUrl + '/mnt/safetyMonitoringExport',
  gateMonitor: apiUrl.defaultUrl + '/mnt/gateMonitor',
  gateMonitorHistory: apiUrl.defaultUrl + '/mnt/gateMonitorHistory',
  monitorHistoryExport: apiUrl.defaultUrl + '/mnt/monitorHistoryExport',
  getPointList: apiUrl.defaultUrl + '/mnt/getPointList',
  getHotSpot: apiUrl.defaultUrl + '/mnt/getHotSpot',
  getSituationPage: apiUrl.defaultUrl + '/moreProjectMonitor/situationPage',
  getSituationChart: apiUrl.defaultUrl + '/moreProjectMonitor/situationChart',
}

/**
 * 总体态势-预警接口
 */
export function getWarning () {
  return $http.get(api.getWarning)
}

/**
 * 总体态势-预警隐患时长接口
 */
export function getWarningDetail (params) {
  return $http.postParams(api.getWarningDetail, undefined, params)
}

/**
 * 总体态势-水位图接口
 */
export function getWaterLevelChart (params) {
  return $http.postParams(api.getWaterLevelChart, undefined, params)
}

/**
 * 总体态势-流域降雨量接口
 */
export function getBasinRainfall (params) {
  return $http.postParams(api.getBasinRainfall, undefined, params)
}

/**
 * 总体态势-闸门状态接口
 */
export function getGateStatus (params) {
  return $http.get(api.getGateStatus, params)
}

/**
 * 水雨情-雨量实况接口
 */
export function getRealTimeRain (data) {
  return $http.post(api.getRealTimeRain, data)
}

/**
 * 水雨情-雨量历史查询接口
 */
export function getPpHistory (data, params) {
  return $http.postParams(api.getPpHistory, data, params)
}

/**
 * 水雨情-雨量历史图表查询接口
 */
export function getPpHistoryChart (data) {
  return $http.postParams(api.getPpHistoryChart, data, undefined)
}

/**
 * 水雨情-水位实况接口
 */
export function getRealTimeWaterLevel (params) {
  return $http.get(api.getRealTimeWaterLevel, params)
}

/**
 * 水雨情-水位历史查询接口
 */
export function getRzHistory (data, params) {
  return $http.postParams(api.getRzHistory, data, params)
}

/**
 * 水雨情-水位历史图表查询接口
 */
export function getRzHistoryChart (data) {
  return $http.postParams(api.getRzHistoryChart, data, undefined)
}

/**
 * 安全监测——获取监测设备接口
 */
export function getDamList () {
  return $http.get(api.getDamList)
}

/**
 * 安全监测-列表数据(分页)接口
 */
export function getSafetyMonitoringList (data, params) {
  return $http.postParams(api.getSafetyMonitoringList, data, params)
}

/**
 * 安全监测-获取大坝类型分类统计超过预警数量接口
 */
export function getDamSafetyClassify () {
  return $http.get(api.getDamSafetyClassify)
}

/**
 * 安全监测-图表数据接口
 */
export function getSafetyMonitoringChartData (data) {
  return $http.post(api.getSafetyMonitoringChartData, data)
}

/**
 * 安全监测-导出接口
 */
export function safetyMonitoringExport (params, responseType) {
  return $http.postDownLoad(
    api.safetyMonitoringExport,
    params,
    '',
    responseType
  )
}

/**
 * 闸门监视-图表数据接口
 */
export function gateMonitor () {
  return $http.get(api.gateMonitor)
}

/**
 * 闸门监视-历史数据查询接口
 */
export function gateMonitorHistory (data, params) {
  return $http.postParams(api.gateMonitorHistory, data, params)
}

/**
 * 闸门监视-导出接口
 */
export function monitorHistoryExport (params, responseType) {
  return $http.postDownLoad(api.monitorHistoryExport, params, '', responseType)
}

// 安全监测-测点下拉列表

export function getPointList (params) {
  return $http.get(api.getPointList, params)
}

/**
 * 全景图热点数据接口
 */
export function getHotSpot () {
  return $http.get(api.getHotSpot)
}
// 多工程运行监控-总体态势列表查询
export function getSituationPage (data) {
  return $http.post(api.getSituationPage, data)
}
//多工程运行监控-总体态势图表数据
export function getSituationChart (data) {
  return $http.post(api.getSituationChart, data)
}
