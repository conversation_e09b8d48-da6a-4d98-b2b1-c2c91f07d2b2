/* 报表-大坝安全 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  generatorSetList: apiUrl.defaultUrl + '/goc/getGeneratorSetForms',
  generatorSetLExport:
    apiUrl.defaultUrl + '/goc/generatorSetDownFormsExcel',
  getGeneratorSetCharts: apiUrl.defaultUrl + '/goc/getGeneratorSetCharts',
  queryProjectTreeOfGeneratingSet: apiUrl.defaultUrl + 'std/moreProjectMonitor/queryProjectTreeOfGeneratingSet'
}

/**
 * 发电机组列表查询
 */
export function generatorSetList (data) {
  return $http.get(api.generatorSetList, data)
}
// 发电机组 导出
export function generatorSetLExport (params) {
  return $http.getDownLoad(api.generatorSetLExport, params, undefined, 'blob')
}

// 发电机组报表统计图
export function getGeneratorSetCharts (params) {
  return $http.get(api.getGeneratorSetCharts, params)
}

// 获取有发电机组的工程列表
export function queryProjectTreeOfGeneratingSet (data) {
  return $http.post(api.queryProjectTreeOfGeneratingSet, data)
}
