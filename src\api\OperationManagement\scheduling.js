/* 运行管护-排班管理 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  queryDutyList: apiUrl.defaultUrl + "/operaDuty/queryDutyList",
  queryDutyView: apiUrl.defaultUrl + "/operaDuty/queryDutyView",
  deleteById: apiUrl.defaultUrl + "/operaDuty/deleteById",
  queryPerson: apiUrl.defaultUrl + "/operaDuty/queryPerson",
  exportPerson: apiUrl.defaultUrl + "/operaDuty/queryPersonExport",
  saveDutyPersonnel: apiUrl.defaultUrl + "/operaDuty/saveDutyPersonnel",
  querySettingList: apiUrl.defaultUrl + "/operaDuty/querySettingList",
  saveDutySetting: apiUrl.defaultUrl + "/operaDuty/saveDutySetting",
  saveDutyView: apiUrl.defaultUrl + "/operaDuty/saveDutyView",
  queryDutyViewList: apiUrl.defaultUrl + "/operaDuty/queryDutyViewList",

};

/**
 * 排班管理-值班总览-列表
 */
export function queryDutyList(data) {
  return $http.post(api.queryDutyList, data);
}

/**
 * 排班管理-值班总览-日历图
 */
export function queryDutyView(data) {
  return $http.post(api.queryDutyView, data);
}

/**
 * 排班管理-值班人员-删除值班人员
 * id
 */
export function deleteById(data) {
  return $http.post(api.deleteById, data);
}

/**
 * 排班管理-值班人员-分页查询
 * "pageNum": number,
 * "pageSize": number
 */
export function queryPerson(data) {
  return $http.post(
    `${api.queryPerson}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  );
}

/**
 * 排班管理-值班人员-导出
 * "pageNum": number,
 * "pageSize": number
 */
export function exportPerson(data, contentType, responseType) {
  return $http.postDownLoad(api.exportPerson, data, contentType, responseType, false, false)
}

/**
 * 排班管理-值班人员-保存值班人员信息
 */
export function saveDutyPersonnel(data) {
  return $http.post(api.saveDutyPersonnel, data);
}

/**
 * 排班管理-值班设置-值班设置查询
 */
export function querySettingList(data) {
  return $http.post(api.querySettingList, data);
}

/**
 * 排班管理-值班设置-保存值班设置
 */
export function saveDutySetting(data) {
  return $http.post(api.saveDutySetting, data);
}
/**
 * 排班管理-值班修改-保存值班设置
 */
export function saveDutyView(data) {
  return $http.post(api.saveDutyView, data);
}
/**
 * 排班管理-值班人员查询-保存值班设置
 */
export function queryDutyViewList(data) {
  return $http.post(api.queryDutyViewList, data);
}
