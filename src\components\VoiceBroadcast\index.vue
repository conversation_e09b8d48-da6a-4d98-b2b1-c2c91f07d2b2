<template>
  <!-- 大禹知水 -->
  <VoiceBroadcastContainer
    v-model="showVoicePrint"
    ref="voiceBroadcastContainerRef"
    :conversation="currentConversation"
    :processStatus="processStatus"
    :stopFocus="dialogVisible"
    @send="queryChat"
  />

  <!-- 文件预览弹窗 -->
  <file-preview-dialog ref="filePreviewDialogRef"></file-preview-dialog>

  <!-- 测站弹窗 -->
  <component
    v-model="dialogVisible"
    :is="comp[activeDialog]"
    :stcd="dialogStcd"
    :stnm="dialogStnm"
  />
</template>

<script lang="ts" setup>
import { useRouter, useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { isObject } from 'lodash-es'
import {
  queryChatMessages,
  queryChatSuggested,
  querySpeechSynthesis
} from '@/api/module/voiceBroadcast/index'
import { Params } from '@/api/module/voiceBroadcast/index.d'
import { useBroadcast } from '@/stores/modules/broadcast'
import useSocket from './useSocket'
import VoiceBroadcastContainer from './container.vue'

const FilePreviewDialog = defineAsyncComponent(() => {
  return import('@/components/FilePreviewDialog/index.vue')
})
const DisplacementDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/displacement/detailDialog.vue')
})
const OutflowDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/outflow/detailDialog.vue')
})
const SeepageFlowDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/seepageFlow/detailDialog.vue')
})
const SeepagePressureDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/seepagePressure/detailDialog.vue')
})
const VideoDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/summaryInfo/videoDialog.vue')
})
const RainDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/waterAndRain/detailDialog/rainDialog.vue')
})
const WaterDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/waterAndRain/detailDialog/waterDialog.vue')
})
const WaterQualityDialog = defineAsyncComponent(() => {
  return import('@/views/FourFull/FullWeather/waterQuality/detailDialog.vue')
})

const router = useRouter()
const route = useRoute()
const broadcast = useBroadcast()
const { showVoicePrint } = storeToRefs(broadcast)
const { setShowVoicePrint, analyzePreviewParameters } = broadcast
const { initSocket, sendSocketMsg } = useSocket()

const voiceBroadcastContainerRef = useTemplateRef('voiceBroadcastContainerRef')
const filePreviewDialogRef = useTemplateRef('filePreviewDialogRef')

// 测站弹窗相关
const comp = {
  RR: markRaw(WaterDialog),
  PP: markRaw(RainDialog),
  GNSS: markRaw(DisplacementDialog),
  WLL: markRaw(SeepagePressureDialog),
  LKG: markRaw(SeepageFlowDialog),
  ZZ: markRaw(OutflowDialog),
  SP: markRaw(VideoDialog),
  WQ: markRaw(WaterQualityDialog)
}
const activeDialog = ref('RR')
const dialogVisible = ref(false)
const dialogStcd = ref('')
const dialogStnm = ref('')

// ai回答进度（todo：后续放进conversationList）
const processStatus = ref<'pending' | 'succeeded' | 'failed' | 'waiting'>('pending')

// 对话记录
type Conversation = {
  question: string
  answer: string
  intrestQuestions: string[]
}
const conversationList = ref<Conversation[]>([])

let chatMethod: any = null
let suggestedMethod: any = null
let speechSynthesisMethod: any = null

// 对话id，多轮对话使用
let conversation_id = ''
let withConversationId = true

// 用来获取感兴趣的三个问题
let message_id = ''
// 是否把系统回复也作为问题传参，在命中率不高时，给前三个选项提示用户选择，并作为系统回复和用户回复一起给到ai
let withResponsive = false

// 相似度阈值，暂时以0.8为准，后续测试再确定具体值
const standardSimilarity = 0.8
// 实体相似度匹配结果
let entityAnswerArr: Record<string, any>[] = []
// 属性相似度匹配结果
let attributeAnswerArr: Record<string, any>[] = []

// 是否是四预场景预演
let isFourPrevention = false
let fourPreventionParamString = ''

// 最新一条对话
const currentConversation = computed(() => {
  return (
    conversationList.value[conversationList.value.length - 1] || {
      question: '',
      answer: '',
      intrestQuestions: []
    }
  )
})

// 语音
let audioText = ''
let audio: HTMLAudioElement | null = null
let audioArr: any[] = []
const removeAllAudio = () => {
  audioArr.forEach(item => {
    item.pause()
    item.remove()
  })
  audioArr = []
}
const clearAudio = () => {
  audio?.pause()
  audio?.remove()
  audio = null
  audioArr.splice(0, 1)
  if (audioArr.length) {
    audio = audioArr[0]
    audio?.play()
  }
}
const handleSpeechSynthesis = async (text: string) => {
  if (!text) {
    return
  }

  const uselessTextArr = [
    '基于数据库查询结果',
    '基于知识库检索结果',
    '基于联网查询结果',
    '基于大模型本身知识'
  ]
  for (const item of uselessTextArr) {
    text = text.replace(item, '')
  }

  speechSynthesisMethod = querySpeechSynthesis({ text })
  await speechSynthesisMethod
    .query()
    .then(res => {
      if (res?.result?.save_path) {
        const ad = new Audio(res.result.save_path)
        ad.onended = clearAudio

        audioArr.push(ad)
        if (audio === null) {
          audio = audioArr[0]
          audio?.load()
          audio?.play()
        }
      }
    })
    .finally(() => {
      speechSynthesisMethod = null
    })
}

// 交互方法
enum OperationEnum {
  '测站' = 'OS',
  '菜单' = 'MENU',
  '文件' = 'FILE'
}
const OperationReverseMap = Object.entries(OperationEnum).reduce(
  (acc, [key, value]) => ({ ...acc, [value]: key }),
  {}
)
const orderOperation = (answerArr: Record<string, any>[]) => {
  console.log('相似度匹配', answerArr)
  const lastConversation = conversationList.value[conversationList.value.length - 1]
  try {
    const data = answerArr[0]

    if (data.similarity > standardSimilarity) {
      switch (data.type) {
        case OperationEnum['菜单']:
          router.push({
            name: data.menuId
          })
          lastConversation.answer = '正在执行跳转操作'
          setTimeout(() => {
            lastConversation.answer = `已跳转到${data.stnm}`
          }, 1000)
          break
        case OperationEnum['文件']:
          {
            const fileList = answerArr
              .filter(
                item => item.type === OperationEnum['文件'] && item.similarity > standardSimilarity
              )
              .slice(0, 3)
              .map(item => {
                return {
                  id: item.attachmentId,
                  zwdlx: item.zwdlx,
                  mc: item.stnm,
                  file_path: item.filePath
                }
              })
            filePreviewDialogRef.value?.previewFileList(fileList, 0)
            lastConversation.answer = '已打开文件'
          }
          break
        case OperationEnum['测站']:
          activeDialog.value = data.sttp
          dialogStcd.value = data.stcd
          dialogStnm.value = data.stnm
          dialogVisible.value = true
          lastConversation.answer = '已打开测站'
          break
      }
    } else {
      processStatus.value = 'waiting'
      withResponsive = true
      lastConversation.intrestQuestions = answerArr.slice(0, 3).map(item => {
        return `打开${OperationReverseMap[item.type]}：${item.stnm}`
      })
      lastConversation.answer = '您想要进行的操作可能是'
    }
  } catch (error) {
    lastConversation.answer = '暂时没找到答案'
    console.error('指令解析失败', error)
  }
}

// 用来判断是否是交互式消息
function isInteraction(str) {
  try {
    const obj = JSON.parse(str)
    return isObject(obj) && obj['意图'] === '交互'
  } catch (e) {
    return false
  }
}
// 指令处理
const commandHandle = (data: Record<string, any>) => {
  switch (data.title) {
    case '实体相似度匹配':
      // todo 处理实体相似度匹配
      try {
        if (data.outputs?.body) {
          const res = JSON.parse(data.outputs.body)
          if (res && Array.isArray(res.data)) {
            entityAnswerArr = res.data
          }
        }
      } catch (error) {
        console.error('实体相似度匹配出错：', error)
      }
      break
    case '属性相似度匹配':
      try {
        if (data.outputs?.body) {
          const res = JSON.parse(data.outputs.body)
          if (res && Array.isArray(res.data)) {
            attributeAnswerArr = res.data
          }
        }
      } catch (error) {
        console.error('属性相似度匹配出错：', error)
      }
      break
    case '回复':
      {
        console.log('指令处理', data)
        if (data?.outputs?.answer) {
          if (isInteraction(data.outputs.answer)) {
            return
          }
        }
        querySuggested(message_id)
      }
      break
    case '四预场景操作':
      {
        const lastConversation = conversationList.value[conversationList.value.length - 1]
        try {
          // 未来24小时降雨300mm， 雨峰集中在未来第5个小时，上游来水900m³/s，开始进行预演
          const config = {
            实体: 'entity',
            雨量: 'rainfall',
            雨量时间: 'rainfallTimes',
            雨量时间类型: 'rainfallTimesType',
            雨峰: 'isRainPeak',
            雨峰时间: 'rainPeakTimes',
            雨峰时间类型: 'rainPeakTimesType',
            入库流量: 'inQ'
          }
          lastConversation.answer = '开始解析预演参数'
          let params = JSON.parse(fourPreventionParamString.replace('json', '').replace(/```/g, ''))
          params = Object.entries(params).reduce((acc, [key, value]) => {
            if (config[key]) {
              acc[config[key]] = value
            }
            return acc
          }, {})
          isFourPrevention = false
          analyzePreviewParameters(params).then(res => {
            if (res) {
              lastConversation.answer = '解析成功'
              if (route.name !== 'SimulationPreview') {
                lastConversation.answer = '解析成功, 正在跳转到预演页面'
                router.push({
                  name: 'SimulationPreview'
                })
              }
            } else {
              lastConversation.answer = '预演参数解析失败'
            }
          })
        } catch (error) {
          lastConversation.answer = '预演参数解析失败'
          isFourPrevention = false
          console.error('四预场景操作出错：', error)
        }
      }
      break
  }
}

// 监听大禹知水返回的消息
const onMessage = (res: Record<string, any>) => {
  if (typeof res === 'string') {
    return
  }
  if (!res) {
    processStatus.value = 'failed'
    // todo 接口返回空，提示失败
    return
  }

  if (conversationList.value.length === 0) {
    conversationList.value.push({
      question: '',
      answer: '',
      intrestQuestions: []
    })
  }

  const lastConversation = conversationList.value[conversationList.value.length - 1]

  const data: Record<string, any> = res.data || {}
  switch (res.event) {
    case 'workflow_started':
      conversation_id = res.conversation_id
      message_id = res.message_id
      break
    case 'workflow_finished':
      if (processStatus.value !== 'waiting') {
        processStatus.value = data.status === 'failed' ? 'failed' : 'succeeded'
      }

      if (entityAnswerArr.length || attributeAnswerArr.length) {
        // 属性相似度匹配优先级高于实体相似度匹配
        if (attributeAnswerArr.length) {
          orderOperation(attributeAnswerArr)
        } else {
          orderOperation(entityAnswerArr)
        }
      }

      if (audioText) {
        handleSpeechSynthesis(audioText)
        audioText = ''
      }
      break
    case 'node_started':
      if (data.title === '提取“预演”场景参数') {
        fourPreventionParamString = ''
        isFourPrevention = true
      }
      break
    case 'node_finished':
      console.log('node_finished', res)
      commandHandle(data)
      break
    case 'message':
      if (isInteraction(res.answer)) {
        return
      }
      if (isFourPrevention) {
        fourPreventionParamString += res.answer
      } else {
        const text = typeof res.answer === 'string' ? res.answer.replace('\n\n', '\n') : res.answer
        audioText += text
        lastConversation.answer += text

        if (
          audioText !== '\n' &&
          typeof text === 'string' &&
          (text.includes('。') || text.includes('\n'))
        ) {
          handleSpeechSynthesis(audioText)
          audioText = ''
        }
      }
      break
    case 'message_replace':
      lastConversation.answer = res.answer
      break
    case 'error':
      console.error('查询失败：', res.message)
      break
  }

  voiceBroadcastContainerRef.value?.scrollToBottom()
}

// 流程打断事件
const bleakProcess = () => {
  processStatus.value = 'pending'
  // 清空相似度匹配结果
  entityAnswerArr = []
  attributeAnswerArr = []
  // 取消上一次的建议问题列表请求
  suggestedMethod?.cancel()
  suggestedMethod = null
  // 如果上一轮请求还没结束，又开始了新的请求，就取消上一轮请求
  chatMethod?.cancel()
  chatMethod = null
  // 停止语音播报
  speechSynthesisMethod?.cancel()
  speechSynthesisMethod = null
  audioText = ''
  removeAllAudio()
  clearAudio()
}

// 语音设备通过后端调用大禹知水接口的返回结果
const onSocketMessage = (res: Record<string, any>) => {
  if (res['voiceText']) {
    bleakProcess()
    setShowVoicePrint(true)
    conversationList.value.push({
      question: res['voiceText'],
      answer: '',
      intrestQuestions: []
    })
  } else if (res['chat-message']) {
    try {
      const data = JSON.parse(res['chat-message'])
      onMessage(data)
    } catch (error) {
      console.error('onSocketMessage', error)
    }
  }
}

// 向大禹知水发生请求
const queryChat = async (query: string) => {
  bleakProcess()

  // 如果用户选择了建议问题列表的序号，转换一下
  const intrestQuestions = currentConversation.value.intrestQuestions
  if (intrestQuestions.length && [1, 2, 3].includes(Number(query))) {
    query = intrestQuestions[Number(query) - 1]
    Object.values(OperationReverseMap).forEach(i => {
      query = query.replace(`打开${i}：`, '')
    })
  }

  conversationList.value.push({
    question: query,
    answer: '',
    intrestQuestions: []
  })

  let params: Params = { query }
  if (withConversationId) {
    params.conversation_id = conversation_id
  }
  withConversationId = true

  if (withResponsive) {
    // 在ai给出了可能的猜测让用户选择时，则将其作为用户输入的一部分
    params = {
      conversation_id,
      query: `系统回复：${JSON.stringify(intrestQuestions)}  \n用户输入：${query}`
    }
    withResponsive = false
  }

  chatMethod = await queryChatMessages(params, {
    onMessage,
    onError: () => {
      chatMethod = null
      processStatus.value = 'failed'
    },
    onComplete: () => {
      chatMethod = null
    }
  })
}

// 根据当前问的问题给出用户可能感兴趣的问题
const querySuggested = (message_id: string) => {
  suggestedMethod = queryChatSuggested(message_id)

  suggestedMethod
    .query()
    .then(res => {
      if (processStatus.value === 'pending') {
        // todo 检查用processStatus判断是否合适
        return
      }
      if (res && Array.isArray(res.data) && res.data.length) {
        const lastConversation = conversationList.value[conversationList.value.length - 1]
        lastConversation.answer += '\n#### 您可能感兴趣的问题：'
        lastConversation.intrestQuestions = res.data
        voiceBroadcastContainerRef.value?.scrollToBottom()
      }
    })
    .finally(() => {
      suggestedMethod = null
    })
}

// 监听语音模型开启关闭
watch(showVoicePrint, val => {
  if (!val) {
    conversation_id = ''
    message_id = ''
    conversationList.value = []
    bleakProcess()
  }
})

onMounted(() => {
  initSocket(onSocketMessage)
})
</script>
<style lang="scss" scoped></style>
