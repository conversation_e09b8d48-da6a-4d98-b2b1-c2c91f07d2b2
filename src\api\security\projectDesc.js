/* 安全管理-工程划界 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  list: apiUrl.defaultUrl + '/demara/list',
  detailById: apiUrl.defaultUrl + '/demara/findDemaraById',
  save: apiUrl.defaultUrl + '/demara/editDemara',
  charts: apiUrl.defaultUrl + '/demara/charts',
  overviewList: apiUrl.defaultUrl + '/demara/overviewList'
}

/**
 * 工程划界-详情
 * id
 */
export function detailById (params) {
  return $http.get(api.detailById, params)
}

/**
 * 工程划界列表查询
 * "pageSize": "number",
 * "pageNum": "number"
 */
export function list (data) {
  return $http.post(api.list, data)
}

/**
 * 工程划界-保存
 */
export function save (data) {
  return $http.post(api.save, data)
}

/**
 * 图表统计
 * id
 */
export function charts (data) {
  return $http.post(api.charts, data)
}

/**
 * 工程划界-省市账号-按区划统计总览列表
 */
export function overviewList (data) {
  return $http.post(api.overviewList, data)
}
