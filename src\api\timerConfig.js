import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  projectList: apiUrl.defaultUrl + '/scheduledTask/queryProjectList',
  taskList: apiUrl.defaultUrl + '/scheduledTask/queryTaskList',
  saveTask: apiUrl.defaultUrl + '/scheduledTask/save',
  deleteTask: apiUrl.defaultUrl + '/scheduledTask/deleteById',
  updateStatus: apiUrl.defaultUrl + '/scheduledTask/trigger',
  updateAllStatus: apiUrl.defaultUrl + '/scheduledTask/triggerByProjectId',
  onlineCheck: apiUrl.defaultUrl + '/scheduledTask/health',
  initTask: apiUrl.defaultUrl + '/scheduledTask/initialValues'
}

/**
 * 定时器配置 - 工程列表
 * @param data
 */
export function projectList (data) {
  return $http.post(api.projectList, data)
}

/**
 * 定时器配置 - 任务列表
 * @param data
 */
export function taskList (data) {
  return $http.post(api.taskList, data)
}

/**
 * 定时器配置 - 保存任务
 */
export function saveTask (data) {
  return $http.post(api.saveTask, data)
}

/**
 * 定时器配置 - 删除任务
 */
export function deleteTask (data) {
  return $http.post(api.deleteTask, data)
}

/**
 * 是否启用 - 工程
 * "project":"number"
 * "jobStatus":"number"
 */
export function updateAllStatus (data) {
  return $http.post(api.updateAllStatus, data)
}

/**
 * 是否启用 - 任务
 */
export function updateStatus (data) {
  return $http.post(api.updateStatus, data)
}

/**
 * 定时器配置 - 定时服务在线检查
 */
export function onlineCheck (data) {
  return $http.get(api.onlineCheck, data)
}

/**
 * 定时器配置 - 初始化参数查询
 */
export function initTask (params) {
  return $http.get(api.initTask, params)
}
