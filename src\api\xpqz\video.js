import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  videoDir: apiUrl.defaultUrl + '/xpqz/video/dir',
  videoPasList: apiUrl.defaultUrl + '/xpqz/video/pasList',
  getSPZList: apiUrl.defaultUrl + '/xpqzapi/spz/getSPZList'
}

export const videoUrl = ''

/**
 * 权限树状结构接口
 * @param data
 */
export function videoDir (data) {
  return $http.get(api.videoDir, data)
}

/**
 * 获取摄像头登录密码接口
 */
export function videoPasList (data) {
  return $http.get(api.videoPasList, data)
}

/**
 *
 */
export function getSPZList (data) {
  return $http.getNoAut(api.getSPZList, data)
}
