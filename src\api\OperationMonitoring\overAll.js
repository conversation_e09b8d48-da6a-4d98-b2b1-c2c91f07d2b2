/* 运行监控-总体态势 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getHotSpot: apiUrl.defaultUrl + '/skmnt/getHotSpot',
  getIndexRz: apiUrl.defaultUrl + '/skmnt/getIndexRz',
  getTodayQuantity: apiUrl.defaultUrl + '/skmnt/getTodayQuantity',
  getWaterStation: apiUrl.defaultUrl + '/skmnt/getWaterStation',
  getZmReal: apiUrl.defaultUrl + '/skmnt/getZmReal',
  lypp: apiUrl.defaultUrl + '/skmnt/lypp',
  getWaterForecast: apiUrl.defaultUrl + '/skmnt/getCurrentSkyb',
  getDamPiezometerView: apiUrl.defaultUrl + '/skmnt/getDamPiezometerView',
}

/**
 * 运行监控-总体态势（水库）-全景图热点列表
 * id
 */
export function getHotSpot(params) {
  return $http.get(api.getHotSpot, params)
}

/**
 * 运行监控-总体态势（水库）-水位
 * id
 */
export function getIndexRz(params) {
  return $http.get(api.getIndexRz, params)
}

/**
 * 运行监控-总体态势（水库）-综述统计 当天超预警的数量
 * id
 */
export function getTodayQuantity(params) {
  return $http.get(api.getTodayQuantity, params)
}

/**
 * 运行监控-总体态势（水库）-首页水位站点
 * id
 */
export function getWaterStation(params) {
  return $http.get(api.getWaterStation, params)
}

/**
 * 运行监控-总体态势（水库）-闸门实时数据
 * id
 */
export function getZmReal(data) {
  return $http.post(api.getZmReal, data)
}

/**
 * 运行监控-总体态势（水库）-流域降雨量
 * id
 */
export function lypp(data) {
  return $http.post(api.lypp, data)
}

/**
 * 运行监控-总体态势*（水库）-水库预报
 */
export function getWaterForecast(params) {
  return $http.get(api.getWaterForecast, params)
}

/**
 * 运行监控-总体态势（水库）- 渗压
 */
export function getDamPiezometerView(params) {
  return $http.get(api.getDamPiezometerView, params)
}

