{
  tasks: [
    // {
    //   name: "矩阵生产", // 任务名称
    //   workDir: "../../svn-qszskjz/std-web-matrix", // 工作目录
    //   buildCommand: "npm run build", // 打包命令
    //   distFolder: "dist", // 打包产物文件夹名
    //   outputName: "青山嘴展示端", // 压缩包名称（不含.zip）
    //   outputPath: "./build", // 输出目录，可选
    //   timeout: 300000 // 命令超时时间（毫秒）5分钟
    // },
    {
      "name": "管理端生产",           // 任务名称
      "workDir": "../../svn-qszskjz/std_web",         // 工作目录
      "buildCommand": "npm run build", // 打包命令
      "distFolder": "dist",            // 打包产物文件夹名
      "outputName": "青山嘴管理端",    // 压缩包名称（不含.zip）
      "outputPath": "./build",         // 输出目录，可选
      "timeout": 1200000                // 命令超时时间（毫秒） 二十分钟
    },
    // {
    //   "name": "矩阵测试",           // 任务名称
    //   "workDir": "../../svn-qszskjz/std-web-matrix",         // 工作目录
    //   "buildCommand": "npm run build:pre", // 打包命令
    //   "distFolder": "dist",            // 打包产物文件夹名
    //   "outputName": "青山嘴展示端",    // 压缩包名称（不含.zip）
    //   "outputPath": "./build",         // 输出目录，可选
    //   "timeout": 300000                // 命令超时时间（毫秒）5分钟
    // },

    // {
    //   "name": "h5生产",           // 任务名称
    //   "workDir": "../../svn-qszskjz/std_app",         // 工作目录
    //   "buildCommand": "npm run build:prod", // 打包命令
    //   "distFolder": "dist",            // 打包产物文件夹名
    //   "outputName": "青山嘴h5",    // 压缩包名称（不含.zip）
    //   "outputPath": "./build",         // 输出目录，可选
    //   "timeout": 300000                // 命令超时时间（毫秒）5分钟
    // }
  ],

  // 兼容旧版本的简单打包配置（可选）
  builds: [
    // {
    //   "name": "simple-pack",
    //   "sourcePath": "./some-folder",
    //   "outputPath": "./build"
    // }
  ]
}
