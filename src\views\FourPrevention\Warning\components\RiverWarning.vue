<template>
  <div class="river-warning-box">
    <ul class="river-list">
      <li
        class="river-item flx"
        v-for="item in isActual ? tableData : tableDataForecast"
        :key="item.stcd"
      >
        <div class="type-info flx-center" :class="[item.sttp === 'ZZ' ? 'green-bg' : 'orange-bg']">
          <template v-if="item.sttp === 'ZZ'">
            <img src="@/assets/images/warning/type-river.png" />
            <span>河道站</span>
          </template>
          <template v-if="item.sttp !== 'ZZ'">
            <img src="@/assets/images/warning/type-dm.png" />
            <span>断面</span>
          </template>
        </div>

        <div class="main-info">
          <div class="flx-align-center" style="margin-bottom: 4px">
            <div class="site-name" :style="{ color: item.sttp === 'ZZ' ? '#7ff' : '#ffd060' }">
              {{ item.stnm }}
            </div>
            <div class="data-time">{{ item.tm }}</div>
          </div>

          <div class="info-content flx">
            <!-- <div class="content-item">
              <div class="label">位置</div>
              <div class="value">{{ item.pos }}</div>
            </div> -->
            <div class="content-item">
              <div class="label">{{ isActual ? '当前水位/警戒差(m)' : '最高水位/警戒差(m)' }}</div>
              <div class="value" :style="{ color: getDistance('color', item) as string }">
                {{ item.waterLevel }}/{{ getDistance('symbol', item)
                }}{{ getDistance('num', item) }}
              </div>
            </div>
            <div class="content-item">
              <div class="label">是否漫堤</div>
              <div class="value" :style="{ color: item.isInrush == 1 ? '#FF4B4B' : '#fff' }">
                {{ item.isInrush == 1 ? '是' : '否' }}
              </div>
            </div>
            <!-- <div class="content-item" v-if="dmOnly">
              <div class="label">洪峰(m³/s)</div>
              <div class="value">{{ item.hf }}</div>
            </div> -->
          </div>
        </div>
      </li>
    </ul>

    <no-warning
      :is-actual="isActual"
      v-if="(isActual && !tableData.length) || (!isActual && !tableDataForecast.length)"
    ></no-warning>
  </div>
</template>

//河道水位预警
<script setup lang="ts">
import NoWarning from './NoWarning.vue'
import { GetActualWarnVO, ActualWarnVo } from '@/api/interface/warning/VO'

interface Props {
  isActual: boolean
  warningData: GetActualWarnVO
  // dmOnly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  // dmOnly: false
})

const tableData = ref<Array<ActualWarnVo>>([
  // {
  //   stcd: '1',
  //   stnm: 'XX',
  //   tm: '01-02 15:00',
  //   waterLevel: 10,
  //   limitLevel: 0,
  //   pos: '上游',
  //   isInrush: 1,
  //   sttp: 'ZZ'
  // }
])

const tableDataForecast = ref<Array<ActualWarnVo>>([
  // {
  //   stcd: '1',
  //   stnm: 'XX',
  //   tm: '01-02 15:00',
  //   waterLevel: 10,
  //   limitLevel: 12,
  //   pos: '上游',
  //   isInrush: 0,
  //   sttp: 'DM'
  // }
])

const getDistance = (type, row) => {
  //获取警戒差
  let waterLevel = row.waterLevel || 0
  let limitLevel = row.limitLevel || 0
  if (type === 'symbol') {
    return waterLevel - limitLevel > 0 ? '+' : ''
  } else if (type === 'num') {
    return Number(waterLevel - limitLevel).toFixed(2)
  } else if (type === 'color') {
    return waterLevel - limitLevel > 0 ? '#ff161a' : '#08d075'
  }
}

watch(
  () => [props.warningData],
  () => {
    if (props.isActual) {
      tableData.value = props.warningData.riverList || []
    } else {
      tableDataForecast.value = props.warningData.riverList || []
    }
  },
  {
    deep: true
  }
)

onMounted(() => {
  if (props.isActual) {
    tableData.value = props.warningData.riverList || []
  } else {
    tableDataForecast.value = props.warningData.riverList || []
  }
})
</script>

<style lang="scss" scoped>
.river-warning-box {
  padding: 9px 11px;

  .river-list {
    width: 100%;
    max-height: 220px;
    overflow-y: scroll;

    .river-item {
      width: 100%;
      padding: 8px 0;
      border-bottom: 1px dashed #ffffff70;
      border-radius: 2px;

      &:last-child {
        border-bottom: 0;
      }

      .type-info {
        flex-direction: column;
        width: 64px;
        height: 56px;
        border-radius: 2px;

        span {
          margin-top: 4px;
          font-size: 12px;
        }
      }

      .green-bg {
        background: #7ff3;

        img {
          width: 28px;
        }

        span {
          color: #0ff;
        }
      }

      .orange-bg {
        background: #ffd06033;

        img {
          width: 22px;
        }

        span {
          color: #ffce5e;
        }
      }

      .main-info {
        padding-left: 12px;

        .site-name {
          margin-right: 6px;
          font-size: 16px;
          line-height: 16px;
        }

        .data-time {
          font-size: 14px;
          line-height: 14px;
          color: $color-base-white;
        }

        .info-content {
          .content-item {
            padding-right: 22px;
            margin-right: 22px;
            font-size: 14px;
            border-right: 1px solid #ffffff60;

            &:last-child {
              padding-right: 0;
              margin-right: 0;
              border-right: 0;
            }

            .label {
              color: #ffffff60;
            }

            .value {
              color: #fff;
            }
          }

          // .num {
          //   margin-right: 10px;
          //   font-size: 14px;
          //   color: #ffffff60;

          //   span {
          //     color: #ff4b4b;
          //   }
          // }
        }
      }
    }
  }
}
</style>
