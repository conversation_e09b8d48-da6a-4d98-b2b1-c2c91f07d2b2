/* 运行管护-维修养护 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  list: apiUrl.defaultUrl + '/operaMaintenance/list',
  statisticsWxyh: apiUrl.defaultUrl + '/operaMaintenance/statisticsWxyh',
  detailList: apiUrl.defaultUrl + '/operaMaintenance/detailList',
  deleteById: apiUrl.defaultUrl + '/operaMaintenance/deleteById',
  exportList: apiUrl.defaultUrl + '/operaMaintenance/detailListExport',
  save: apiUrl.defaultUrl + '/operaMaintenance/save',
  charts: apiUrl.defaultUrl + '/operaMaintenance/charts',
  queryOverviewListByArea: apiUrl.defaultUrl + '/operaMaintenance/queryOverviewListByArea',
  getMaintenancePlanList: apiUrl.defaultUrl + '/operaMaintenancePlan/page',
  addMaintenancePlan: apiUrl.defaultUrl + '/operaMaintenancePlan/add',
  updateMaintenancePlan: apiUrl.defaultUrl + '/operaMaintenancePlan/edit',
  deleteMaintenancePlan: apiUrl.defaultUrl + '/operaMaintenancePlan/delete'
}

/**
 * 维修养护-列表记录查询
 * "pageNum": number,
 * "pageSize": number
 */
export function list(data) {
  return $http.post(api.list + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}

/**
 * 维修养护-统计图表
 * "beginDate": "string",
 * "dayValue": 0,
 * "endDate": "string",
 * "projectId": 0,
 * "type": "string",
 * "wxyhxm": "string"
 */
export function statisticsWxyh(data) {
  return $http.post(api.statisticsWxyh, data)
}

/**
 * 维修养护-项目查询
 * "pageNum": number,
 * "pageSize": number
 */
export function detailList(data) {
  return $http.post(api.detailList, data)
}

/**
 * 维修养护-项目删除
 * id
 */
export function deleteById(data) {
  return $http.post(api.deleteById, data)
}

/**
 * 维修养护-项目导出
 * id
 */
export function exportList(data, contentType, responseType) {
  return $http.postDownLoad(api.exportList, data, contentType, responseType, false, true)
}

/**
 * 维修养护-项目保存
 * id
 */
export function save(data) {
  return $http.post(api.save, data)
}
// 图表统计
export function charts(data) {
  return $http.post(api.charts, data)
}
// 按区划查询总览
export function queryOverviewListByArea(data) {
  return $http.post(api.queryOverviewListByArea, data)
}

export function getMaintenancePlanList(data) {
  return $http.get(api.getMaintenancePlanList, data)
}

export function addMaintenancePlan(data) {
  return $http.post(api.addMaintenancePlan, data)
}

export function updateMaintenancePlan(data) {
  return $http.post(api.updateMaintenancePlan, data)
}

export function deleteMaintenancePlan(data) {
  return $http.post(api.deleteMaintenancePlan, data)
}

