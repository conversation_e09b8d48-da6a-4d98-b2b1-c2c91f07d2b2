<template>
  <el-form
    ref="form"
    label-suffix=":"
    v-bind="$attrs"
    :model="formData"
    :rules="options.rules"
    :validate-on-rule-change="false"
    :label-width="options.labelWidth || 'auto'"
  >
    <el-row :gutter="14" v-if="getConfigList.length">
      <template v-for="(item, index) in getConfigList">
        <el-col :key="index" :span="item.span || options.span ||  12">
          <el-form-item :prop="item.value" :label="item.label" :label-width="item.labelWidth">
            <slot :name="'form-' + item.value">
              <!-- 附件上传 -->
              <task-upload
                v-if="item.type === 'upload'"
                ref="taskUpload"
                v-bind="item.attrs"
                :file-list.sync="formData[item.value]"
              />
              <component
                v-else
                :is="item.type"
                v-model="formData[item.value]"
                v-bind="item.attrs"
                :disabled="item.disabled"
                :placeholder="getPlaceholder(item)"
                :style="item.attrs && item.attrs.style ? item.attrs.style : 'width: 100%'"
                @click.native="
                  handleEvent(item.event, formData[item.value], 'click', item.value)
                "
                @change="handleEvent(item.event, formData[item.value], 'change', item.value)"
                @input="handleEvent(item.event, formData[item.value], 'input', item.value)"
                @blur="handleEvent(item.event, formData[item.value], 'blur', item.value)"
              >
                <template v-if="item.type === 'el-select'">
                  <el-option
                    v-for="(childItem, childIndex) in item.list"
                    :key="childIndex"
                    :label="childItem.label"
                    :value="childItem.value"
                  >
                  </el-option>
                </template>
                <template v-if="item.type === 'el-radio-group'">
                  <el-radio
                    v-for="(childItem, childIndex) in item.list"
                    :key="childIndex"
                    :label="childItem.value"
                    :disabled="childItem.disabled"
                  >
                    {{ childItem.label }}
                  </el-radio>
                </template>
              </component>
            </slot>
          </el-form-item>
        </el-col>
      </template>
    </el-row>
  </el-form>
</template>
<script>
import TaskUpload from '@/components/elCommon/taskUpload.vue'
// const titleTemplate = {
//   add: '新增',
//   edit: '编辑',
//   view: '查看'
// }
export default {
  name: 'taskForm',
  components: { TaskUpload },
  props: {
    // 表单数据
    data: { type: Object },
    // 表单配置
    options: { type: Object, required: true },
    // 表单类型 自定义例如新增、编辑
    formType: { type: String, default: '' }
  },
  watch: {
    data: {
      handler (val) {
        if (val) {
          this.formData = val
        }
      },
      deep: true,
      immediate: true
    }
  },
  data () {
    return {
      formData: {},
      fileList: [],
      curFormItem: {},
      datePickerOptions: {
        // 不能选择当前之前的时间
        disabledCurrentBefore: {
          disabledDate (time) {
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 1)
            return time.getTime() < start.getTime()
          }
        }
      }
    }
  },
  computed: {
    getConfigList () {
      if (this.options.fieldList.length) {
        return this.options.fieldList.filter(
          item =>
            !item.hasOwnProperty.call('show') ||
            (item.hasOwnProperty.call('show') && item.show)
        )
      } else {
        return []
      }
    }
  },
  methods: {
    // 得到placeholder的显示
    getPlaceholder (row) {
      if (row.attrs?.placeholder) {
        return row.attrs.placeholder
      } else {
        let placeholder
        if (row.type.includes('input') || row.type.includes('textarea')) {
          placeholder = '请输入' + row.label
        } else if (
          row.type.includes('el-select') ||
          row.type.includes('time') ||
          row.type.includes('date')
        ) {
          placeholder = '请选择' + row.label
        } else {
          placeholder = row.label
        }
        return placeholder
      }
    },
    // 绑定的相关事件
    handleEvent (e, data, type, key) {
      this.$emit('handleEvent', { e, key, data, type })
    },
    fileRemove (type) {
      if (Array.isArray(this.$refs.taskUpload)) {
        this.$refs.taskUpload.forEach(refItem => {
          refItem.deleteFiles(type)
        })
      } else {
        this.$refs.taskUpload.deleteFiles(type)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.search-form-box {
  background: #f5f7fa;
  padding: 20px;
}
</style>
