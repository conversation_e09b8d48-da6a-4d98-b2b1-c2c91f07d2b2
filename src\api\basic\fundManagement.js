/* 基础管理-经费管理 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteDetailById: apiUrl.defaultUrl + '/scoreFundManagement/deleteDetailById',
  deleteNdById: apiUrl.defaultUrl + '/scoreFundManagement/deleteNdById',
  details: apiUrl.defaultUrl + '/scoreFundManagement/details',
  projectList: apiUrl.defaultUrl + '/scoreFundManagement/projectList',
  recordtList: apiUrl.defaultUrl + '/scoreFundManagement/recordtList',
  saveFundData: apiUrl.defaultUrl + '/scoreFundManagement/saveFundData',
  insertDetail: apiUrl.defaultUrl + '/scoreFundManagement/saveDetail',
  getFundsOverview: apiUrl.defaultUrl + '/moreProject/getFundsOverview',
}

/**
 * 经费管理-删除明细
 * id
 */
export function deleteDetailById (data) {
  return $http.post(api.deleteDetailById, data)
}

/**
 * 经费管理-删除年度数据
 * id
 */
export function deleteNdById (data) {
  return $http.post(api.deleteNdById, data)
}

/**
 * 经费管理-查看详情
 * "projectId": 0,
 * "years": [
 *   0
 * ]
 */
export function details (data) {
  return $http.post(api.details, data)
}

/**
 * 经费管理-工程列表查询
 * "areaCode": "string",
 * "projectName": "string",
 * "projectScale": 0,
 * "projectType": "string"
 */
export function projectList (data, params) {
  return $http.postParams(api.projectList, data, params)
}

/**
 * 经费管理-列表记录查询
 * "projectId": 0,
 * "years": [
 *   0
 * ]
 */
export function recordtList (data) {
  const url = api.recordtList + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`
  delete data.pageNum
  delete data.pageSize
  return $http.post(url, data)
}

/**
 * 经费管理-保存
 * "id": 0,
 * "nd": 0, // 年度
 * "projectId": 0, // 工程id
 * "scoreFundDetailsUpdateDTOList": [
 *   {
 *     "fundId": 0, // 年度经费id
 *     "imgId": "string", // 图片id
 *     "jflx": 0, // 经费类型：1-维修养护经费，2-运行管理经费
 *     "sxjf": 0, // 所需经费
 *     "xgwjId": "string", // 相关文件id
 *     "yt": "string" // 用途
 *   }
 * ],
 * "wxysyjf": 0, // 维修已使用经费
 * "wxzjf": 0, // 维修总经费
 * "yxysyjf": 0, // 运行已使用经费
 * "yxzjf": 0 // 运行总经费
 */
export function saveFundData (data) {
  return $http.post(api.saveFundData, data)
}

/**
 * 经费管理-新增表格数据
 * "fundId": 0, // 年度经费id
 * "imgId": "string", // 图片id
 * "jflx": 0, // 经费类型：1-维修养护经费，2-运行管理经费 int
 * "sxjf": 0, // 所需经费 number
 * "xgwjId": "string", // 相关文件id
 * "yt": "string" // 用途
 */
export function insertDetail (data) {
  return $http.post(api.insertDetail, data)
}
/*
  经费管理 - 总览列表
*/
export function getFundsOverview (data) {
  return $http.post(api.getFundsOverview, data)
}
