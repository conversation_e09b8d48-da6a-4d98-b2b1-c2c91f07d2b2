<template>
  <div class="f-legend">
    <div class="title">图例</div>
    <div class="mt12">
      <div class="f-legend-item" v-for="item in LEGEND_LIST" :key="item.text">
        <el-image :src="item.icon" class="icon" />
        <div class="text">{{ item.text }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { LAYOUT_RIGHT_START_WIDTH } from '@/layouts/constant'
import { LEGEND_LIST, LEGEND_POP_CONFIG } from '../constant'
import { useMap3dStore } from '@/stores/modules/map3d'
import { flashIconConfig, getPopupHtml, markerConfig } from '@/hooks/useMapMarker'
import { checkNull } from '@/utils'
import { getForecastResultOnMap } from '@/alova_api/methods/forecast'
const { addComPointLayer, locateTo, pointHeight, removeLayer } = useMap3dStore()
const legendListRight = inject(LAYOUT_RIGHT_START_WIDTH)

const props = defineProps({
  planId: {
    type: Number,
    default: 0
  }
})

const getHtmlContent = (type: keyof typeof LEGEND_POP_CONFIG) =>
  LEGEND_POP_CONFIG[type].configList
    ?.map(item => {
      if (item.key === 'currentTm') {
        return `<div style="margin-bottom:4px;margin-top:-4px;padding-left:4px;">{${item.key}}</div>`
      } else {
        return `<div style="background-color:#00224350;margin-bottom:2px;padding:4px 8px;"><span style="color:#7ff;">${item.label}</span>{${item.key}}</div>`
      }
    })
    ?.join('')

const loadPoints = async () => {
  if (!props.planId) {
    return
  }
  const points = await getForecastResultOnMap(props.planId)
  points
    .reduce(
      (acc, item) => {
        const target = acc.find(it => it.type === item.sttp)
        if (target) {
          target.data.push(item)
        }
        return acc
      },
      [...LEGEND_LIST]
    )
    .forEach((item, index) => {
      setPoints(item, index)
    })
}
const layers: string[] = []
const setPoints = (item: (typeof LEGEND_LIST)[number], index: number) => {
  layers.push('legend-point-' + index)
  addComPointLayer({
    id: 'legend-point-' + index,
    idAttr: 'stcd',
    data: item.data.map(i => ({ ...i, h: pointHeight })),
    posAttr: { x: 'lgtd', y: 'lttd', z: 'h' }, //位置属性
    symbol: {
      label: {
        maxRange: 22000
      },
      billboard: {
        image: markerConfig[item.type]
        // clampToGround: true
      },
      window: {
        div: getPopupHtml(getHtmlContent(item.type), 'title'),
        divData: {
          z: data => checkNull(data.z),
          q: data => checkNull(data.q),
          title: data => checkNull(data.stname)
        },
        moveShow: true,
        pixelOffset: [0, -50]
      },
      conditionSetDef: function (data) {
        return {
          billboard: {
            //图标类配置
            image: flashIconConfig[data.sttp || ''],
            width: 45,
            height: 36,
            // clampToGround: true,
            // depthTest: true,
            timeInterval: 50
          }
        }
      }
    },
    click: function (e) {
      locateTo({
        lat: e.overlay?._attr?.lttd,
        lon: e.overlay?._attr?.lgtd,
        height: 5000
      })
    },
    zoom: {
      minDist: item.minDist || 40000
    }
  })
}
const removeAllLayers = () => {
  layers.forEach(layer => {
    removeLayer(layer)
  })
  layers.splice(0, layers.length)
}

watch(
  () => props.planId,
  () => {
    removeAllLayers()
    loadPoints()
  }
)

onMounted(() => {
  loadPoints()
})

onUnmounted(() => {
  removeAllLayers()
})
</script>

<style lang="scss" scoped>
.f-legend {
  width: 120px;
  height: auto;
  background-color: rgba(8, 64, 120, 0.6);
  border-radius: 2px;
  position: absolute;
  bottom: 40px;
  right: v-bind(legendListRight);
  box-sizing: border-box;
  padding: 10px 14px;
  transition: all 0.3s ease-in-out;
  overflow: hidden;
  .title {
    font-size: 14px;
    color: #77ffff;
    font-weight: 500;
    line-height: 22px;
    margin-bottom: 5px;
  }
  .f-legend-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 4px;
    .icon {
      width: 16px;
      height: 16px;
    }
    .text {
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      color: #fff;
    }
  }

  .water-layer-btn {
    width: 100%;
    font-size: 12px;
    height: 28px;
    border-radius: 4px;
    background: #1890ff;
    border: 1px solid #1890ff;
    color: #fff;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }

    &:active {
      background: #096dd9;
      border-color: #096dd9;
    }
  }
}
</style>
