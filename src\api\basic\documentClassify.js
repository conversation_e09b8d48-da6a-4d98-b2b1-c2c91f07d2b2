/* 基础管理-档案分类 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteById: apiUrl.defaultUrl + '/scoreDocumentClassify/deleteById',
  list: apiUrl.defaultUrl + '/scoreDocumentClassify/list',
  save: apiUrl.defaultUrl + '/scoreDocumentClassify/save',
  getDocsChart: apiUrl.defaultUrl + 'std/moreProject/getDocsChart',
}

/**
 * 档案分类-删除
 */
export function deleteById (data) {
  return $http.post(api.deleteById, data)
}

/**
 * 档案分类-工程&分类查询
 * "areaCode": "string",
 * "projectName": "string",
 * "projectScale": 0,
 * "projectType": "string"
 */
export function list (data,params) {
  return $http.post(api.list + `?pageNum=${params.pageNum}&pageSize=${10}`, data,params)
}

/**
 * 档案分类-保存
 * "docClassify": "string",
 * "id": 0,
 * "parentId": 0,
 * "projectId": 0
 */
export function save (data) {
  return $http.post(api.save, data)
}
/*
档案分类-多工程总览
*/
export function getDocsChart (data) {
  return $http.post(api.getDocsChart, data)
}
