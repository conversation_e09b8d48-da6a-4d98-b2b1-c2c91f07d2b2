import { DescRecordItem } from '@/components/DescRecord/type'

export const BASE_INFO_OPTIONS: DescRecordItem[] = [
  {
    label: '水库代码',
    value: 'resCode',
    valueUnit: ''
  },
  {
    label: '水库名称',
    value: 'resName',
    valueUnit: ''
  },
  {
    label: '水库中心经度',
    value: 'resCenLong',
    valueUnit: ''
  },
  {
    label: '水库中心纬度',
    value: 'resCenLat',
    valueUnit: ''
  },
  {
    label: '水库所在位置',
    value: 'resLoc',
    valueUnit: ''
  },
  {
    label: '水库类型',
    value: 'resType',
    valueUnit: '',
    dictCode: 'RESERVOIR_TYPE'
  },
  {
    label: '水库功能',
    value: 'rsfn',
    valueUnit: ''
  },
  {
    label: '工程等别',
    value: 'engGrad',
    valueUnit: '',
    dictCode: 'PROJECT_GRADE'
  },
  {
    label: '工程规模',
    value: 'engScal',
    valueUnit: '',
    dictCode: 'PROJECT_SCALE'
  },
  {
    label: '坝址控制流域面积',
    value: 'watShedArea',
    valueUnit: 'km²'
  },
  {
    label: '工程建设情况',
    value: 'engStat',
    valueUnit: '',
    dictCode: 'ENG_STAT'
  },
  {
    label: '开工时间',
    value: 'startDate',
    valueUnit: ''
  },
  {
    label: '建成时间',
    value: 'compDate',
    valueUnit: ''
  },
  {
    label: '归口管理部门',
    value: 'admDep',
    valueUnit: '',
    dictCode: 'ADM_DEP'
  },
  {
    label: '管理单位',
    value: 'mnun',
    valueUnit: ''
  },
  {
    label: '管理单位类别',
    value: 'mnunType',
    valueUnit: '',
    dictCode: 'MANAGEMENT_UNIT_TYPE'
  },
  {
    label: '单位责任人',
    value: 'mnunResponsible',
    valueUnit: ''
  },
  {
    label: '管理单位性质',
    value: 'mnunNature',
    valueUnit: ''
  },

  {
    label: '主管单位',
    value: 'cmun',
    valueUnit: ''
  },
  {
    label: '所在流域',
    value: 'basin',
    valueUnit: ''
  },
  {
    label: '所在河流',
    value: 'river',
    valueUnit: ''
  },
  {
    label: '水库概况',
    value: 'resOv',
    valueUnit: '',
    full: true
  },
  {
    label: '备注',
    value: 'note',
    valueUnit: '',
    full: true
  }
]

export const HYDRO_INFO_OPTIONS: DescRecordItem[] = [
  {
    label: '高程系统',
    value: 'elevSys',
    valueUnit: ''
  },
  {
    label: '高程系统转换值',
    value: 'elevSysConver',
    valueUnit: 'm'
  },
  {
    label: '死水位',
    value: 'deadLev',
    valueUnit: 'm'
  },
  {
    label: '库容复核日期',
    value: 'capReviewDate',
    valueUnit: ''
  },
  {
    label: '正常蓄水位',
    value: 'normWatLev',
    valueUnit: 'm'
  },
  {
    label: '防洪高水位',
    value: 'uppLevFlco',
    valueUnit: 'm'
  },
  {
    label: '设计洪水位',
    value: 'dsfllv',
    valueUnit: 'm'
  },
  {
    label: '校核洪水位',
    value: 'chfllv',
    valueUnit: 'm'
  },
  {
    label: '旱警水位',
    value: 'drwlv',
    valueUnit: 'm'
  },
  {
    label: '总库容',
    value: 'totCap',
    valueUnit: '万m³'
  },
  {
    label: '死库容',
    value: 'deadCap',
    valueUnit: '万m³'
  },
  {
    label: '调洪库容',
    value: 'storFlCap',
    valueUnit: '万m³'
  },
  {
    label: '防洪库容',
    value: 'flcoCap',
    valueUnit: '万m³'
  },
  {
    label: '正常蓄水位相应库容',
    value: 'normPoolStagCap',
    valueUnit: '万m³'
  },
  {
    label: '设计洪水总量',
    value: 'dsflnu',
    valueUnit: 'm³/s'
  },
  {
    label: '校核洪水总量',
    value: 'chflvl',
    valueUnit: 'm³/s'
  },
  {
    label: '设计洪峰流量',
    value: 'dspkfl',
    valueUnit: 'm³/s'
  },
  {
    label: '校核洪峰流量',
    value: 'chpkfl',
    valueUnit: 'm³/s'
  },
  {
    label: '多年平均径流量',
    value: 'avanrnam',
    valueUnit: '万m³'
  },
  {
    label: '多年平均降水量',
    value: 'avanpram',
    valueUnit: 'mm'
  },
  {
    label: '水库调节特性',
    value: 'rsrrgtpfm',
    valueUnit: '',
    dictCode: 'RESERVOIR_REGULATION'
  },
  {
    label: '设计重现期',
    value: 'dsrcin',
    valueUnit: '年'
  },
  {
    label: '校核重现期',
    value: 'chrcin',
    valueUnit: '年'
  },
  {
    label: '集雨面积',
    value: 'catchmentArea',
    valueUnit: 'km²'
  },
  {
    label: '备注',
    value: 'note',
    valueUnit: '',
    full: true
  }
]

// 主坝、副坝
export const DAM_INFO_OPTIONS: DescRecordItem[] = [
  {
    label: '大坝名称',
    value: 'damName',
    valueUnit: ''
  },
  {
    label: '大坝代码',
    value: 'damCode',
    valueUnit: ''
  },
  {
    label: '大坝级别',
    value: 'damGrad',
    valueUnit: '',
    dictCode: 'DAM_GRADE'
  },
  {
    label: '大坝所在位置',
    value: 'damLoc',
    valueUnit: ''
  },
  {
    label: '最大坝高',
    value: 'damMaxHeig',
    valueUnit: 'm'
  },
  {
    label: '大坝概况',
    value: 'damOv',
    valueUnit: '',
    span: 2
  },
  {
    label: '水库大坝中点纬度',
    value: 'damStartLat',
    valueUnit: ''
  },
  {
    label: '水库大坝中点经度',
    value: 'damStartLong',
    valueUnit: ''
  },
  {
    label: '坝顶高程',
    value: 'damTopElev',
    valueUnit: 'm'
  },
  {
    label: '坝顶长度',
    value: 'damTopLen',
    valueUnit: 'm'
  },
  {
    label: '坝顶宽度',
    value: 'damTopWid',
    valueUnit: 'm'
  },
  {
    label: '大坝材料类型',
    value: 'damTypeMat',
    valueUnit: '',
    dictCode: 'DAM_MATERIAL_TYPE'
  },
  {
    label: '大坝结构类型',
    value: 'damTypeStr',
    valueUnit: '',
    dictCode: 'DAM_STRUCTURE_TYPE'
  },
  {
    label: '高程系统',
    value: 'elevSys',
    valueUnit: '',
    dictCode: 'ELEVATION_SYSTEM'
  },
  {
    label: '高程转换值',
    value: 'elevSysConver',
    valueUnit: ''
  },
  {
    label: '工程等别',
    value: 'engGrad',
    valueUnit: '',
    dictCode: 'PROJECT_GRADE'
  },
  {
    label: '是否主坝',
    value: 'ifMainDam',
    valueUnit: '',
    valueFormat: (value: string) => {
      return value === '1' ? '是' : '否'
    }
  },
  {
    label: '是否溢流坝',
    value: 'ifYlDam',
    valueUnit: '',
    valueFormat: (value: string) => {
      return value === '1' ? '是' : '否'
    }
  },
  {
    label: '备注',
    value: 'note',
    valueUnit: '',
    span: 2
  },
  {
    label: '防浪墙顶高程',
    value: 'wvwltPel',
    valueUnit: 'm'
  }
]

// 溢洪道
export const SPILLWAY_INFO_OPTIONS: DescRecordItem[] = [
  {
    label: '溢洪道名称',
    value: 'swnm',
    valueUnit: ''
  },
  {
    label: '溢洪道编号',
    value: 'swcd',
    valueUnit: ''
  },
  {
    label: '堰顶高程',
    value: 'wrcrel',
    valueUnit: 'm'
  },
  {
    label: '控制方式',
    value: 'crmt',
    valueUnit: '',
    dictCode: 'CONTROL_METHOD'
  },
  {
    label: '闸门数量',
    value: 'gtam',
    valueUnit: '个'
  },
  {
    label: '溢流堰型式',
    value: 'ofwrst',
    valueUnit: '',
    dictCode: 'WEIR_TYPE'
  },
  {
    label: '堰顶净宽',
    value: 'wrcrntwd',
    valueUnit: 'm'
  },
  {
    label: '闸门型式',
    value: 'gtst',
    valueUnit: '',
    dictCode: 'GATE_TYPE'
  },
  {
    label: '启闭设备',
    value: 'hseq',
    valueUnit: '',
    dictCode: 'HOIST_EQUIPMENT'
  },
  {
    label: '布置位置',
    value: 'lyps',
    valueUnit: '',
    dictCode: 'LAYOUT_POSITION'
  },
  {
    label: '最大泄量',
    value: 'maxds',
    valueUnit: 'm³/s'
  },
  {
    label: '闸门尺寸',
    value: 'gtsz',
    valueUnit: ''
  },
  {
    label: '启闭机数量',
    value: 'hsgram',
    valueUnit: '个'
  },
  {
    label: '消能型式',
    value: 'endsst',
    valueUnit: '',
    dictCode: 'ENERGY_DISSIPATION_TYPE'
  },
  {
    label: '电源条件',
    value: 'pwspcn',
    valueUnit: '',
    span: 3
  },
  {
    label: '备注',
    value: 'note',
    valueUnit: '',
    span: 3
  }
]

// 输水洞
export const TUNNEL_INFO_OPTIONS: DescRecordItem[] = [
  {
    label: '输水洞名称',
    value: 'wtcntnnm',
    valueUnit: ''
  },
  {
    label: '输水洞编码',
    value: 'wtcntncd',
    valueUnit: ''
  },
  {
    label: '输水洞编号/功能码',
    value: 'wtcntnfn',
    valueUnit: ''
  },
  {
    label: '洞长',
    value: 'tnlen',
    valueUnit: 'm'
  },
  {
    label: '进口底高程',
    value: 'inflel',
    valueUnit: 'm'
  },
  {
    label: '出口底高程',
    value: 'otflel',
    valueUnit: 'm'
  },
  {
    label: '断面尺寸',
    value: 'crscsz',
    valueUnit: 'mm'
  },
  {
    label: '布置类型',
    value: 'lytp',
    valueUnit: '',
    dictCode: 'LAYOUT_POSITION'
  },
  {
    label: '布置说明',
    value: 'lyps',
    valueUnit: ''
  },
  {
    label: '闸门设置',
    value: 'gtst',
    valueUnit: '',
    dictCode: 'GATE_TYPE'
  },
  {
    label: '最大流量',
    value: 'maxfl',
    valueUnit: 'm³/s'
  },
  {
    label: '末端形式',
    value: 'endsst',
    valueUnit: '',
    dictCode: 'ENERGY_DISSIPATION_TYPE'
  },
  {
    label: '洪水标准',
    value: 'hseq',
    valueUnit: '',
    dictCode: 'STRUCTURE_TYPE'
  },
  {
    label: '状态',
    value: 'stst',
    valueUnit: '',
    dictCode: 'SSD_STATUS'
  },
  {
    label: '电源条件说明',
    value: 'pwspcn',
    valueUnit: '',
    span: 5
  },
  {
    label: '备注',
    value: 'note',
    valueUnit: '',
    span: 5
  }
]
