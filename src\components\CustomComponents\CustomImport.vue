<template>
  <div class="custom-import">
    <el-button type="primary" icon="el-icon-upload2" @click="show">{{
      buttonText
    }}</el-button>

    <task-dialog
      dialogTop="24vh"
      width="520px"
      dialogTitle="导入"
      :visible.sync="visible"
      :append-to-body="true"
      @updateVisible="handleCancel"
      @submitPopupData="handleOk"
    >
      <el-form ref="entityForm" :model="entityForm" label-suffix=":" label-width="auto">
        <el-form-item
          label="导入文件"
          prop="fileName"
          :rules="[{ required: true, message: '请选择导入文件' }]"
        >
          <div class="file-upload">
            <el-input
              v-model="entityForm.fileName"
              size="medium"
              style="width: 100%"
              readonly
            >
              <template #append>
                <el-upload
                  ref="importUpload"
                  action=""
                  :show-file-list="false"
                  accept=".xlsx, .xls"
                  :before-upload="beforeUpload"
                >
                  <template>
                    <el-button class="browse-btn" type="primary">浏览</el-button>
                  </template>
                </el-upload>
              </template>
            </el-input>
            <div class="file-upload__tip" v-if="template">
              请点击
              <span class="down-text" @click="downloadTemplate">
                下载导入模板
                <i class="el-icon-download"></i>
              </span>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </task-dialog>
  </div>
</template>

<script>
import TaskDialog from "@/components/elCommon/taskDialog/taskDialog";
import { downloadFileByBlob } from "@/utils/utils";
import $http from "@/utils/request";

export default {
  name: "CustomImport",
  components: {
    TaskDialog,
  },
  props: {
    // 按钮文字内容
    buttonText: { type: String, default: "导入" },
    // 导入方法
    importApi: { type: Function, required: true },
    // 导入类型
    template: { type: String, default: "" },
    // 导入模板
    templateApi: { type: String, default: "" },
  },
  data() {
    return {
      // 弹出框
      visible: false,
      confirmLoading: false,
      // 表单
      entityForm: {
        fileName: "",
      },
      file: null,
    };
  },
  computed: {
    importType: (vm) => excelImportTypes[vm.path] || {},
  },
  methods: {
    show() {
      this.confirmLoading = false;
      this.file = undefined;
      this.entityForm = {
        fileName: "",
      };
      this.visible = true;
      this.$nextTick(() => this.$refs.entityForm.resetFields());
    },

    // 文件导入提交前
    beforeUpload(file) {
      this.file = file;
      this.entityForm.fileName = file.name;
      return false;
    },

    // 下载导入模板
    downloadTemplate() {
      $http.getDownLoad(this.templateApi, {}, "", "blob").then((res) => {
        downloadFileByBlob(this.template + "导入模板.xlsx", res);
      });
    },

    handleOk() {
      this.$refs.entityForm.validate((valid) => {
        if (!valid) return;
        this.confirmLoading = true;
        const formData = new FormData();
        formData.append("file", this.file);
        this.importApi(formData)
          .then((res) => {
            if (res.status === 200) {
              this.$message.success("导入数据成功！");
              this.$emit("uploaded");
            } else {
              this.$message.error("导入数据失败，请重新导入！");
            }
            this.visible = false;
            this.confirmLoading = false;
          })
          .catch((err) => {
            this.$message.error(
              err.response.data.message || "导入数据失败，请重新导入！"
            );
            this.confirmLoading = false;
          });
      });
    },
    handleCancel() {
      this.visible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-import {
  display: inline-block;
  margin: 0 10px;
  .file-upload {
    .browse-btn {
      background-color: #0a4ead;
      color: #fff;
      border-bottom: solid 1px #368aff;
      border-radius: 0;
    }
    .file-upload__tip {
      font-size: 18px;
      color: #606266;
      .down-text {
        color: #409eff;
        cursor: pointer;
      }
    }
  }
  ::v-deep(.el-form-item) {
    width: 100%;
  }
  ::v-deep(.el-form-item__content) {
    display: block;
  }
}
</style>
