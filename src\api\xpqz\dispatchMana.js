import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  scheduleList: apiUrl.defaultUrl + '/xpqz/schedule/list',
  exportSchedule: apiUrl.defaultUrl + '/xpqz/schedule/export',
  pushSchedule: apiUrl.defaultUrl + '/xpqz/schedule/push',
  updateSchedule: apiUrl.defaultUrl + '/xpqz/schedule/update',
  deleteSchedule: apiUrl.defaultUrl + '/xpqz/schedule/delete',
  getOperationList: apiUrl.defaultUrl + '/xpqz/schedule/getOperationList',
  saveOperation: apiUrl.defaultUrl + '/xpqz/schedule/saveOperation',
  deleteOperation: apiUrl.defaultUrl + '/xpqz/schedule/deleteOperation',
  operationExport: apiUrl.defaultUrl + '/xpqz/schedule/operationExport'
}

/**
 * 调度方案列表接口
 * @param data
 */
export function scheduleList (data) {
  return $http.post(api.scheduleList, data)
}
export function getOperationList (data) {
  return $http.post(api.getOperationList, data)
}
/**
 * 调度方案列表接口
 * @param data
 */
export function pushSchedule (data) {
  return $http.post(api.pushSchedule, data)
}
/**
 * 调度方案列表接口
 * @param data
 */
export function updateSchedule (data) {
  return $http.post(api.updateSchedule, data)
}
export function saveOperation (data) {
  return $http.post(api.saveOperation, data)
}
/**
 * 调度方案列表接口
 * @param data
 */
export function deleteSchedule (data) {
  return $http.post(api.deleteSchedule, data)
}

export function deleteOperation (data) {
  return $http.post(api.deleteOperation, data)
}

/**
 * 预警通知人下拉框接口
 */
export function getYjUsers (params) {
  return $http.get(api.getYjUsers, params)
}

/**
 * 调度方案导出接口
 * @param data
 */
export function exportSchedule (data) {
  return $http.postDownLoad(api.exportSchedule, data, '', 'arraybuffer')
}
export function operationExport (data) {
  return $http.postDownLoad(api.operationExport, data, '', 'arraybuffer')
}
