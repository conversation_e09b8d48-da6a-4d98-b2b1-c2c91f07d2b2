/* 运行管护-确权划界 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  detailQh: apiUrl.defaultUrl + '/confirm/findDemaraCount',
  notImplementedList: apiUrl.defaultUrl + '/confirm/getNotWorkAble',
  basinInfo: apiUrl.defaultUrl + '/confirm/getBasinList',
  projectFunc: apiUrl.defaultUrl + '/confirm/view',
  editProject: apiUrl.defaultUrl + '/confirm/addOrUpdateDemara',
  getRange: apiUrl.defaultUrl + '/confirm/getBasinRange',
  addRange: apiUrl.defaultUrl + '/confirm/addRange'
}
/**
 * 确权划界-区划统计
 * "areaCode": string

 */
export function detailQh (params) {
  return $http.get(api.detailQh, params)
}

/**
 * 确权划界-未落实名单
 * "areaCode": string
 * "pageNum": number
 * "pageSize": number
 */
export function notImplementedList (params) {
  return $http.get(api.notImplementedList, params)
}

/**
 * 确权划界-流域信息
 * "appKey": string,
 */
export function basinInfo (params) {
  return $http.get(api.basinInfo, params)
}


/**
 * 确权划界-工程功能
 * "appKey":"string"
 * "areaCode":"string"
 * "projectType":"string"
 */
export function projectFunc (data) {
  return $http.post(api.projectFunc, data)
}

/**
 * 确权划界 - 编辑工程功能
 * @param {*} data
 * @returns
 */
export function editProject (data) {
  return $http.post(api.editProject, data)
}

/**
 * 确权划界 - 获取保护、管理范围
 * "appKey":"string"
 */
export function getRange(params) {
  return $http.get(api.getRange, params)
}

/**
 * 确权划界 - 设置保护、管理范围
 * "appKey":"string"
 * "type":"number"
 * "file":"string"
 */
export function addRange(data) {
  return $http.post(api.addRange, data)
}
