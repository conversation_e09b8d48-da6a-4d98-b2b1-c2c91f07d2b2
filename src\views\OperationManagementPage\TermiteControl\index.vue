<template>
  <div class="termite-control">
    <el-radio-group v-model="activeTab" class="mb10">
      <el-radio-button label="preventionRecord">防治记录</el-radio-button>
      <el-radio-button label="monitorDev">监测设备</el-radio-button>
    </el-radio-group>
    <ProjectTree
      v-show="activeTab === 'preventionRecord'"
      :is-detail="isDetail"
      @getProjectMsg="getProjectMsg"
      @goBack="goBack"
      @checkTypeChange="checkTypeChange"
    >
      <template #content>
        <div class="flex-column tc-table" v-show="!showDetail">
          <div class="graphical">
            <CustomCard
              title="辖区内水利工程制定白蚁防治计划比例"
              class="chartArea"
            >
              <div class="chart" id="pie1"></div>
            </CustomCard>
            <CustomCard
              title="未完成工程分类统计"
              class="chartArea"
              style="margin-left: 20px"
            >
              <div class="chart" id="pie2"></div>
            </CustomCard>
          </div>
          <!-- <project-search :columns="formColumns" :btn-columns="btnColumns" @load="(options) => loadDataFn(options)">
          </project-search> -->
          <free-table
            :data="tableData"
            :column="tableColumns"
            height="100%"
            :showPagination="userInfo?.areaCode.length === 6"
            background
            paginationPosition="bottom"
            :total="pagination.total"
            :page-size="pagination.limit"
            :current-page.sync="pagination.page"
            @page-change="loadData"
          >
            <template #actions="{ row }">
              <el-button
                class="std-detail-btn"
                type="text"
                @click="rowDetail(row)"
                >详情</el-button
              >
            </template>
          </free-table>
        </div>
        <div class="tc-detail" v-show="showDetail">
          <div class="std-detail-header flex-vc j-sb mb20">
            <!-- <div class="title"> -->
            <!-- <el-button style="margin-right: 20px;" v-if="btnShow" type="primary" class="el-icon-back"
                @click="back">返回</el-button> -->
            <!-- <h1 style="font-weight: 700;">{{ title }}</h1> -->
            <!-- </div> -->
            <el-button type="primary" @click="confirm">保存</el-button>
          </div>
          <div class="detail-content flex-column">
            <div class="termite-plan flex j-sb">
              <div class="termite-plan-left">
                <div class="flex">
                  <div class="plan-label">白蚁防治计划:</div>
                  <el-radio-group v-model="detailRecord.termitePlan">
                    <el-radio :label="1">有</el-radio>
                    <el-radio :label="0">无</el-radio>
                  </el-radio-group>
                </div>
                <div class="flex">
                  <div class="plan-label">白蚁监测范围:</div>
                  <el-input
                    style="width: 100px; margin-bottom: 5px"
                    size="small"
                    v-model="detailRecord.area"
                    @input="handleInputArea"
                    type="number"
                  >
                  </el-input>
                  <span style="margin-left: 10px">m²</span>
                </div>
                <div class="flex">
                  <div class="plan-label">白蚁活动痕迹分布图:</div>
                  <div class="plan-content flex-1">
                    <div class="trace-upload-tip">文件不超过100M</div>
                    <task-upload
                      ref="tracePictures"
                      list-type="picture-card"
                      accept=".jpg,.png,.jpeg"
                      module="operationManagement"
                      :showTip="false"
                      :readonly="false"
                      :file-list.sync="detailRecord.tracePictures"
                    >
                    </task-upload>
                  </div>
                </div>
              </div>

              <div class="termite-plan-right">
                <div class="plan-label">
                  附件（防治方案、委托白蚁防治公司相关文件）:
                </div>
                <div class="plan-content">
                  <task-upload
                    ref="planFiles"
                    list-type="table"
                    module="operationManagement"
                    :showTip="true"
                    accept=".pdf,.doc,.docx,.wps,.xls,.xlsx,.rar,.zip"
                    tip="*支持扩展名：pdf、doc、docx、wps、xls、xlsx、rar、zip"
                    :file-list.sync="detailRecord.planFiles"
                    :limitSize="50"
                  >
                  </task-upload>
                </div>
              </div>
            </div>

            <div class="termite-record flex-column flex-1">
              <BasicMsgTitle title="白蚁防治记录"></BasicMsgTitle>
              <div class="record-content flex flex-1">
                <free-table
                  :data="detailCheckList"
                  :column="detailTableColumns"
                  :isDisposeTableAlign="true"
                  height="100%"
                  showPagination
                  background
                  :total="detailPagination.total"
                  :page-size="detailPagination.limit"
                  :current-page.sync="detailPagination.page"
                  @page-change="loadCheckList"
                >
                  <template #indexHeader>
                    <span>
                      序号
                      <el-button
                        type="primary"
                        class="table-add el-icon-plus"
                        @click="handleRecordEdit('add')"
                      ></el-button>
                    </span>
                  </template>
                  <template #index="{ $index }">
                    {{ $index + 1 }}
                  </template>
                  <template #monitorCategory="{ row }">
                    {{ findDictName(row.monitorCategory) }}
                  </template>

                  <template #termiteAppearence="{ row }">
                    {{ row.termiteAppearence ? "有" : "无" }}
                  </template>

                  <template #governanceSituation="{ row }">
                    {{
                      row.governanceSituation === 0
                        ? "未治理"
                        : row.governanceSituation === 1
                        ? "治理中"
                        : "已完成"
                    }}
                  </template>
                  <template #termiteTrack="{ row }">
                    <div v-if="row.termiteTrackPictrues">
                      <el-image
                        class="file-item"
                        v-for="file in row.termiteTrackPictrues"
                        :key="file.id"
                        :src="previewUrl + file.file_path"
                        :description="file.mc"
                        :preview-src-list="[previewUrl + file.file_path]"
                        :z-index="3000"
                      >
                      </el-image>
                      <!-- <el-link
                class="file-item"
                v-for="file in row.wxyhhFileList"
                :key="file.id"
                @click="handlePreview(file)"
                ><img class="table-img" :src="previewUrl + file.file_path"
              /></el-link> -->
                    </div>
                    <!-- termiteTrackPictrues -->
                    <!-- <ul class="termite-track">
                  <li v-for="img in row.termiteTrackPictrues" :key="img.id">
                    <img :src="previewUrl + img.file_path" alt="" />
                  </li>
                </ul> -->
                  </template>
                  <template #actions="{ row }">
                    <template v-if="row.monitorCategory !== '1'">
                      <el-button
                        type="text"
                        class="el-icon-edit"
                        @click="handleRecordEdit('edit', row)"
                        >编辑</el-button
                      >
                      <el-button
                        type="text"
                        class="el-icon-delete"
                        @click="handleDelete(row.id)"
                        >删除</el-button
                      >
                    </template>
                  </template>
                </free-table>
              </div>
            </div>
          </div>
        </div>
        <record-dialog
          v-show="showDetail"
          ref="Dialog"
          @refresh="loadCheckList"
        ></record-dialog>
      </template>
    </ProjectTree>

    <MonitorDevice v-show="activeTab === 'monitorDev'" />
  </div>
</template>

<script>
import FreeTable from "@/components/elCommon/freeTable";
import BasicMsgTitle from "@/components/BasicMsgTitle";
import RecordDialog from "./recordDialog.vue";
import TaskUpload from "@/components/elCommon/taskUpload.vue";
import { mapState } from "vuex";
import { previewUrl } from "@/api/attachment";
import {
  list,
  details,
  save,
  checkList,
  deleteCheckById,
  detailCheck,
  getCharts,
  overviewList,
} from "@/api/OperationManagement/termiteControl";
import ProjectSearch from "@/components/basic/ProjectSearch.vue";
import { getStorage } from "@/utils/storage";
import * as echarts from "echarts";
import ProjectTree from "@/components/ProjectTree";
import CustomCard from "@/components/CustomComponents/CustomCard";
import MonitorDevice from "./MonitorDevice.vue";
import { getDictionaryTreeList } from "@/api/system/dict";
/* 白蚁防治 */
export default {
  name: "TermiteControl",
  props: {
    proMsg: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    FreeTable,
    RecordDialog,
    TaskUpload,
    BasicMsgTitle,
    ProjectSearch,
    ProjectTree,
    CustomCard,
    MonitorDevice,
  },
  data() {
    return {
      activeTab: "preventionRecord",
      isDetail: false,
      previewUrl,
      showDetail: true,
      tableData: [],
      tableColumns: [],
      areaColumns: [
        { label: "序号", type: "index", width: 60 },
        { label: "工程名称", prop: "pjName" },
        // { label: "工程规模", prop: "pjScale" },
        // { label: "行政区划", prop: "xzqh" },
        // {
        //   prop: 'projectType',
        //   label: '工程类型',
        //   render: (h, { row }) => {
        //     let text = "";
        //     if (row.projectType === 1) {
        //       text = "水库"
        //     } else if (row.projectType === 2) {
        //       text = "水闸"
        //     } else if (row.projectType === 3) {
        //       text = "堤坝"
        //     }
        //     return <span>{text}</span>
        //   }
        // },
        {
          label: "白蚁防治计划",
          prop: "termitePlan",
          align: "center",
          render: (h, { row }) => {
            if (row.termitePlan) {
              return <i class="table-icon el-icon-success" color="#1684FC"></i>;
            }
            return "";
          },
        },
        {
          label: "白蚁检查记录",
          prop: "termiteRecord",
          align: "center",
          render: (h, { row }) => {
            if (row.termiteRecord) {
              return <i class="table-icon el-icon-success" color="#1684FC"></i>;
            }
            return "";
          },
        },
        {
          label: "活动轨迹",
          prop: "termiteTrack",
          align: "center",
          render: (h, { row }) => {
            if (row.termiteTrack) {
              return <i class="table-icon el-icon-success" color="#1684FC"></i>;
            }
            return "";
          },
        },
        // { label: "操作", prop: "actions", slotScope: true, width: 120 },
      ],
      saveColumns: [
        { label: "序号", type: "index", width: 60 },
        {
          prop: "areaName",
          label: "市",
        },
        {
          prop: "projectTotal",
          label: "工程总数量",
        },
        {
          prop: "allDoneNum",
          label: "全部完成工程数",
        },
        {
          label: "无白蚁防治计划工程数",
          prop: "termitePlan",
          align: "center",
        },
        {
          label: "无白蚁检查记录工程数",
          prop: "termiteRecord",
          align: "center",
        },
        {
          label: "无活动轨迹工程数",
          prop: "termiteTrack",
          align: "center",
        },
      ],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
      },
      detailRecord: {},
      detailPagination: {
        total: 0,
        page: 1,
        limit: 10,
      },
      detailTableColumns: [
        {
          label: "序号",
          prop: "index",
          width: 80,
          slotScope: true,
          slotScopeHeader: true,
        },
        { label: "检查人员", prop: "checkUserName" },
        { label: "监测类别", prop: "monitorCategory", slotScope: true },
        { label: "检查日期", prop: "checkTime" },
        { label: "治理日期", prop: "governanceTime" },
        {
          label: "是否存在明显白蚁蚁害现象",
          prop: "termiteAppearence",
          slotScope: true,
        },
        { label: "治理情况", prop: "governanceSituation", slotScope: true },
        { label: "活动轨迹", prop: "termiteTrack" },
        { label: "操作", prop: "actions", slotScope: true },
      ],
      detailCheckList: [],
      projectName: null,
      form: {},
      formColumns: [],
      btnColumns: [],
      dictMonitorList: [],
    };
  },
  computed: {
    ...mapState("user", ["userInfo"]),
    ...mapState({
      projectInfo: (state) =>
        Object.keys(state.project.projectInfo).length === 0
          ? getStorage("std-projectInfo")
          : state.project.projectInfo,
      areaCode: (state) => state.project.areaCode,
      isSingleProject: (state) =>
        state.project.isSingleProject || getStorage("std-isSingleProject"),
    }),
    btnShow() {
      return !(this.userInfo.assignProjectIdList.length > 0);
    },
    title() {
      let text = "";
      if (this.userInfo.assignProjectIdList?.length) {
        text = this.projectInfo?.projectName;
      }
      return text;
    },
    findDictName() {
      return (value) => {
        const target = this.dictMonitorList.find(
          (item) => item.value === value
        );
        return target ? target.label : "";
      };
    },
  },
  watch: {
    proMsg: {
      // 用于接收“综合评分”中工程详情弹窗传递的数据
      handler(val) {
        if (val && JSON.stringify(val) !== "{}") {
          this.isDetail = true;
          this.$nextTick(() => {
            this.getProjectMsg(val);
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.getDict();
  },
  mounted() {},
  methods: {
    async getDict() {
      const rootRes = await getDictionaryTreeList({ parentId: -1 });
      if (rootRes.data) {
        const pid = rootRes.data.find(
          (i) => i.dictCode === "TERMITE_MONITORING_CATEGORY"
        )?.id;
        if (pid) {
          getDictionaryTreeList({ parentId: pid }).then((res) => {
            if (res.status === 200) {
              this.dictMonitorList = res.data.map((item) => {
                return {
                  label: item.dictName,
                  value: item.dictValue,
                };
              });
            }
          });
        }
      }
    },
    loadData(projectType) {
      this.showDetail = false;
      if (this.userInfo?.areaCode.length === 6) {
        list({
          ...this.form,
          pageNum: this.pagination.page,
          pageSize: this.pagination.limit,
          appKey: this.projectInfo?.appKey,
          xzqh: this.areaCode.length
            ? this.areaCode[this.areaCode.length - 1]
            : "",
          projectType,
        }).then((res) => {
          if (res.status === 200) {
            this.tableColumns = this.areaColumns;
            this.tableData = res.data?.list || [];
            this.$set(this.pagination, "total", res.data.total || 0);
          }
        });
      } else {
        overviewList({
          projectType,
        }).then((res) => {
          if (res.status === 200) {
            if (this.userInfo?.areaCode.length === 4) {
              // 市级用户
              this.saveColumns?.splice(1, 1, {
                prop: "areaName",
                label: "区（县）",
              });
              this.tableColumns = this.saveColumns;
            } else {
              //省级用户
              this.tableColumns = this.saveColumns;
            }
            this.tableData = res?.data || [];
          }
        });
      }
      if (!this.isSingleProject) {
        getCharts({ projectType })
          .then((res) => {
            if (res.status === 200) {
              this.$nextTick(() => {
                this.diagram({
                  completed: res.data["已完成"],
                  incomplete: res.data["未完成"],
                });
                this.getChart([
                  res.data["水库"],
                  res.data["水闸"],
                  res.data["堤防"],
                ]);
              });
            }
          })
          .catch((e) => {
            console.log(e);
          });
      }
    },
    handleInputArea(value) {
      const reg = /^(\d*\.?\d{0,3}|\.\d{0,3})/; // 小数点后三位
      const match = value.match(reg);
      if (match) {
        this.detailRecord.area = match[0];
      } else {
        this.detailRecord.area = "";
      }
    },
    rowDetail(row) {
      // this.projectName = row.pjName

      this.showDetail = true;
      details({ wrpcd: row.id }).then((res) => {
        if (res.status === 200) {
          this.detailRecord = { ...res.data, termiteId: row.id };
          this.loadCheckList();
        }
      });
    },
    loadCheckList() {
      checkList({
        wrpcd: this.detailRecord.termiteId,
        pageNum: this.detailPagination.page,
        pageSize: this.detailPagination.limit,
      }).then((res) => {
        if (res.status === 200) {
          this.detailCheckList = res.data.list || [];
          this.$set(this.detailPagination, "total", res.data.total || 0);
        }
      });
    },

    handleRecordEdit(type, row = {}) {
      if (type === "edit") {
        detailCheck({ id: row.id }).then((res) => {
          this.$refs.Dialog.show(
            type,
            Object.assign({}, res.data, {
              termiteId: this.detailRecord.termiteId,
            })
          );
        });
      } else {
        this.$refs.Dialog.show(type, {
          wrpcd: this.detailRecord.termiteId,
        });
      }
    },

    handleDelete(id) {
      this.$confirm("是否删除选择数据", "提示", {
        type: "warning",
      }).then(() => {
        deleteCheckById({ id }).then((res) => {
          if (res.status === 200) {
            if (this.detailPagination.page > 1 && this.tableData.length === 1) {
              this.detailPagination.page -= 1;
            }
            this.loadCheckList();
          }
        });
      });
    },

    back() {
      this.showDetail = false;
    },
    confirm() {
      save(this.detailRecord).then((res) => {
        if (res.status === 200) {
          this.$message.success("保存成功");
          // this.loadData();
          this.rowDetail({ id: this.projectInfo.wrpcd });
        }
      });
    },
    loadDataFn(options) {
      this.form = options;
      if (!this.form.areaCode) {
        this.form.areaCode = this.userInfo.areaCode;
      }
      this.pagination.page = 1;
      this.loadData();
    },
    // 饼图
    diagram(item) {
      const chartDom = document.getElementById("pie1");
      const myChart = echarts.init(chartDom);
      const data = [
        { name: "已完成", value: item.completed || 0 },
        { name: "未完成", value: item.incomplete || 0 },
      ];
      const option = {
        title: {
          show: false,
        },
        tooltip: {
          show: false,
          formatter: "{b} <br> {c}%",
        },
        legend: {
          show: false,
        },
        xAxis: [
          {
            type: "value",
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            splitLine: {
              show: false,
            },
          },
        ],
        yAxis: [
          {
            //type: 'category',
            data: [""],
            axisTick: {
              show: false,
            },
            axisLine: {
              show: false,
            },
            axisLabel: {
              textStyle: {
                color: "#fff",
              },
            },
          },
        ],
        series: [
          {
            name: "已完成",
            type: "bar",
            barWidth: 16,
            stack: "维护工作情况",
            label: {
              normal: {
                borderWidth: 10,
                distance: 50,
                align: "center",
                verticalAlign: "middle",
                borderRadius: 1,
                borderColor: "#CFE3FF",
                backgroundColor: "#CFE3FF",
                show: true,
                position: "top",
                formatter: function (params) {
                  let per = Math.floor(
                    (item.completed / (item.completed + item.incomplete)) * 100
                  );
                  return (
                    "{c|}" +
                    "{a| " +
                    params.seriesName +
                    "}" +
                    "\t\t" +
                    "{b| " +
                    item.completed +
                    "}" +
                    "\n\n" +
                    "{a| " +
                    "占比" +
                    "}" +
                    "\t\t" +
                    "{b| " +
                    per +
                    "%" +
                    "}"
                  );
                },
                // 自定义样式
                rich: {
                  a: {
                    // 颜色设置
                    color: "#222F40",
                    fontSize: 16,
                  },
                  b: {
                    color: "#1C6FE3",
                    fontSize: 20,
                    fontWeight: "bold",
                  },
                  c: {
                    width: 10,
                    height: 10,
                    backgroundColor: "#1C6FE3",
                  },
                },
                color: "#1C6FE3",
              },
            },
            itemStyle: {
              color: "#1C6FE3",
            },
            markPoint: {
              symbol: "rect",
              // x:'0%',
              symbolSize: [28, 28],
              symbolOffset: [-190, 0],
              label: {
                show: false, // 设置为 false，隐藏 markPoint 的文字
              },
              data: [
                {
                  name: "已完成",
                  type: "max",
                  symbol:
                    "image://" +
                    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAABINJREFUSEulln2I1GUQxz/f3fWyDKNQK7GCiiwTDUx3Vw0VL7GUE6PAPwwRDnR/eyplEmagZEpYQqi7p5SgvYkXCWov6pVclLe/O718CyyyF5BKyrMkxZd1d7pn3V3uvNs9TwcWfuwzM9+ZZ74zz4gyMn6Zhc7fybOCaozxQLCkuvGDBdhsGd5prlFrKT2VOogmbLSJDcDQvI4BPwPHgN8FGYO7gYeBwe2COYNY7M9lPZKz6SCdAc0UXs/LMpYDIeAfWQ54Yyqu410FODJhdwXFLGA+MDCv82nvizzf8IL+bW/TCTCSsLWImrzSB6pgQapap8tdfeFsxAa7pVeWpRgvAQHgYDrIhJY5OlPQ6QAYTtgCibeBrGBhypP77rFEa226GVuAm4Bd6SBVLXOUdo6KgOGETZD40kUmMS8V07oeI7UziK6ziRbgc6Ci7bfS97SkCPhcnQVPnOIo8IjBe02eXD1uWCIJW4J4HUhjDPfjOpbLMJKwZxCf5AhSwYPXWrPuIhpSZxV9/+YwyjF5o++p+gpg0nYCUxEr/Jhe7c5RT84jSXO3tQk409sYpPEJu/WC+A+4HISh+zz92BOHBd3wGhukEK8py4epGn3VgbkZfnLtIjFZ0aSNM2iQcSAV18jrAcv34dfAQ8AO39O09n4iSXsfmIlIKpwwTyLRRpitvqcZHQCXWSA8gOqgcbAxrv1dBeMyC4TYZfCo4wBZKv0afdcBMGHLEEvbAvpI4aStFCzGWO3H5Rq2KPlW2QtcyIopzTG576KMWWsDM0FcZg8CpwLGk41xHbo6sHDSZgpcllsVSdpq4EUZb6TiWtxeedib1ufmPnwreAw4lxVVBdDRtTYgazS4VmobaaclKlMxHezyFmpthtwgMOpcDRcZrEJs8mOa3Sm6NdZXIeqBUcB5xNOW5riC1Ofp3kqWKX6NmkrVP1xrC2W8ZWKLy7AK2I5R78c1qSujsUm7PQ1785k6Rv8FPAC0ZgNMbJ6rw+XIFklaEogBm5Wn84lCnzTEdbYr4yfWWP9LIfbkQTFwr0Blk6eW7pgdTdh+E49jzC40vhtrQxGeH1NtKQcu08uwu21q3GMw9VrA2hHvcsa4vwD4CrACcdSPaVi5iN3cPXuS0BfzdbG7zHJTLGHbENOBfb6nsTnAfOS/ArcZ1DR5cn15wxJJWiWwJ/cqGVV+XDuLz1ORrXAuICKNMX1/I4gjNli/XhkOAPcZ7G7yNNn5KwK6henCAHYATzkWBsTE6wXNjzo3T4cAJxFRP6bfOgAWrjYD37gx5VhoMLPZ02c9yTRaa6PM+Bi41w0LGZNScTUWfHTaaaLv2h12CfdcjQbHfrZJrErF1FwOeEzSBmdgEeSWKbd8tcqoag/WKcOCQ3e9F/uz3MTCNvb2yv9/xNUiAIdM/CHDskZ/ieGQ21ldgG5xcmHWp0PMapmjP68OsuRe6hRHrbfhgWxuXXQFLwCXS/QI5vYX6rraSUtmeLVHR4JQgGlZGCe39Br93OKrK3X+BWhWhu2peXIDpKyUzbA74+s5/x/OiLnTtfQRoAAAAABJRU5ErkJggg==",
                },
              ],
            },
            data: [
              {
                value: item.completed || 0,
                itemStyle: {
                  normal: {
                    color: {
                      type: "bar",
                      colorStops: [
                        {
                          offset: 0,
                          color: "#1C6FE3", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#1C6FE3", // 100% 处的颜色
                        },
                      ],
                      globalCoord: false, // 缺省为 false
                    },
                  },
                },
              },
            ],
          },
          {
            name: "已完成",
            type: "line",
            barWidth: 0,
            markPoint: {
              symbol: "triangle",
              symbolRotate: "180",
              itemStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#1C6FE3", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#1C6FE3", // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
              },
              symbolSize: [6, 5], // 容器大小
              symbolOffset: [0, -15], //位置偏移
              data: [
                {
                  coord: [53.11 / 2],
                },
              ],
              label: {
                normal: {
                  show: false,
                },
                offset: [0, 0],
              },
            },
          },
          {
            name: "未完成",
            type: "bar",
            barWidth: 16,
            stack: "维护工作情况",
            itemStyle: {
              color: "#FF9553",
            },
            label: {
              normal: {
                borderWidth: 10,
                distance: 50,
                align: "center",
                verticalAlign: "middle",
                borderRadius: 1,
                borderColor: "#FFE1CF",
                backgroundColor: "#FFE1CF",
                show: true,
                position: "top",
                formatter: function (params) {
                  let per = Math.ceil(
                    (item.incomplete / (item.completed + item.incomplete)) * 100
                  );
                  return (
                    "{c|" +
                    "}" +
                    "{a| " +
                    params.seriesName +
                    "}" +
                    "\t\t" +
                    "{b| " +
                    item.incomplete +
                    "}" +
                    "\n\n" +
                    "{a| " +
                    "占比" +
                    "}" +
                    "\t\t" +
                    "{b| " +
                    per +
                    "%" +
                    "}"
                  );
                },
                // 自定义样式
                rich: {
                  a: {
                    // 颜色设置
                    color: "#222F40",
                    fontSize: 16,
                  },
                  b: {
                    color: "#FF9553",
                    fontSize: 20,
                    fontWeight: "bold",
                  },
                  c: {
                    width: 10,
                    height: 10,
                    backgroundColor: "#FF9553",
                  },
                },
                color: "#FF9553",
              },
            },
            data: [
              {
                value: item.incomplete || 0,
                itemStyle: {
                  normal: {
                    color: {
                      type: "bar",
                      colorStops: [
                        {
                          offset: 0,
                          color: "#FF9553", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#FF9553", // 100% 处的颜色
                        },
                      ],
                      globalCoord: false, // 缺省为 false
                    },
                  },
                },
              },
            ],
            markPoint: {
              symbol: "rect",
              // x:'0%',
              symbolSize: [28, 28],
              symbolOffset: [30, 0],
              label: {
                show: false, // 设置为 false，隐藏 markPoint 的文字
              },
              data: [
                {
                  name: "未完成",
                  type: "max",
                  symbol:
                    "image://" +
                    "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAAAXNSR0IArs4c6QAABCNJREFUSEu1lmtoXFUQgL+5u2liQqMSSqBK1RQUi31YqFRKwEdFQVJN7m40rY+IYh67WaGgtARkFVGkP6ppdtMgmmpR2+wjqUpDUaFof1itpraitr7FolFa0dJtjXvvmHs30Wy62d2k9vy63DMz35w5c2ZGKLC03z+HX63bQXwIq4BLAO8ktT+AL0B2Y6S3S9vg986edpshlCPSkdgzGSHT8TR8g5d5VQGQx0DnF3JsfD+NOmD9GOUJlJclmGguCNSe+muwjZeAFePCp0AHwRjCI8Ok0yP8rRZlJRVY1kKElSD3AIunOPaVBBJX5gVqpP42MJLABcBJoAvLeF5Csd/ynVIVIep7GHRrllypUSUPxU5M/MsKqfbU34Rt7AZKgWEwGiUQ+7qYcGpfcxmpk58AV2fJ2/ikI5E4C6hRswbVAyAXA+9hldVJ6NU/i4G5SbLp3grKUxtRqUS0YtyOhVqtEhw4ngXUcNhg3qF9INcDR7HKVuSD6Ytr5nKmdKEEYgeLcUi71lVy4lhKwnvTbkg10uADiTmfGMZKaYt9mPe+ImYfcB8YVxUKuXY1XIpHPgcOSyCxSjKXbR4AloO+IoHk/YW81qgZRzExWC5tieG8zvX6F5C2vwU8qDaIbvEvxrA/BQTLXiahAec775oJMBNBc3AsCe9AdIdoxNwwlpXPgO6TQLK2EMw1MIMTuvI95rXY7AGJOsAMXaVTgvGnzwfQhYbDhoTDtgN04nvFWHaulUDi9fMG7Pd7OJ6ucoCjQAkqqyUYf3dGQNUGRL/M0rHKj+V6UhoxNwHrHWDazSBbbpSO+N4igY+7xTnnyp0LGjHfBlaLdps/ICxApUmC8R1FAd2H710PsgQw/tMRRfUtCSacwp+1NGJ+A9Q4J3wDqENkg7THny0GOBsZjZjOe13iADvHGupTwDsSSNwyG2PF6Gh3fRVeb7VoxL8M7Ey18BqXSUvsx2IMzFZGMoX78GfjbaVbAomO2RrLpaeb77yIEiMI2i/BgaMTxbsZxCnINmIskvbYkf8LqhHzNaDJLWvtyaYMMDO/7M8UcD7CStVKaOivc4VO6kK4hTuYHPi34+tW/yIs+wNgLrAL7+haaXkzdS5Q7fZ1IdqBah+B5IMiaPaIscV0Ws5OtxDAfiz1SSj502yh7v3NMW6lvHKXPLDtjGPnrDFRI751oNsys6f+PtaSn+R0xQvy6PZThcDaVb8Uj7HRnYms1N25riXnXKpRXy3qQmsyEBkB3Qn2ELZ1iOrSEWmMWZMd0KhvM6oht/Ioo5To5dKS/Hmqk9MPwr115VglnagEgcopiqdRvkOMmyUQ+8VNvIjpjCXOHDuMyCPSHn8/V0SmBU4Ia6//QizrLlQagevGk8rZTuMxlkprzJlX0OfWVFNaMp/WxEEnOaYL/z/3hLf/XglE9wAAAABJRU5ErkJggg==",
                },
              ],
            },
          },
          {
            name: "未完成",
            type: "line",
            barWidth: 0,
            markPoint: {
              symbol: "triangle",
              symbolRotate: "180",
              itemStyle: {
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#FF9553", // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#FF9553", // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
              },
              symbolSize: [6, 5], // 容器大小
              symbolOffset: [0, -15], //位置偏移
              data: [
                {
                  coord: [53.11 + 23 / 2],
                },
              ],
              label: {
                normal: {
                  show: false,
                },
                offset: [0, 0],
              },
            },
          },
        ],
      };
      option && myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart?.resize();
      });
    },
    // 柱形图
    getChart(data) {
      const key = ["水库", "水闸", "堤防"];
      const list = data?.map((item, index) => {
        return {
          活动轨迹: item["活动轨迹"] || 0,
          白蚁检查记录: item["白蚁检查记录"] || 0,
          白蚁防治计划: item["白蚁防治计划"] || 0,
          product: key[index],
        };
      });
      const result = {};

      data?.forEach((obj) => {
        for (const key in obj) {
          if (result.hasOwnProperty(key)) {
            result[key] += obj[key] || 0;
          } else {
            result[key] = obj[key] || 0;
          }
        }
      });
      const chartDom = document.getElementById("pie2");
      const myChart = echarts.init(chartDom);
      let option = {
        color: ["#3388FF", "#47DEA0", "#FF9553"],
        legend: {
          orient: "vertical",
          left: 10,
          top: 20,
          itemWidth: 5, // 设置图例项的宽度为20像素
          itemGap: 20, // 设置图例项之间的间隔距离为30像素
          // 关键的在这
          formatter: function (name) {
            return name + "{value|" + result[name] + "}";
          },
          data: ["活动轨迹", "白蚁检查记录", "白蚁防治计划"],
          textStyle: {
            color: ["#3388FF", "#47DEA0", "#FF9553"], // 每个图例的文字颜色
            rich: {
              name: {
                fontSize: 12,
                color: "#000",
              },
              value: {
                fontSize: 16,
                fontWeight: "bold",
                padding: [0, 5, 0, 15],
              },
            },
          },
        },
        tooltip: {},
        grid: {
          show: false,
          top: "12%",
          right: "5%",
          bottom: "10%",
          left: "24%",
        },
        dataset: {
          dimensions: ["product", "活动轨迹", "白蚁检查记录", "白蚁防治计划"],
          source: list,
        },
        xAxis: { type: "category" },
        yAxis: {},
        series: [
          {
            type: "bar",
            barWidth: "10%",
            label: {
              show: true,
              position: "top",
              color: "#3388FF",
              fontSize: 18,
              fontWeight: 400,
              offset: [0, 0],
            },
          },
          {
            type: "bar",
            barWidth: "10%",
            label: {
              show: true,
              position: "top",
              color: "#47DEA0",
              fontSize: 18,
              fontWeight: 400,
              offset: [0, 0],
            },
          },
          {
            type: "bar",
            barWidth: "10%",
            label: {
              show: true,
              position: "top",
              color: "#FF9553",
              fontSize: 18,
              fontWeight: 400,
              offset: [0, 0],
            },
          },
        ],
      };
      option && myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart?.resize();
      });
    },
    getProjectMsg(val) {
      if (val.id) {
        this.detailPagination.page = 1;
        this.rowDetail(val);
      }
    },
    goBack() {
      this.showDetail = false;
    },
    checkTypeChange(val) {
      // console.log("val",val);
      // if (!this.isSingleProject) {
      //   this.loadData(val);
      // }
      this.rowDetail({ id: this.projectInfo.wrpcd });
    },
  },
};
</script>

<style lang="scss" scoped>
@import url(./index.scss);
</style>
