/* 安全管理-预警规则 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  sectionListAndReservoir: apiUrl.defaultUrl + '/section/getSectionListAndReservoir',
  warnRuleList: apiUrl.defaultUrl + '/warningRule/warnRuleList',
  setWarnRule: apiUrl.defaultUrl + '/warningRule/setWarnRule',
  exportWarnRule: apiUrl.defaultUrl + '/warningRule/exportWarnRule',
  getYjUsers: apiUrl.defaultUrl + '/warningRule/getYjUsers',
  queryStbprpList: apiUrl.defaultUrl + '/warningRule/queryStbprpList',
  addWarnRule: apiUrl.defaultUrl + '/warningRule/addWarnRule',
  deleteWarnRule: apiUrl.defaultUrl + '/warningRule/deleteWarnRule',
  getForecastWatershed: apiUrl.defaultUrl + '/jz/sy/getForecastWatershed',
}

/**
 * 预警规则-列表
 */
export function warnRuleList (data) {
  return $http.post(api.warnRuleList, data)
}

/**
 * 预警规则-设置
 */
export function setWarnRule (data) {
  return $http.post(api.setWarnRule, data)
}

/**
 * 预警规则-导出
 */
export function exportWarnRule (data) {
  return $http.postDownLoad(api.exportWarnRule, data, '', 'arraybuffer')
}

/**
 * 预警规则-预警通知人
 */
export function getYjUsers (params) {
  return $http.get(api.getYjUsers, params)
}
/**
 * 预警规则-测站列表
 */
export function queryStbprpList (params) {
  return $http.post(api.queryStbprpList, params)
}

/**
 * 预警规则-预报区间列表
 */
export function getForecastWatershed() {
  return $http.get(api.getForecastWatershed)
}

/**
 * 预警规则-预报区间列表
 */
export function getSectionListAndReservoir() {
  return $http.get(api.sectionListAndReservoir)
}

/**
 * 预警规则-新增预警规则
 */
export function addWarnRule (params) {
  return $http.post(api.addWarnRule, params)
}
/**
 * 预警规则-删除预警规则
 */
export function deleteWarnRule (params) {
  return $http.post(api.deleteWarnRule, params)
}
