/* 报表-大坝安全 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  waterLevelList: apiUrl.defaultUrl + '/mdc/getStageReportForms',
  waterLevelExport: apiUrl.defaultUrl + '/mdc/waterLevelStationDownExcel',
  getStageReportCharts: apiUrl.defaultUrl + '/mdc/getStageReportCharts'
}

/**
 * 水位列表查询
 */
export function waterLevelList (params) {
  return $http.get(api.waterLevelList, params)
}

// 水位 导出
export function waterLevelExport (params) {
  return $http.getDownLoad(api.waterLevelExport, params, undefined, 'blob')
}

/**
 * 水位列表查询
 */
export function getStageReportCharts (params) {
  return $http.get(api.getStageReportCharts, params)
}
