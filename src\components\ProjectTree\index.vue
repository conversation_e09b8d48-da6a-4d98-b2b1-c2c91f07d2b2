<template>
  <div :class="['pro-tree-container', isCollapse ? 'open' : '']">
    <!-- <div class="full-w" v-if="!isSingle">
      <project-search @load="search"></project-search>
    </div> -->
    <div :class="['proList', isCollapse ? 'open' : '']" v-if="!isSingle">
      <div class="score-item-search mb20">
        <!-- <el-cascader
          class="cascader"
          placeholder="请选择行政区划"
          v-model="areaData"
          :options="cascaderOptions"
          :props="{ checkStrictly: true }"
          @change="handleCascaderChange"
          clearable
        >
        </el-cascader> -->
        <!-- <el-input
          class="nameIpt"
          clearable
          placeholder="请输入工程名称"
          prefix-icon="el-icon-search"
          @input="handleProjectNameChange"
          v-model="projectName"
        >
        </el-input> -->
      </div>
      <!-- 工程类型选择 -->
      <div class="flex pro-type-container mb20">
        <button type="button" :class="[projectTypeCheck.indexOf(val.value) !== -1 ? 'selected' : '']" v-for="val of screenCheckTypeData" :label="val.value" :key="val.value" @click="handleProTypeChange(val)">
          <img :src="val.src" />
          <span class="mr10 btn-name">{{ val.label }}</span>
          <span class="btn-num">{{ projectTypeNum[val.value] }}</span>
        </button>
      </div>
      <!-- <el-checkbox-group v-if="screenCheckTypeData.length" class="stdClass score-item-checkbox mb20"
        v-model="projectTypeCheck" @change="handleProjectTypeCheckboxChange">
        <el-checkbox v-for="val of screenCheckTypeData" :label="val.value" :key="val.value"
          @change="isChecked => handleProjectTyeChange(isChecked, val.value)">
          <div class="flex-vc mb10">
            <img :src="val.src" />
            <span>{{ val.label }}</span>
          </div>
          <span class="colorblue font28">{{ projectTypeNum[val.value] }}</span>
        </el-checkbox>
      </el-checkbox-group> -->
      <!-- <h4>工程列表</h4> -->
      <div class="projectList">
        <!-- <p v-for="v of projectList" :key="v.id" :class="appKey === v.appKey ? 'active' : ''" @click="clickPro(v)">{{
          v.projectName
        }}</p>
        <p v-if="projectList.length === 0">暂无数据</p> -->
        <el-tree ref="tree" :data="projectList" @node-click="handleClickNode" node-key="value" default-expand-all
          :filter-node-method="filterNode">
          <template slot-scope="{ node, data }">
            <div class="custom-node ellipsis-text flex-vc">
              <span class="custom-icon flex-vc">
                <i v-if="data.projectType" :class="['pro-type', 'pro-type' + data.projectType]"></i>
                <i v-else-if="!data.children || !data.children.length" class=""></i>
                <i v-else-if="node.expanded" class="el-icon-minus"></i>
                <i v-else class="el-icon-plus"></i>
              </span>
              <el-tooltip v-if="data.projectType" class="item" effect="dark" :content="node.label" placement="top-start">
                <span class="custom-label">{{ node.label }}</span>
              </el-tooltip>
              <span v-else class="custom-label">{{ node.label }}</span>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <img v-if="!isSingle" class="collapse-btn" src="@/assets/score/icon-collapse.png"
      @click="() => (isCollapse = !isCollapse)" />
    <div :class="['pro-tree-content', 'flex-column', isSingle ? '' : 'singleProject']">
      <div class="mb20" v-if="showBtn">
        <el-button type="primary" @click="goBack">返回总览</el-button>
      </div>
      <slot name="content" :data="{ appKey }"></slot>
    </div>
  </div>
</template>

<script>
// import ProjectSearch from '@/components/basic/ProjectSearch.vue'
import { querySortByProjectType } from '@/api/basic/project'
import { mapState } from 'vuex'
import { getStorage } from '@/utils/storage'
import { areaTree, getExistsPjAreaList } from '@/api/OperationMonitoring/waterAndRain'
import utils from '@/utils/utils'
export default {
  name: 'ProjectTree',
  props: {
    // 判断是否在“综合评分”的详情弹窗中调用
    isDetail: {
      type: Boolean,
      default: false
    },
    // 设置默认工程类型
    defaultType: {
      type: Array,
      default: () => [1, 2, 3]
    },
    module: {
      type: String,
      default: ''
    },
    method: {
      type: [Function, String],
      default: ''
    },
    // 是否显示“返回总览”按钮
    isShowBackBtn: {
      type: Boolean,
      default: true
    },
    // 是否默认选择第一个工程
    isSelectFirstPro: {
      type: Boolean,
      default: false
    }
  },
  components: {
    // ProjectSearch
  },
  data () {
    return {
      clickNodeKey: '',
      projectList: [],
      appKey: '',
      projectId: '',
      options: {},
      isSingle: false,
      checkTypeData: [
        {
          value: 1,
          label: '水库',
          src: require('@/assets/score/icon-sk.png')
        },
        {
          value: 2,
          label: '水闸',
          src: require('@/assets/score/icon-sz.png')
        },
        {
          value: 3,
          label: '堤防',
          src: require('@/assets/score/icon-df.png')
        }
      ],
      screenCheckTypeData: [],
      projectTypeCheck: [1, 2, 3],
      projectTypeMap: {
        1: '水库',
        2: '水闸',
        3: '堤防'
      },
      projectTypeNum: {
        1: 0,
        2: 0,
        3: 0
      },
      reservoirsCount: 0,
      waterLocksCount: 0,
      dikeCount: 0,
      projectName: '',
      cascaderOptions: [],
      areaCode: '',
      cancelCheckedNum: '',
      areaData: [],
      isCollapse: true,
      isFirst: true,
      fn: querySortByProjectType,
      firstPro: null
    }
  },
  computed: {
    ...mapState({
      isSingleProject: state => state.project.isSingleProject || getStorage('std-isSingleProject'),
      projectInfo: state => getStorage('std-projectInfo')
    }),
    showBtn () {
      return this.projectList[0] && this.clickNodeKey && !this.isSingle && this.isShowBackBtn && !this.isSelectFirstPro
    }
  },
  watch: {
    isSingleProject: {
      handler (val) {
        if (val) {
          this.handleClickNode(this.projectInfo)
          this.isSingle = true
        }
      },
      immediate: true
    },
    isDetail: {
      handler (val) {
        // 用于“综合评分”，工程详情弹窗
        if (val) {
          this.isSingle = true
        }
      },
      immediate: true
    },
    defaultType: {
      handler (val) {
        if (Array.isArray(val)) {
          this.screenCheckTypeData = []
          for (const val of this.checkTypeData) {
            if (this.defaultType.indexOf(val.value) !== -1) {
              this.screenCheckTypeData.push(val)
            }
          }
          if (this.screenCheckTypeData.length === 1) {
            this.projectTypeCheck = [this.screenCheckTypeData[0].value]
            this.screenCheckTypeData = []
          } else {
            this.projectTypeCheck = this.screenCheckTypeData.map(v => v.value)
          }
        }
      },
      immediate: true,
      deep: true
    },
    method: {
      handler (f) {
        if (typeof f === 'function') {
          this.fn = f
        }
      },
      immediate: true,
      deep: true
    }
  },
  created () {
    this.$emit('checkTypeChange', this.projectTypeCheck.join())
  },
  mounted () {
    if (!this.isSingleProject && !this.isDetail) {
      this.getProList()
    }
  },
  methods: {
    search (options) {
      this.options = options
      this.getProList()
    },
    // 获取树组件的数据
    getProList () {
      getExistsPjAreaList({
        projectType: this.projectTypeCheck.join(),
        module: this.module
      }).then(res => {
        if (res.status === 200) {
          this.projectList = res.data
          this.loopFormatTreeData(this.projectList)
          this.isFirst = false
        }
      })
    },
    /**
     * 树状列表的数据进行格式化
    */
    loopFormatTreeData (data) {
      if (Array.isArray(data) && data.length > 0) {
        for (let i = 0, num = data.length; i < num; i++) {
          if (data[i].projectType) {
            data[i].label = data[i].projectName
            data[i].value = data[i].id
            if (!this.firstPro && this.isSelectFirstPro) {
              this.firstPro = data[i]
              this.handleClickNode(data[i])
            }
          } else {
            data[i].label = data[i].areaName + '(' + data[i].projectNum + ')'
            data[i].value = data[i].areaCode
          }
          if (Array.isArray(data[i].childList) && Array.isArray(data[i].scoreProjectList)) {
            // 获取不同类型工程的数量
            if (this.isFirst) {
              for (const p of data[i].scoreProjectList) {
                this.projectTypeNum[p.projectType] += 1
              }
            }
            data[i].children = [...data[i].childList, ...data[i].scoreProjectList]
            this.loopFormatTreeData(data[i].children)
          }
        }
      }
    },
    // 选中对应的“行政区划”后的回调函数
    // handleCascaderChange (val) {
    //   this.areaCode = val[val.length - 1]
    //   this.getProList()
    // },
    handleProjectNameChange (val) {
      this.projectName = val.trim()
      // 调用防抖函数，防止输入过程中不断请求
      utils.throttle(() => {
        this.$refs.tree.filter(this.projectName)
      }, 500, this)
    },
    // 筛选树列表
    filterNode (value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    handleProTypeChange (val) {
      const index = this.projectTypeCheck.indexOf(val.value)
      if (index === -1) {
        this.projectTypeCheck.push(val.value)
        this.$emit('checkTypeChange', this.projectTypeCheck.join())
        this.getProList()
      } else {
        if (this.projectTypeCheck.length === 1) {
          this.$message.warning('请至少选择一种类型')
        } else {
          this.projectTypeCheck.splice(index, 1)
          this.$emit('checkTypeChange', this.projectTypeCheck.join())
          this.getProList()
        }
      }
    },
    // 检测checkbox group的回调函数
    // handleProjectTypeCheckboxChange (val) {
    //   if (val.length === 0) {
    //     this.$message.warning('请至少选择一种类型')
    //     this.projectTypeCheck = [this.cancelCheckedNum]
    //     return false
    //   }
    //   this.$emit('checkTypeChange', this.projectTypeCheck.join())
    //   this.getProList()
    // },
    // // 检测checkbox的回调函数
    // handleProjectTyeChange (isChecked, val) {
    //   this.cancelCheckedNum = isChecked ? null : val
    // },
    // 获取“行政区划”数据
    // getAreaTree () {
    //   areaTree({ }).then(res => {
    //     if (res.status === 200) {
    //       if (Array.isArray(res.data)) {
    //         this.cascaderOptions = res.data
    //         this.loopFormatTreeData(this.cascaderOptions)
    //         this.areaCode = this.cascaderOptions[0].areaCode
    //         this.areaData = [this.cascaderOptions[0].areaCode]
    //       }
    //     }
    //   }).catch(e => {
    //     console.log(e)
    //   })
    // },
    // 点击左侧列表中的工程
    handleClickNode (data) {
      if (data.projectType) {
        this.clickNodeKey = data.value
        this.appKey = data.appKey
        this.projectId = data.id
        this.$emit('getProjectMsg', data)
      }
    },
    // 返回总览
    goBack () {
      this.clickNodeKey = ''
      this.$emit('goBack')
      this.$refs.tree?.setCurrentKey(null)
    }
  }
}
</script>

<style lang="scss" scoped>
.pro-tree-container {
  height: 100%;
  display: flex;
  position: relative;

  &.open {
    .collapse-btn {
      left: 364px;
      transform: rotate(0deg);
    }
  }

  .collapse-btn {
    width: 16px;
    height: 22px;
    position: absolute;
    left: 0;
    top: 0;
    user-select: none;
    cursor: pointer;
    transition-duration: left;
    transition-property: 0.3s;
    transform: rotate(180deg);
  }
}

.proList {
  width: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  transition-property: padding-right, width;
  transition-duration: 0.3s;
  overflow: hidden;
  padding-right: 0;
  border-right: 1px solid #cccccc;

  &.open {
    padding-right: 30px;
    width: 350px;
  }

  h4 {
    text-align: center;
    height: 50px;
    line-height: 50px;
    width: 100%;
    font-size: 22px;
    font-weight: 700;
    color: #ffffff;
    background-color: #0a4ead;
  }

  .projectList {
    height: 0;
    flex: auto;
    overflow-y: auto;
    margin-top: 10px;
  }

  p {
    cursor: pointer;
    font-size: 20px;
    padding: 16px 0;
    display: flex;
    background-color: #e2ebf8;
    margin-bottom: 10px;

    &:nth-child(1) {
      border-top: none;
    }

    &::before,
    &::after {
      content: '';
      width: 10px;
    }

    &.active,
    &:hover {
      color: white;
      background-color: #78acf3;
    }
  }
}

.pro-tree-content {
  width: 0;
  flex: auto;
}

.singleProject {
  margin-left: 30px;
}

.score-item-search {
  display: flex;

  .cascader {
    flex: 1;
    margin-right: 10px;
  }

  .nameIpt {
    flex: 1;
  }
}

.score-item-checkbox {
  display: flex;
  justify-content: space-between;

  ::v-deep(.el-checkbox) {
    background-color: #ffffff;
    flex: 1;
    border-radius: 4px;
    height: 48px;
    @include flexHC;
    border: 1px solid #83ADE7;
    margin-left: 14px;
    margin-right: 0;
    padding: 12px 0;

    &:nth-child(1) {
      margin-left: 0
    }

    .el-checkbox__label {
      @include flexVC;
      flex-direction: column;
      color: #222F40;
      font-size: 18px;
    }

    .el-checkbox__inner {
      width: 22px;
      height: 22px;

      &::after {
        width: 5px;
        height: 12px;
        left: 6px;
      }
    }

    img {
      user-select: none;
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
}

.pro-type-container {
  button {
    flex: 1;
    cursor: pointer;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: 1px solid #1F7CD6;
    margin-left: 10px;
    padding: 0;
    display: flex;
    align-items: center;
    border-radius: 4px;
    height: 72px;
    border: 1px solid #0A4EAD;
    position: relative;
    color: #0A4EAD;
    font-size: 22px;
    flex-wrap: wrap;
    justify-content: center;
    background-image: none;
    background-color: #FFFFFF;
    &:nth-child(1) {
      margin-left: 0;
    }
    &.selected {
      background-color: transparent;
      background-image: linear-gradient(0deg, #ebf3ff, #c6deff);
      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 22px;
        height: 22px;
        background-size: 22px 22px;
        background-repeat: no-repeat;
        background-image: url('@/assets/score/btn-selected.png');
      }
    }
    img {
      user-select: none;
      width: 20px;
      margin: 0 10px;
    }
    .btn-name {
      color: #222F40;
    }

    .btn-num {
      width: 100%;
      font-weight: 700;
    }
  }
}

.pro-type {
  width: 20px;
  height: 20px;
  user-select: none;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 20px 20px;

  &1 {
    background-image: url('@/assets/score/icon-sk.png');
  }

  &2 {
    background-image: url('@/assets/score/icon-sz.png');
  }

  &3 {
    background-image: url('@/assets/score/icon-df.png');
  }
}

.el-tree {
  height: 100%;
  overflow: auto;

  .custom-node {
    font-size: 18px;
    font-weight: 500;
    color: #222F40;
    line-height: 42px;
    .custom-label {
      width: 240px;
      white-space: nowrap;
      /* 文字不换行 */
      overflow: hidden;
      /* 超出部分隐藏 */
      text-overflow: ellipsis;
      /* 显示省略号 */
    }

    .custom-icon i {
      margin-right: 12px;
    }

    .custom-icon i[class*="el-icon-"] {
      display: inline-block;
      width: 16px;
      height: 16px;
      color: #1064DA;
      border: 1px solid #1064DA;
      margin-right: 12px;
      text-align: center;
      line-height: 16px;
      font-size: 12px;
    }
  }
}

::v-deep(.el-tree-node__content) {
  height: auto !important;
}

::v-deep(.el-tree-node__expand-icon) {
  opacity: 0;
  position: absolute;
  width: 24px;
  height: 24px;
  box-sizing: border-box;
  padding: 0;
}

::v-deep(.el-tree-node.is-current) {
  background-color: #F0F5FC;

  &>.el-tree-node__content {
    background-color: transparent;
  }

  &>.el-tree-node__content .custom-node {
    color: #0A4EAD;
  }
}

.colorblue {
  color: #0A4EAD;
}
</style>
