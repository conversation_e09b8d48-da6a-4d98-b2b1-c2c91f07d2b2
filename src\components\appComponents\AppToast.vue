<template>
  <div v-if="toastObj.type" class="lotus-toast">
    <div class="lotus-toast-inner">
      <p v-text="toastObj.text"></p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'lotus-toast',
  props: ['lotusToastData'],
  data () {
    return {
      toastObj: {
        text: '',
        type: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
$lotusToast: lotus-toast;
.#{$lotusToast} {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 10001;
  background: rgba(255, 255, 255, 0);
  &-inner {
    position: absolute;
    left: 50%;
    top: 50%;
    background: rgba(0, 0, 0, 0.6);
    transform: translate(-50%, -50%);
    text-align: center;
    font-size: 18px;
    color: #fff;
    min-width: 80px;
    padding: 10px 10px;
  }
}
</style>
