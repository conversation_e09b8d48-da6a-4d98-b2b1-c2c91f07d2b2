<template>
  <div class="archives">
    <!-- <div class="archives-tab" v-show="!switchPage"> -->
    <!-- <project-search :columns="formColumns" :btn-columns="btnColumns" @load="(options) => loadDataFn(options)">
      </project-search> -->
    <!-- </div> -->
    <ProjectTree
      @getProjectMsg="getProjectMsg"
      @goBack="goBack"
      @checkTypeChange="checkTypeChange"
      :is-detail="isDetail"
    >
      <template #content>
        <!-- 工程档案总览 -->
        <!-- <div class="graphical" v-show="!switchPage">
          <div class="chart1" id="main1"></div>
          <div class="chart2" id="main2"></div>
        </div>
        <free-table
          class="overviewTable"
          v-show="!switchPage"
          ref="freeTable"
          :data="tableData"
          :column="tableColumn"
          height="100%"
          :showPagination="false"
          background
          paginationPosition="bottom"
          :total="pagination.total"
          :current-page.sync="pagination.page"
          @page-change="handleCurrentPageChange"
        >
        </free-table> -->

        <!-- 工程档案详情 -->
        <div class="flex flex-1 overflow-h">
          <div class="archives-type flex-column">
            <div class="archives-type-title">
              <p>档案分类</p>
              <el-button
                @click="createNewFolder"
                icon="el-icon-plus"
                type="text"
              >新增</el-button
              >
            </div>
            <el-tree
              ref="archivesTree"
              class="archives-type-container"
              :data="archivesType"
              node-key="id"
              @node-click="handleNodeClick"
              :props="{
                children: 'childList',
                label: 'docClassify',
              }"
            >
              <div class="archives-type-item" slot-scope="{ node, data }" @click.stop="nodeClick(node, data)">
                <p>
                  <span>{{ data.docClassify }}</span>
                  <span>({{ data.count }})</span>
                </p>
                <div class="archives-type-btns">
                  <el-button
                    @click.stop="handleEditFolderName(data)"
                    type="text"
                  ><i class="el-icon-edit-outline"></i
                  ></el-button>
                  <el-button
                    class="mr10"
                    @click.stop="handleAddFolder(data)"
                    type="text"
                  ><i class="el-icon-plus"></i
                  ></el-button>
                  <el-popconfirm
                    title="是否删除该档案分类？"
                    @confirm="handleDelFolder(node, data)"
                  >
                    <el-button @click.stop slot="reference" type="text"
                    ><i class="el-icon-delete"></i
                    ></el-button>
                  </el-popconfirm>
                </div>
              </div>
            </el-tree>
          </div>
          <div class="file-list">
            <ArchivesDetail
              ref="ArchivesDetail"
              :wrpcd="activeOne"
              :doc-id="activeTwo"
              @queryData="queryData"
            />
          </div>
        </div>
        <el-dialog
          class="std-theme center-dialog"
          :title="isEditFolderName ? '编辑' : '新增'"
          :visible="visible"
          :append-to-body="true"
          @close="cancelEditFolderName"
          align-center
          top="0"
        >
          <div class="">
            <p class="mb20">档案分类名称：</p>
            <el-input v-model="docClassify" maxlength="50" show-word-limit></el-input>
          </div>
          <div slot="footer">
            <el-button @click="cancelEditFolderName">取消</el-button>
            <el-button @click="submitFolderName" type="primary">提交</el-button>
          </div>
        </el-dialog>
      </template>
    </ProjectTree>
  </div>
</template>

<script>
/* 档案管理 */
import { mapState } from "vuex";
import {
  deleteById as delFolderById,
  save as saveFolder,
} from "@/api/basic/documentClassify";
import { getDocsOverview, getDocsByProjectId } from "@/api/basic/docFile";
import buildingTypeMap from "@/utils/buildingTypeMap";
import FreeTable from "@/components/elCommon/freeTable";
import ProjectTree from "@/components/ProjectTree";
import FormItemComp from "@/components/elCommon/searchForm/searchForm.vue";
import ArchivesDetail from "./components/ArchivesDetail.vue";
import { getStorage } from "@/utils/storage";

export default {
  // eslint-disable-next-line vue/multi-word-component-names
  name: "Archives",
  components: {
    FreeTable,
    ProjectTree,
    FormItemComp,
    ArchivesDetail,
  },
  data() {
    return {
      wrpcd: null,
      isDetail: false,
      code: "",
      buildingTypeMap,
      switchPage: false,
      tableData: [],
      tableColumn: [],
      saveColumn: [
        {
          type: "index",
          label: "序号",
          width: 55,
        },

        {
          prop: "areaName",
          label: "市",
        },
        {
          label: "工程总数量",
          prop: "projectTotal",
        },
        {
          label: "有档案工程数",
          prop: "hasDocProjectNum",
        },
      ],
      cityColumn: [
        {
          type: "index",
          label: "序号",
          width: 55,
        },

        {
          prop: "areaName",
          label: "区（县）",
        },
        {
          label: "工程总数",
          prop: "projectTotal",
        },
        {
          label: "有档案工程数",
          prop: "hasDocProjectNum",
        },
      ],
      areaColumn: [
        {
          type: "index",
          label: "序号",
          width: 55,
        },
        // {
        //   label: '行政区划',
        //   prop: 'address'
        // },
        {
          prop: "projectName",
          label: "工程名称",
        },
        // {
        //   prop: 'projectType',
        //   label: '工程类型',
        //   render: (h, { row }) => {
        //     let text = "";
        //     if (row.projectType === 1) {
        //       text = "水库"
        //     } else if (row.projectType === 2) {
        //       text = "水闸"
        //     } else if (row.projectType === 3) {
        //       text = "堤坝"
        //     }
        //     return <span>{text}</span>
        //   }
        // },
        // {
        //   label: '工程级别',
        //   prop: 'level'
        // },
        // {
        //   label: '工程地址',
        //   prop: 'addresss'
        // },
        {
          label: "已有文件数",
          prop: "count",
        },
        // {
        //   label: '操作',
        //   prop: 'actions',
        //   width: 90,
        //   // fixed: 'right',
        //   slotScope: true
        // }
      ],
      pagination: {
        total: 0,
        page: 1,
      },
      activeOne: "",
      engineeringList: [
        {
          id: 0,
          type: 0,
          name: "wwww",
          number: 3,
          address: "fdfd",
        },
        {
          id: 1,
          type: 1,
          name: "wwww",
          number: 3,
          address: "fdfd",
        },
        {
          id: 2,
          type: 2,
          name: "wwww",
          number: 3,
          address: "fdfd",
        },
      ],
      activeTwo: "",
      lastActiveId: "",
      archivesType: [],
      isEdit: false,
      update_obj: null,
      docClassify: "",
      isEditFolderName: false,
      visible: false,
      defaultExpandedKeys: [],
      projectName: null,
      form: {},
      formColumns: [],
      btnColumns: [],
      chartObj: {},
    };
  },
  created() {
    //   this.getAreaTree()
    //   if (this.userInfo) {
    //     this.form.areaCode = [this.userInfo.areaCode]
    //   }
  },
  mounted() {},
  beforeDestroy() {},
  computed: {
    ...mapState({
      projectInfo: (state) =>
        Object.keys(state.project.projectInfo).length === 0
          ? getStorage("std-projectInfo")
          : state.project.projectInfo,
      scoreProjectMsg: (state) => state.project.scoreProjectMsg,
      userInfo: (state) => state.user.userInfo,
      isSingleProject: (state) => state.project.isSingleProject,
    }),
    // btnShow() {
    //   return this.userInfo.assignwrpcdList.length > 0 ? false : true
    // },
    title() {
      let text = "";
      if (this.userInfo.assignwrpcdList?.length) {
        text = this.projectInfo?.projectName;
      }
      return text;
    },
  },
  watch: {
    scoreProjectMsg: {
      // 用于接收“综合评分”中工程详情弹窗传递的数据
      handler(val) {
        if (val && JSON.stringify(val) !== "{}") {
          this.isDetail = true;
          this.$nextTick(() => {
            this.getProjectMsg(val);
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    nodeClick(node, data) {
      node.expanded = !node.expanded
      this.handleNodeClick(data)
    },
    handleProjectClick(val, isChangeActiveTwo = true) {
      this.activeOne = val?.id;
      this.archivesType = val?.dcVoList || [];
      if (this.lastActiveId) {
        this.$nextTick(() => {
          this.$refs.archivesTree.setCurrentKey(this.lastActiveId);
          this.$refs.ArchivesDetail.search();
        });
      }
      // if (isChangeActiveTwo) {
      //   this.activeTwo = this.archivesType?.[0]?.id || "";
      // }
      // if (this.activeTwo) {
      //   this.$nextTick(() => {
      //     this.$refs.archivesTree.setCurrentKey(this.activeTwo);
      //     this.$refs.ArchivesDetail.search();
      //   });
      // } else {
      //   this.$refs.archivesTree.resetData();
      // }
    },

    async queryData() {
      await this.getProjectMsg({ id: this.projectInfo.wrpcd }, false);
      // await this.queryListData(false);
    },

    // 获取档案总览列表数据
    queryListData(isChangeActiveTwo = true, projectType) {
      this.engineeringList = [];
      this.archivesType = [];
      this.tableColumn = [];
      getDocsOverview(
        {
          projectType,
        },
        {
          // pageNum: this.pagination.page
        }
      )
        .then((res) => {
          if (res.status === 200) {
            this.chartObj = res.data?.chart || {};
            if (this.userInfo?.areaCode.length === 6) {
              // 区级用户
              this.tableColumn = this.areaColumn;
              this.engineeringList = res.data?.countyList || [];
              this.tableData = res.data?.countyList || [];
            } else if (this.userInfo?.areaCode.length === 4) {
              // 市级用户
              this.tableColumn = this.cityColumn;
              this.engineeringList = res.data?.provinceOrCityList || [];
              this.tableData = res.data?.provinceOrCityList || [];
            } else {
              // 省级用户
              this.tableColumn = this.saveColumn;
              this.engineeringList = res.data?.provinceOrCityList || [];
              this.tableData = res.data?.provinceOrCityList || [];
            }
            // this.$nextTick(() => {
            //   this.diagram1(res.data?.chart);
            //   this.diagram2(res.data?.chart);
            // });
            // this.pagination.total = res.data.total || 0
          }
        })
        .catch((e) => {
          console.log(e);
        })
        .finally(() => {
          let data = this.engineeringList?.[0];
          if (this.activeOne) {
            for (const val of this.engineeringList) {
              if (val.wrpcd === this.activeOne) {
                data = val;
                break;
              }
            }
          }
          this.handleProjectClick(data, isChangeActiveTwo);
        });
    },

    /**
     * 打开“新增档案分类”弹窗
     */
    createNewFolder() {
      this.visible = true;
    },
    /**
     * 点击档案树节点的回调函数
     */
    handleNodeClick(val) {
      this.defaultExpandedKeys = [val.id];
      this.activeTwo = val.id;
      this.lastActiveId = val.id;
      this.$nextTick(() => {
        this.$refs.ArchivesDetail.search();
      });
    },
    /**
     * 点击档案树节点的编辑按钮
     */
    handleEditFolderName(data) {
      this.isEditFolderName = true;
      this.update_obj = data;
      this.docClassify = data.docClassify;
      this.visible = true;
    },
    /**
     * 点击档案树节点的新增按钮
     */
    handleAddFolder(data) {
      this.visible = true;
      this.isEditFolderName = false;
      this.update_obj = data;
    },
    /**
     * 关闭“新增/编辑档案分类名称”弹窗
     */
    cancelEditFolderName() {
      this.docClassify = "";
      this.update_obj = null;
      this.visible = false;
    },
    /**
     * 新增/编辑档案分类
     */
    submitFolderName() {
      const form = {
        docClassify: this.docClassify,
      };
      if (this.isEditFolderName) {
        form.id = this.update_obj.id;
        form.parentId = this.update_obj.parentId;
      } else {
        form.parentId = this.update_obj ? this.update_obj.id : -1;
      }
      form.wrpcd = this.projectInfo.wrpcd;
      saveFolder(form)
        .then((res) => {
          if (res.status === 200) {
            this.$message.success("提交成功");
            if (this.isEditFolderName) {
              this.update_obj.docClassify = this.docClassify;
            } else {
              form.childList = [];
              form.count = 0;
              for (const key in res.data) {
                form[key] = res.data[key];
              }
              if (this.update_obj) {
                if (!this.update_obj.childList) {
                  this.$set(this.update_obj, "childList", []);
                }
                this.update_obj.childList.push(form);
              } else {
                this.archivesType.push(form);
              }
              if (this.archivesType.length === 1) {
                this.handleNodeClick(this.archivesType[0]);
              }
            }
            this.visible = false;
          }
        })
        .catch((e) => {
          console.log(e);
          this.$message.error("提交失败");
        });
    },
    /**
     * 删除分类
     */
    handleDelFolder(node, data) {
      delFolderById({
        id: data.id,
        parentId: data.parentId,
        wrpcd: data.wrpcd,
      })
        .then((res) => {
          if (res.status === 200) {
            this.$message.success("删除成功");
            const parent = node.parent;
            const children = parent.data.childList || parent.data;
            const index = children.findIndex((d) => d.id === data.id);
            children.splice(index, 1);
            this.$refs.ArchivesDetail.search();
          }
        })
        .catch((e) => {
          console.log(e);
          this.$message.error("删除失败");
        });
    },
    // 下一页
    handleCurrentPageChange(page) {
      this.pagination.page = page;
      this.queryListData();
    },
    /**
     * 点击详情按钮
     */
    handleDetail(row) {
      // console.log("row: ", row);
      this.handleProjectClick(row);
      this.projectName = row.projectName;
      this.switchPage = true;
    },
    loadDataFn(options) {
      this.form = options;
      if (!this.form.areaCode) {
        this.form.areaCode = this.userInfo.areaCode;
      }
      this.pagination.page = 1;
      this.queryListData();
    },
    getProjectMsg(val, isChangeActiveTwo = true) {
      // this.handleProjectClick({})
      this.wrpcd = val.id;
      if (val.id) {
        getDocsByProjectId({ wrpcd: val.id }).then((res) => {
          // console.log("res",res);
          if (res.status === 200) {
            this.handleProjectClick(res?.data, isChangeActiveTwo);
          }
        });
        this.switchPage = true;
      }
    },
    // 饼图
    diagram1(item) {
      const chartDom = document.getElementById("main1");
      const myChart = this.$echarts.init(chartDom);
      let img =
        "data:image/png;base64,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";
      const list = [
        {
          name: "有档案工程数量",
          value: item.hasContentNum,
          textStyle: { color: "#3388FF" },
        },
        {
          name: "无档案工程数量",
          value: item.noContentNum,
          textStyle: { color: "#FF9553" },
        },
      ];
      let formatNumber = function (num) {
        let reg = /(?=(\B)(\d{3})+$)/g;
        return num.toString().replace(reg, ",");
      };
      let total = list?.reduce((a, b) => {
        return a + b.value * 1;
      }, 0);
      const option = {
        color: ["#3388FF", "#FF9553"],
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)",
        },
        title: [
          {
            text:
              "{val|" + formatNumber(total) + "}\n{name|" + "总工程数量" + "}",
            top: "center",
            left: "23%",
            textStyle: {
              rich: {
                val: {
                  fontSize: 32,
                  fontWeight: "bold",
                  color: "#0A4EAD",
                  padding: [0, 15],
                },
                name: {
                  fontSize: 14,
                  fontWeight: "normal",
                  color: "#666666",
                  padding: [10, 0],
                },
              },
            },
          },
        ],
        graphic: {
          elements: [
            {
              type: "image",
              z: 3,
              style: {
                image: img,
                width: 130,
                height: 130,
              },
              left: "18.3%",
              top: "center",
              position: [100, 100],
            },
          ],
        },
        legend: {
          orient: "vertical",
          right: "10%",
          align: "left",
          top: "middle",
          itemWidth: 12,
          itemHeight: 12,
          data: list,
          formatter: function (name) {
            let res = list?.filter((v) => v.name === name);
            res = res[0] || {};
            let unit = res.unit || "";
            return (
              "{name|" +
              name +
              "}  {value|" +
              res.value +
              "}{unit|" +
              unit +
              "}"
            );
          },
          textStyle: {
            rich: {
              name: {
                fontSize: 12,
                color: "#000",
              },
              value: {
                fontSize: 16,
                fontWeight: 700,
                padding: [0, 5, 0, 15],
              },
              unit: {
                fontSize: 12,
              },
            },
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            center: ["30%", "50%"],
            radius: ["50%", "60%"],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: true,
                borderWidth: 8, //大小设置
                // position: 'outter',
                formatter: "{d}%",
              },
            },
            labelLine: {
              normal: {
                show: true,
                length: 20, // 改变标示线的长度
              },
            },
            data: list,
            itemStyle: {
              normal: {
                borderWidth: 2, //大小设置
                shadowBlur: 0,
                borderRadius: 5,
                borderColor: "#f0f6fb",
              },
            },
          },
        ],
      };
      option && myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart?.resize();
      });
    },
    // 饼图
    diagram2(item) {
      const chartDom = document.getElementById("main2");
      const myChart = this.$echarts.init(chartDom);
      const list = [
        { name: "有档案的水库工程数量", value: item.reservoirNum },
        { name: "有档案的水闸工程数量", value: item.sluiceNum },
        { name: "有档案的堤防工程数量", value: item.dikeNum },
      ];
      let total = list?.reduce((a, b) => {
        return a + b.value * 1;
      }, 0);
      const option = {
        title: {
          // text: '销量（百万）',
          left: "50%",
          bottom: "0%",
          textStyle: {
            color: "#a1a1a1",
            fontFamily: "Microsoft YaHei",
            fontWeight: 400,
          },
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            label: {
              show: true,
              backgroundColor: "#000", // 设置提示的背景颜色
            },
          },
          formatter: function (params) {
            return params[0].name + ": " + params[0].value; // 自定义提示框内容
          },
        },
        legend: {
          show: false,
        },
        grid: {
          containLabel: true,
          left: "5%",
          top: "10%",
          bottom: "15%",
          right: "5%",
        },
        xAxis: {
          type: "value",
          splitLine: {
            show: false,
            lineStyle: {
              color: "#ebebeb",
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: "#ebebeb",
            },
          },
          axisTick: {
            show: true,
          },
          axisLabel: {
            fontSize: 16,
            color: "#222F40",
          },
        },
        yAxis: {
          type: "category",
          inverse: true,

          axisLine: {
            show: true,
            lineStyle: {
              color: "#ebebeb",
            },
          },
          axisTick: {
            show: false,
          },
          axisPointer: {
            label: {
              show: true,
              margin: 30,
            },
          },
          data: list?.map((item) => item.name),
          axisLabel: {
            fontSize: 16,
            align: "right",
            color: "#222F40",
          },
        },
        series: [
          {
            z: 1,
            // name: '标识标牌',
            realtimeSort: true, //名字排序
            showBackground: false,
            barWidth: 20,
            type: "bar",
            data: list?.map((item, i) => {
              return {
                value: item.value,
                itemStyle: {
                  color: "#1C6FE3",
                },
              };
            }),
            label: {
              show: true,
              position: "right",
              color: "#1C6FE3",
              fontSize: 14,
              offset: [10, 0],
            },
          },
          {
            //分隔
            type: "pictorialBar",
            itemStyle: {
              color: "#fff",
            },
            symbolRepeat: "fixed",
            symbolMargin: 4,
            symbol: "roundRect",
            symbolClip: true,
            symbolSize: [2, 18],
            symbolPosition: "start",
            symbolOffset: [0, 0],
            data: list,
            z: 9,
            animationEasing: "elasticOut",
          },
          {
            // name: "外框",
            type: "bar",
            barGap: "-120%", // 设置外框粗细
            data: [
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
              total,
            ],
            barWidth: 24,
            itemStyle: {
              normal: {
                color: "transparent", // 填充色
                barBorderColor: "#edeff0", // 边框色
                barBorderWidth: 1, // 边框宽度
                // barBorderRadius: 0, //圆角半径
                label: {
                  // 标签显示位置
                  show: false,
                  position: "top", // insideTop 或者横向的 insideLeft
                },
              },
            },

            z: 0,
          },
        ],
      };
      option && myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart?.resize();
      });
    },
    goBack() {
      this.switchPage = false;
    },
    checkTypeChange(val) {
      // console.log("val",val);
      // if (!this.userInfo.assignwrpcdList?.length) {
      //   this.queryListData(true, val);
      // }
      this.queryData();
    },
  },
};
</script>

<style scoped lang="scss">
@mixin flexalign {
  display: flex;
  align-items: center;
}

@mixin flexjustify {
  display: flex;
  justify-content: space-between;
}

.graphical {
  width: 100%;
  height: 400px;
  display: flex;
  align-content: center;

  .chart1 {
    width: 40%;
    height: 100%;
  }

  .chart2 {
    width: 60%;
    height: 100%;
  }
}

.overviewTable {
  height: 60%;
}

.archives {
  width: 100%;
  height: 100%;
}

.archives-container {
  height: 100%;
  display: flex;
}

.engineering-list {
  flex: 1;
  padding-right: 20px;
  margin-right: 20px;
  border-right: 1px solid #e5e9f0;
  display: flex;
  flex-direction: column;
}

.engineering-list-title {
  color: #222f40;
  font-size: 18px;

  span {
    color: #0a4ead;
  }

  margin-bottom: 20px;
}

.engineering-list-container {
  flex: auto;
  overflow-y: auto;
}

.engineering-list-item {
  padding: 30px 0;
  margin-bottom: 10px;
  background-color: #f1f3f5;
  border: 4px;
  padding: 20px;
  cursor: pointer;

  &.isActive {
    background-color: #e2ebf9;

    .engineering-list-item-top {
      p {
        color: #0a4ead;
      }
    }
  }

  .engineering-list-item-top {
    @include flexjustify;
    margin-bottom: 16px;
    color: #222f40;
    font-size: 18px;

    p {
      font-weight: 700;
    }

    span {
      padding: 0 8px;
      border-radius: 4px;
      height: 28px;
      line-height: 28px;
      color: #ffffff;
      margin-left: 10px;
      background-color: #0a4ead;
    }
  }

  .engineering-list-item-bottom {
    display: flex;
    color: #222f40;
    font-size: 18px;

    @mixin beforePoint {
      content: "";
      display: block;
      background-color: #9bafcb;
      border-radius: 50%;
      width: 8px;
      height: 8px;
      margin-right: 10px;
    }

    p {
      @include flexalign;
      margin-right: 16px;

      &::before {
        @include beforePoint;
      }
    }

    span {
      position: relative;
      padding-left: 16px;

      &::before {
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        margin: auto 0;
        @include beforePoint;
      }
    }
  }
}

.archives-type {
  flex: 1;
  padding-right: 20px;
  margin-right: 20px;
  border-right: 1px solid #e5e9f0;

  .archives-type-btn {
    display: flex;
    align-items: center;
  }
}

.archives-type-title {
  @include flexjustify;
  align-items: center;
  color: #0a4ead;
  font-size: 18px;
  font-weight: 700;
  margin-bottom: 15px;
}

.archives-type-item {
  @include flexalign;
  flex: 1;
  justify-content: space-between;
  cursor: pointer;
  margin: 10px 0;
  padding-left: 40px;
  background: url("@/assets/basic/icon-folder.png") no-repeat;
  background-size: 18px 18px;
  background-position: 14px center;

  &.isActive {
    p {
      color: #0a4ead;
    }
  }

  p {
    color: #222f40;
    font-size: 18px;
    margin-right: auto;
    flex: 1;
  }
}

.file-list {
  flex: 3;
  height: 100%;
}

.file-list-title {
  @include flexalign;
  margin-bottom: 20px;
  padding-bottom: 20px;
  @include borBottom;

  p {
    font-size: 18px;
    font-weight: 700;
    color: #0a4ead;
    margin-right: 30px;
  }

  .file-list-btns {
    margin-right: auto;
    display: flex;
  }

  .pagination {
    text-align: right;
  }

  .mr20 {
    margin-right: 20px;
  }
}

.archives-tab {
  width: 100%;
  height: 80%;

  .form {
    width: 100%;
    display: flex;

    ::v-deep(.el-form-item__label) {
      line-height: 48px;
      color: #000;
    }

    ::v-deep(.el-input__inner) {
      color: #000;
      width: 300px;
      background-color: #ffffff;
      border: 1px solid #dcdfe6;
    }

    ::v-deep(.el-button) {
      padding: 0;
      width: 100px;
      height: 48px;
      line-height: 48px;
    }

    .form-item {
      margin-right: 20px;
      position: relative;
    }

    .form-item-btns {
      flex: 1;

      ::v-deep(.el-form-item__content) {
        width: 100%;
        display: flex;
      }
    }

    .ml20 {
      margin-left: 20px;
    }

    .mrAuto {
      margin-right: auto;
    }
  }
}

.overflow-h {
  overflow: hidden;
}

::v-deep(.el-table__body-wrapper) {
  flex: 1;
  height: 0;
  overflow-y: auto;
}
::v-deep(.el-tree-node.is-current) {
  background-color: transparent;

  & > .el-tree-node__content {
    background-color: #f0f5fc;
  }
}
</style>
