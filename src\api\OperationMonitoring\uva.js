import $http from "@/utils/request";
import { withBaseUrl } from "@/utils/apiUrl";

/**
 * 无人机巡查请求类型定义
 * @typedef {Object} AddUvaInspectParams
 */
/**
 * @property {Date} createdAt - 创建时间
 * @property {number} id - 主键ID
 * @property {string} inspectionRoute - 巡查路线
 * @property {string} resCode - 水库代码
 * @property {string} resName - 水库名称
 * @property {Date} tm - 巡查时间
 * @property {Date} tmEnd - 巡查时间结束
 * @property {Date} tmStart - 巡查时间开始
 * @property {string} uavName - 无人机名称
 * @property {Date} updatedAt - 更新时间
 * @property {number} videoFileId - 巡查视频附件id
 * @property {any} [key: string] - 动态扩展字段
 */
/**
 * 无人机巡查新增
 * @param {AddUvaInspectParams} params
 * @returns
 */
export const addUvaInspect = (params) => {
  return $http.post(withBaseUrl("/uav/uavInspect/add"), params);
};

/**
 * 运行监控-无人机巡查删除
  GET /uav/uavInspect/delete
 */
export const deleteUvaInspect = (id) => {
  return $http.get(withBaseUrl(`/uav/uavInspect/delete?id=${id}`));
};

/**
运行监控-无人机巡查分页查询
  POST /uav/uavInspect/pageList
 */
/**
 * 无人机巡查列表参数类型定义
 * @typedef {Object} UavInspectListParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [inspectionRoute] - 巡查路线
 * @property {string} [resCode] - 水库代码
 * @property {string} [resName] - 水库名称
 * @property {Date} [tm] - 巡查时间
 * @property {Date} [tmEnd] - 巡查时间结束
 * @property {Date} [tmStart] - 巡查时间开始
 * @property {string} [uavName] - 无人机名称
 * @property {Date} [updatedAt] - 更新时间
 * @property {number} [videoFileId] - 巡查视频附件id
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 无人机巡查列表
 * @param {UavInspectListParam} params
 * @returns
 */
export const getUvaInspectList = (params) => {
  const { pageNum, pageSize } = params || {};
  const url = `/uav/uavInspect/pageList?pageNum=${pageNum}&pageSize=${pageSize}`;
  delete params.pageNum;
  delete params.pageSize;
  return $http.post(
    withBaseUrl(url),
    params
  );
};

/*
运行监控-无人机巡查修改
  POST /uav/uavInspect/update
*/
/**
 * 无人机巡查更新参数类型定义
 * @typedef {Object} UpdateUavInspectParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [inspectionRoute] - 巡查路线
 * @property {string} [resCode] - 水库代码
 * @property {string} [resName] - 水库名称
 * @property {Date} [tm] - 巡查时间
 * @property {Date} [tmEnd] - 巡查时间结束
 * @property {Date} [tmStart] - 巡查时间开始
 * @property {string} [uavName] - 无人机名称
 * @property {Date} [updatedAt] - 更新时间
 * @property {number} [videoFileId] - 巡查视频附件id
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 无人机巡查更新
 * @param {UpdateUavInspectParam} params
 * @returns
 */
export const updateUvaInspect = (params) => {
  return $http.post(withBaseUrl("/uav/uavInspect/update"), params);
};

/**
运行监控-无人机巡查路线新增
  POST /uav/uavInspectRoute/add
 */
/**
 * 添加无人机巡查路线参数类型定义
 * @typedef {Object} AddUavRouteParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [resCode] - 水库代码
 * @property {string} [routeName] - 路线名称
 * @property {string} [specificRoute] - 具体路线（JSON 格式字符串或路径描述）
 * @property {number} [trajectoryFileId] - 轨迹附件id
 * @property {Date} [updatedAt] - 更新时间
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 添加无人机巡查路线
 * @param {AddUavRouteParam} params
 * @returns
 */
export const addUvaRoute = (params) => {
  return $http.post(withBaseUrl("/uav/uavInspectRoute/add"), params);
};

/**
运行监控-无人机巡查路线删除
  GET /uav/uavInspectRoute/delete
 */
export const deleteUvaRoute = (id, uploadid) => {
  return $http.get(withBaseUrl(`/uav/uavInspectRoute/delete?id=${id}&uploadid=${uploadid || ''}`));
};

/**
运行监控-无人机巡查路线分页查询
  POST /uav/uavInspectRoute/pageList
 */
/**
 * 无人机巡查路线列表查询参数类型定义
 * @typedef {Object} UavRouteListParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [resCode] - 水库代码
 * @property {string} [routeName] - 路线名称
 * @property {string} [specificRoute] - 具体路线（JSON 格式字符串或路径描述）
 * @property {number} [trajectoryFileId] - 轨迹附件id
 * @property {Date} [updatedAt] - 更新时间
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 无人机巡查路线列表
 * @param {UavRouteListParam} params
 * @returns
 */
export const getUvaRouteList = (params) => {
  const { pageNum, pageSize } = params || {};
  const url = `/uav/uavInspectRoute/pageList?pageNum=${pageNum}&pageSize=${pageSize}`;
  delete params.pageNum;
  delete params.pageSize;
  return $http.post(withBaseUrl(url), params);
};

export const uvaRouteListAll = () => {
  return $http.post(withBaseUrl("/uav/uavInspectRoute/listAll"));
};

/**
运行监控-无人机巡查路线修改
  POST /uav/uavInspectRoute/update
*/
/**
 * 更新无人机巡查路线参数类型定义
 * @typedef {Object} UpdateUavRouteParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [resCode] - 水库代码
 * @property {string} [routeName] - 路线名称
 * @property {string} [specificRoute] - 具体路线（JSON 格式字符串或路径描述）
 * @property {number} [trajectoryFileId] - 轨迹附件id
 * @property {Date} [updatedAt] - 更新时间
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 无人机巡查路线更新
 * @param {UpdateUavRouteParam} params
 * @returns
 */
export const updateUvaRoute = (params) => {
  return $http.post(withBaseUrl("/uav/uavInspectRoute/update"), params);
};

/*
运行监控-无人机管理新增
  POST /uav/uavManagement/add
*/
/**
 * 添加无人机管理参数类型定义
 * @typedef {Object} AddUavManagementParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [imageFileId] - 图片文件ID（如：UUID 或 文件标识）
 * @property {Date} [purTime] - 采购时间
 * @property {Date} [purTimeEnd] - 采购时间结束（用于查询范围）
 * @property {Date} [purTimeStart] - 采购时间开始（用于查询范围）
 * @property {string} [resCode] - 水库代码
 * @property {string} [taskDes] - 任务描述
 * @property {string} [uavModel] - 无人机型号
 * @property {string} [uavName] - 无人机名称
 * @property {Date} [updatedAt] - 更新时间
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 添加无人机管理
 * @param {AddUavManagementParam} params
 * @returns
 */
export const addUvaManagement = (params) => {
  return $http.post(withBaseUrl("/uav/uavManagement/add"), params);
};

/*
运行监控-无人机管理删除
  GET /uav/uavManagement/delete
*/
export const deleteUvaManagement = (id) => {
  return $http.get(withBaseUrl(`/uav/uavManagement/delete?id=${id}`));
};

/*
运行监控-无人机管理分页查询
  POST /uav/uavManagement/pageList
*/
/**
 * 无人机管理列表查询参数类型定义
 * @typedef {Object} UavManagementListParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [imageFileId] - 图片文件ID（如：UUID 或 文件标识）
 * @property {Date} [purTime] - 采购时间
 * @property {Date} [purTimeEnd] - 采购时间结束（用于查询范围）
 * @property {Date} [purTimeStart] - 采购时间开始（用于查询范围）
 * @property {string} [resCode] - 水库代码
 * @property {string} [taskDes] - 任务描述
 * @property {string} [uavModel] - 无人机型号
 * @property {string} [uavName] - 无人机名称
 * @property {Date} [updatedAt] - 更新时间
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 无人机管理列表
 * @param {UavManagementListParam} params
 * @returns
 */
export const getUvaManagementList = (params) => {
  const { pageNum, pageSize } = params || {};
  const url = `/uav/uavManagement/pageList?pageNum=${pageNum}&pageSize=${pageSize}`;
  delete params.pageNum;
  delete params.pageSize;
  return $http.post(withBaseUrl(url), params);
};

export const uvaManagementListAll = () => {
  return $http.post(withBaseUrl("/uav/uavManagement/listAll"));
};

/*
运行监控-无人机管理修改
  POST /uav/uavManagement/update
*/
/**
 * 更新无人机管理参数类型定义
 * @typedef {Object} UpdateUavManagementParam
 */
/**
 * @property {Date} [createdAt] - 创建时间
 * @property {number} [id] - 主键ID
 * @property {string} [imageFileId] - 图片文件ID（如：UUID 或 文件标识）
 * @property {Date} [purTime] - 采购时间
 * @property {Date} [purTimeEnd] - 采购时间结束（用于查询范围）
 * @property {Date} [purTimeStart] - 采购时间开始（用于查询范围）
 * @property {string} [resCode] - 水库代码
 * @property {string} [taskDes] - 任务描述
 * @property {string} [uavModel] - 无人机型号
 * @property {string} [uavName] - 无人机名称
 * @property {Date} [updatedAt] - 更新时间
 * @property {*} [key: string] - 动态扩展字段（任意附加属性）
 */
/**
 * 更新无人机管理
 * @param {UpdateUavManagementParam} params
 * @returns
 */
export const updateUvaManagement = (params) => {
  return $http.post(withBaseUrl("/uav/uavManagement/update"), params);
};
