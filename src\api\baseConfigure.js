import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  list: apiUrl.defaultUrl + '/setting/list',
  save: apiUrl.defaultUrl + '/setting/save',
  delete: apiUrl.defaultUrl + '/setting/delete',
  systemInfo: apiUrl.defaultUrl + '/setting/systemInfo'
}

export const downloadUrl = apiUrl.defaultUrl + '/setting/downloadImg'

/**
 * 列表
 */
export function list (data) {
  return $http.post(api.list, data)
}

/**
 * 保存
 */
export function save (data) {
  return $http.postUpLoadFile(api.save, data)
}

/**
 * 删除
 */
export function deleteBg (data) {
  return $http.post(api.delete, data)
}

/**
 * 下载
 */
export function downloadImg (data) {
  return $http.postDownLoad(downloadUrl, data, '', 'arraybuffer')
}

export function systemInfo (data) {
  return $http.postDownLoadHead(api.systemInfo, data, '', 'arraybuffer')
}
