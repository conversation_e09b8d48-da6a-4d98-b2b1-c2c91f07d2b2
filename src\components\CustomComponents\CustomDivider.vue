<template>
  <!-- 自定义分隔线组件 -->
  <div class="cs-divider"></div>
</template>

<script>
export default {
  name: "CustomDivider",
};
</script>

<style lang="scss" scoped>
$color-bg: #e2ebf8;
@mixin commonStyle {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 3px;
  background: $color-primary;
  content: "";
}

.cs-divider {
  position: relative;
  height: 1px;
  background: $color-bg;
  &::before {
    @include commonStyle;
    left: 0;
  }
  &::after {
    @include commonStyle;
    right: 0;
  }
}
</style>
