@mixin mix-el-input {
  --el-input-bg-color: rgb(16 54 98 / 70%) !important;
  --el-input-border-color: #0b4eb3 !important;
  --el-input-hover-border-color: #3f80e2 !important;
  --el-input-text-color: #c5e3ff !important;
}

.el-empty {
  --el-text-color-secondary: #fff !important;
}

.c-black-input {
  @include mix-el-input;
}

.c-black-el-date-picker {
  --el-input-text-color: #c5e3ff !important;
  --el-input-border-color: #0b4eb3 !important;
  --el-input-bg-color: rgb(21 63 113 / 70%) !important;
  --el-input-icon-color: #c5e3ff !important;
  --el-input-placeholder-color: #c5e3ff !important;
  --el-input-hover-border-color: #1882fb !important;

  .el-icon {
    color: #c5e3ff !important;
  }

  .el-range-input,
  .el-range-separator {
    color: #c5e3ff !important;
  }
}

.el-date-editor--datetimerange.el-input__wrapper {
  --el-text-color-regular: #c5e3ff;
  --el-text-color-primary: #7ff;
  background: transparent;
  color: #c5e3ff;
}

// 时间选择器弹框统一样式
.el-popper.el-picker__popper.el-tooltip {
  color: #c5e3ff !important;
  background-color: rgb(16 54 98 / 70%) !important;
  border-color: #0b4eb3 !important;

  .el-picker-panel {
    background-color: rgb(16 54 98 / 70%) !important;

    .el-date-picker__header {
      span {
        color: #c5e3ff !important;

        button {
          color: #c5e3ff !important;
        }
      }

      border-color: #0b4eb3 !important;
    }

    .el-date-picker__time-header {
      border-color: #0b4eb3 !important;

      .el-input {
        @include mix-el-input;
      }
    }

    .el-picker-panel__body {
      .el-date-range-picker__time-header {
        border-color: #0b4eb3 !important;

        .el-icon {
          color: #c5e3ff !important;
        }

        .el-date-range-picker__time-picker-wrap {
          .el-input {
            --el-input-border-color: #0b4eb3 !important;
            --el-input-hover-border-color: #0b4eb3 !important;
          }
        }

        .el-input__wrapper {
          background-color: rgb(16 54 98 / 70%) !important;

          .el-input__inner {
            color: #c5e3ff !important;
          }
        }
      }

      .el-picker-panel__content {
        border-color: #1882fb !important;
      }

      .el-date-range-picker__header {
        color: #c5e3ff;

        .el-picker-panel__icon-btn {
          .el-icon {
            color: #c5e3ff;
          }
        }
      }

      .el-date-table {
        td.disabled .el-date-table-cell {
          background-color: #88ade421 !important;
          .el-date-table-cell__text {
            color: #ccc !important;
          }
        }
        th {
          color: #c5e3ff;
          border-color: #c5e3ff !important;
        }

        .in-range {
          --el-datepicker-inrange-bg-color: #ffffff40;
          --el-datepicker-inrange-hover-bg-color: #ffffff60;
          --el-datepicker-hover-text-color: #c5e3ff !important;

          .el-date-table-cell__text {
            color: #c5e3ff !important;
          }
        }
      }

      .el-date-table-cell__text {
        color: #c5e3ff;
      }

      td:not(.today) {
        &:hover {
          .el-date-table-cell__text {
            color: #1882fb;
          }
        }
      }
    }

    .el-picker-panel__footer {
      background-color: rgb(16 54 98 / 70%) !important;
      border-color: #0b4eb3 !important;

      .el-button {
        span {
          color: #c5e3ff;
        }
      }

      .is-plain {
        background-color: rgb(16 54 98 / 70%) !important;
        border-color: #0b4eb3 !important;
      }
    }
  }

  .el-popper__arrow {
    &::before {
      background-color: #103662 !important;
      border-color: #0b4eb3 !important;
    }
  }
}

.el-picker-panel {
  --el-text-color-regular: #54bcff !important;
  --el-datepicker-header-text-color: #4080ff !important;
  --el-datepicker-inrange-hover-bg-color: #155bc4 !important;
  --el-datepicker-inrange-bg-color: #2256a4 !important;
  --el-datepicker-icon-color: #54bcff !important;

  .el-date-table td.available:hover .el-date-table-cell__text {
    color: #fff !important;
  }
}

.c-black-el-dropdown {
  --el-bg-color-overlay: rgb(16 54 98 / 70%);
  --el-text-color-regular: #fff;
  --el-border-color-light: #0b4eb3 !important;
  --el-dropdown-menuItem-hover-fill: #1882fb30 !important;
  --el-dropdown-menuItem-hover-color: #fff !important;
}

.c-black-el-checkbox {
  .el-checkbox__label {
    color: #ffffff90;
  }

  .el-checkbox__inner {
    background-color: transparent;
    border-color: #0459d7;
  }
}

.c-black-el-radio {
  .el-radio__label {
    color: #ffffff90;
  }

  .el-radio__inner {
    background-color: transparent;
    border-color: #0459d7;

    &::after {
      width: 6px;
      height: 6px;
      background-color: #003866;
    }
  }

  .is-checked {
    .el-radio__inner {
      background-color: #0459d7;
    }
  }
}

@mixin mix-el-select {
  --el-border-color: #0b4eb3 !important;
  --el-select-border-color-hover: #3f80e2;
  --el-text-color-regular: #fff;

  .el-select__wrapper {
    background-color: transparent !important;
  }

  .is-hovering {
    border-color: #3f80e2 !important;

    .el-tag {
      .el-icon {
        color: #1882fb;
        background-color: #0d63c5;
      }
    }
  }

  .el-select__suffix {
    .el-icon {
      color: #0459d7;
    }
  }

  .el-select__selected-item {
    .el-tag {
      background-color: #1882fb !important;

      .el-select__tags-text,
      .el-icon {
        color: #fff;
      }
    }
  }
}

.c-black-el-select {
  @include mix-el-select;
}

.el-select__popper {
  color: #fff !important;
  background-color: rgb(16 54 98 / 100%) !important;
  border-color: #1882fb !important;

  .el-select-dropdown__item {
    color: #fff !important;
    background-color: transparent !important;

    &:hover {
      background-color: #1882fb30 !important;
    }
  }

  .el-popper__arrow {
    &::before {
      background-color: rgb(16 54 98 / 100%) !important;
      border-color: #1882fb !important;
    }
  }
}

.c-black-el-tabs {
  .el-tabs__header {
    margin-bottom: 0;

    .el-tabs__nav-wrap {
      &::after {
        background-color: #fff0 !important;
      }
    }

    .el-tabs__active-bar {
      background-color: #7ff;
    }

    .el-tabs__item {
      position: relative;
      height: 30px;
      padding: 0 15px !important;

      // padding-left: 20px !important;
      font-size: 18px !important;
      color: #7ff8;
    }

    .is-active {
      padding: 0 !important;
      margin: 0 26px !important;
      font-size: 22px !important;
      color: #fff;

      &::before,
      &::after {
        position: absolute;
        top: 50%;
        width: 22px;
        height: 16px;
        content: "";
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 100% 100%;
        transform: translateY(-50%);
      }

      &::before {
        left: -26px;
        background-image: url("@/assets/images/common/tab-left.png");
      }

      &::after {
        right: -26px;
        background-image: url("@/assets/images/common/tab-right.png");
      }
    }
  }
}

.c-checkbox {
  --el-checkbox-text-color: #fff !important;
  --el-color-primary: #7fff !important;
  --el-checkbox-checked-icon-color: #083766ff !important;
  --el-fill-color-blank: #083766ff !important;

  .el-checkbox__input .el-checkbox__inner {
    overflow: hidden;
    border: 1px solid #7fff;

    // border-radius: 50%;
  }

  &.circle {
    .el-checkbox__input .el-checkbox__inner {
      border-radius: 50%;
    }
  }
}

// 弹窗样式
.c-el-dialog {
  &.el-dialog {
    padding: 0;
    background-color: transparent;

    .el-dialog__header {
      padding-left: 20px;
      line-height: 54px;
      background-image: url("@/assets/images/common/dialog-header-bg.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .el-dialog__title {
        font-family: YouSheBiaoTiHei, sans-serif;
        font-size: 26px;
        color: #7ff;
      }

      .el-dialog__headerbtn {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        background-image: url("@/assets/images/common/close-btn-bg.png");
        background-repeat: no-repeat;
        background-position: center center;
        background-size: 26px 26px;

        .el-dialog__close {
          color: #7ff;
        }
      }
    }

    .el-dialog__body {
      padding: 0 16px 22px;
      background-image: url("@/assets/images/common/dialog-conten-bg.png");
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }
  }
}

.c-black-btn {
  background-color: #001529 !important;
  border-color: #1882fb !important;

  .el-icon {
    color: #1882fb;
  }

  span {
    color: #1882fb;
  }
}

// 表格样式
.c-blue-el-table {
  --el-table-bg-color: transparent !important;
  --el-table-header-bg-color: #0099ff80 !important;
  --el-table-tr-bg-color: #0272c21a !important;
  --el-table-header-text-color: #7ff !important;
  --el-table-text-color: #fff !important;
  --el-table-row-hover-bg-color: #0099ff38 !important;
  --el-border-color-lighter: #3f7cbaff !important;

  .el-table__row {
    &:hover {
      box-shadow: 0 0 7px 0 #00b2ff inset;
    }
  }

  thead {
    .cell {
      font-size: 18px;
    }
  }

  th.el-table__cell {
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }

  .el-table-fixed-column--right.el-table__cell {
    backdrop-filter: blur(3px) !important;
  }
}

.c-el-segmented {
  --el-text-color-primary: #7ff !important;
  --el-segmented-bg-color: transparent !important;
  --el-text-color-regular: #fff !important;
  --el-fill-color-darker: #00000042 !important;
  --el-fill-color-dark: transparent !important;

  background: transparent !important;

  .el-segmented__group {
    gap: 6px;
  }

  .el-segmented__item-selected {
    background: radial-gradient(106.99% 117.88% at 0% 0%, #09fc 0%, #0099ff52 100%) !important;
    border: 1px solid #2fb3e9ff !important;
    box-shadow: 0 0 7px 0 #00b2ff inset !important;
  }

  &.have-padding {
    .el-segmented__item {
      height: 34px !important;
      padding: 0 52px !important;
    }
  }

  .el-segmented__item {
    color: #fff !important;
    background: linear-gradient(180deg, #98c2f433 0%, #f0f7ff24 100%) !important;

    &.is-selected {
      color: #7fff !important;
      background: transparent !important;
    }
  }
}

// 输入框/下拉框样式
.c-blue-search-input {
  --el-border-color: #77ffffb3 !important;
  --el-color-primary: #7fff !important;
  --el-border-color-hover: #7fff !important;
  --el-fill-color-blank: #083766ff !important;
  --el-input-text-color: #7fff !important;
  --el-select-input-color: #77ffffb3 !important;

  .el-input__wrapper {
    background-color: transparent !important;
    border: 1px solid #0b4eb3 !important;
    box-shadow: none !important;
  }

  .el-input__inner {
    color: #7fff !important;
  }
}

// 分页器样式
.c-blue-pagination {
  justify-content: flex-end !important;
  padding: 10px 0 !important;
  margin-top: 20px !important;

  .el-pagination__total {
    color: #fff !important;
  }

  --el-fill-color-blank: transparent !important;
  --el-pagination-button-color: rgb(27 95 191) !important;
  --el-input-text-color: #fff !important;
  --el-text-color-regular: #fff !important;
}

// 表单样式
.c-blue-form {
  .el-form-item__label {
    color: #fff !important;
  }

  .el-input__wrapper,
  .el-textarea__inner,
  .el-select__wrapper {
    color: #fff !important;
    background-color: transparent !important;
    border: 1px solid #0b4eb3 !important;

    --el-text-color-regular: #fff !important;
  }

  .el-select .el-input__inner,
  .el-input__inner,
  .el-textarea__inner {
    color: #fff !important;

    --el-text-color-regular: #fff !important;
  }
}

// 上传控件样式
.c-blue-upload {
  .el-upload-list--picture-card .el-upload-list__item {
    --el-upload-list-picture-card-size: 80px !important;

    position: relative;
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: transparent !important;
    border: none !important;
  }

  .el-upload--picture-card {
    --el-upload-picture-card-size: 80px !important;

    position: relative;
    color: #fff !important;
    background-color: rgb(11 78 179 / 30%) !important;
    border: 1px dashed #0b4eb3 !important;
  }
}

.c-black-el-pagination {
  .btn-prev,
  .btn-next {
    background-color: transparent !important;
    border: 1px solid #1882fb;

    .el-icon {
      color: #1882fb;
    }
  }

  .el-pager {
    .number {
      color: #1882fb;
      background-color: transparent !important;
      border: 1px solid #1882fb;
    }

    .is-active {
      background-color: #1882fb !important;
    }
  }

  .el-pagination__total,
  .el-pagination__goto,
  .el-pagination__classifier {
    color: #fff;
  }

  .el-select {
    @include mix-el-select;

    .el-select__selected-item {
      span {
        color: #fff;
      }
    }
  }

  .el-pagination__editor {
    @include mix-el-input;
  }
}

.c-black-el-tree {
  --el-tree-node-hover-bg-color: #7ff5 !important;

  background-color: transparent !important;

  .el-checkbox {
    .el-checkbox__inner {
      background-color: #083766 !important;
      border-color: #7ff;
    }

    .is-checked {
      .el-checkbox__inner {
        background-color: #7ff !important;
        border-color: #7ff;

        &::after {
          border-color: #083766 !important;
        }
      }
    }

    .is-indeterminate {
      .el-checkbox__inner {
        background-color: #7ff !important;
        border-color: #7ff;

        &::before {
          background-color: #083766;
        }
      }
    }
  }

  .el-tree-node__label {
    color: #fff !important;
  }
}

.c-black-el-table {
  --el-table-bg-color: transparent !important;
  --el-table-header-bg-color: transparent !important;
  --el-table-tr-bg-color: rgb(1 48 94 / 40%) !important;
  --el-table-header-text-color: #7ff !important;
  --el-table-text-color: #fff !important;
  --el-table-row-hover-bg-color: rgb(0 255 255 / 40%) !important;
  --el-fill-color-light: rgb(0 153 255 / 35%) !important;
  --el-table-border-color: transparent !important;

  .el-table__inner-wrapper {
    &::before {
      background-color: transparent;
    }
  }

  .el-table__header {
    background: linear-gradient(to bottom, #005ca4 0%, #003d6190 100%);

    .el-table__cell {
      border: none !important;

      .cell {
        font-weight: 400 !important;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__row {
      position: relative;

      .el-table__cell {
        border: none !important;
      }

      &:hover {
        .el-table__cell {
          .cell {
            // font-size: 16px;
            color: #7ff;
          }
        }
      }
    }
  }
}

.c-dialog-el-table {
  --el-table-bg-color: transparent !important;
  --el-table-header-bg-color: transparent !important;
  --el-table-tr-bg-color: rgb(1 48 94 / 40%) !important;
  --el-table-header-text-color: #7ff !important;
  --el-table-text-color: #fff !important;
  --el-fill-color-light: rgb(0 153 255 / 35%) !important;
  --el-table-border-color: transparent !important;
  --el-table-row-hover-bg-color: #0099ff38 !important;
  --el-table-current-row-bg-color: var(--el-fill-color-light) !important;

  .el-table__inner-wrapper {
    &::before {
      background-color: transparent;
    }
  }

  .el-table__header {
    background: #005c9a !important;

    .el-table__cell {
      border-right: 2px solid #0272c250 !important;
      border-bottom: 2px solid #0272c250 !important;

      &:last-child {
        border-right: 0 !important;
      }
    }
  }

  .el-table__body-wrapper {
    .el-table__row {
      position: relative;

      .el-table__cell {
        border-right: 2px solid #0272c250 !important;
        border-bottom: 2px solid #0272c250 !important;

        &:last-child {
          border-right: 0 !important;
        }
      }

      &.current-row,
      &:hover {
        box-shadow: 0 0 7px 0 #00b2ff inset !important;
      }
    }
  }
}

.c-black-normal-button {
  // 无type的普通按钮
  background-color: #022745 !important;
  border: 1px solid #1882fb !important;

  span {
    color: #1882fb;
  }
}

.c-cyan-el-button {
  background: url("@/assets/images/strengthenMaintenance/active-tab.png") no-repeat;
  background-color: #033c67 !important;
  background-position: center center;
  background-size: 100% 100%;
  border: none !important;
  border-radius: 4px !important;

  &:hover {
    background-color: #fff5 !important;
  }

  .el-icon {
    color: #7ff !important;
  }

  span {
    color: #7ff !important;
  }
}

// 地图弹窗样式，暂时使用ignore-，不转换px单位
.ignore-map-popup,
.mini-border {
  min-width: 300px;
  font-size: 14px;
  color: #fff;

  .map-popup-header {
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 40px;
    padding: 0 18px;
    font-size: 16px;
    font-weight: 700;
    color: #7ff;
    background: url("@/assets/images/common/popup-bg-header.png") no-repeat 0 0;
    background-size: 100% 100%;
  }

  .map-popup-body {
    box-sizing: border-box;
    padding: 12px 15px 3px;
    background: url("@/assets/images/common/popup-bg-body.png") no-repeat 0 0;
    background-size: 100% 100%;
  }

  .map-popup-body-flex {
    display: flex;

    .map-popup-img {
      flex-shrink: 0;
      width: 148px;
      height: 178px;
      margin-right: 6px;
    }

    .map-pop-list {
      flex: 1;
    }
  }

  .map-popup-footer {
    height: 18px;
    background: url("@/assets/images/common/popup-bg-footer.png") no-repeat 0 0;
    background-size: 100% 100%;
  }

  .map-popup-item {
    display: flex;
    align-items: flex-start;
    padding: 2px 9px;
    margin-top: 2px;
    line-height: 22px;
    background: rgb(0 34 67 / 30%);

    .item-label {
      color: #7ff;
    }

    .item-value {
      word-break: break-all;
    }
  }
}

.mini-border {
  .map-popup-header {
    height: 26px;
    padding: 0 12px;
    font-size: 14px;
    white-space: nowrap;
  }

  .map-popup-body {
    padding: 6px 6px 0;
  }

  .map-popup-footer {
    height: 6px;
  }
}

.ignore-map-tooltip {
  height: 28px;
  padding: 3px 12px;
  font-size: 14px;
  color: #fff;
  text-align: center;
  background: #00234599;
  border: 1px solid #70b8ff99;
  border-radius: 4px;
}

#layout.no-decorate .decorate-elm {
  display: none;
}
