<template>
  <div class="content">
    <left-region style="color: #fff" overflow="hidden auto">
      <base-title>落实监管</base-title>
      <div class="row mt2">
        <div class="pos-icon"></div>
        <div class="col">
          <span class="txt">
            {{ baseInfo?.resName }} ( {{ baseInfo?.resCenLong || '--' }}E,
            {{ baseInfo?.resCenLat || '--' }}°N )
          </span>
          <span class="sub-txt">
            {{ baseInfo?.mnun }}
          </span>
        </div>
      </div>
      <div class="row sub-infos mt11">
        <el-row :gutter="20" class="w-full">
          <el-col :span="9">
            <div class="col">
              <span class="tag-title"> 行政区划 </span>
              <el-tooltip
                class="item mt7"
                effect="light"
                placement="bottom"
                :content="baseInfo?.resLoc"
              >
                <span class="sub-txt">
                  {{ baseInfo?.resLoc || '--' }}
                </span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="col">
              <span class="tag-title"> 所在流域 </span>
              <el-tooltip class="item" effect="light" placement="bottom" :content="baseInfo?.basin">
                <span class="sub-txt"> {{ baseInfo?.basin || '--' }} </span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="col">
              <span class="tag-title"> 工程规模 </span>
              <el-tooltip class="item" effect="light" placement="bottom" :content="projectScale">
                <span class="sub-txt"> {{ projectScale || '--' }} </span>
              </el-tooltip>
            </div>
          </el-col>
          <el-col :span="5">
            <div class="col">
              <span class="tag-title"> 主管单位 </span>
              <el-tooltip class="item" effect="light" placement="bottom" :content="baseInfo?.cmun">
                <span class="sub-txt"> {{ baseInfo?.cmun || '--' }} </span>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="func-area mt15">
        <span class="fun-title">水库功能：</span>
        <div class="func-btns">
          <div
            :class="['func-btn', `func-btn-${index % 3}`]"
            v-for="(f, index) in baseInfo?.rsfn?.split(',')"
            :key="f"
          >
            {{ f }}
          </div>
        </div>
      </div>

      <div class="person-area mt15">
        <div class="person-box">
          <div class="person-title">地方政府负责人</div>
          <div class="person-info">
            <div class="row-info">
              <div class="avatar-icon"></div>
              <div class="person-name">{{ localGovResponsibility?.pername }}</div>
            </div>
            <div class="person-phone">{{ localGovResponsibility?.tel }}</div>
            <el-tooltip
              class="item"
              effect="dark"
              :content="localGovResponsibility?.post"
              placement="bottom"
            >
              <div class="person-dept">{{ localGovResponsibility?.post }}</div>
            </el-tooltip>
          </div>
        </div>

        <div class="person-box green-man">
          <div class="person-title">主管部门负责人</div>
          <div class="person-info">
            <div class="row-info">
              <div class="avatar-icon green-man"></div>
              <div class="person-name">{{ mainGovResponsibility?.pername }}</div>
            </div>
            <div class="person-phone">{{ mainGovResponsibility?.tel }}</div>
            <el-tooltip
              class="item"
              effect="dark"
              :content="mainGovResponsibility?.post"
              placement="bottom"
            >
              <div class="person-dept">{{ mainGovResponsibility?.post }}</div>
            </el-tooltip>
          </div>
        </div>

        <div class="person-box yellow-man">
          <div class="person-title">管理部门负责人</div>
          <div class="person-info">
            <div class="row-info">
              <div class="avatar-icon yellow-man"></div>
              <div class="person-name">{{ manageGovResponsibility?.pername }}</div>
            </div>
            <div class="person-phone">{{ manageGovResponsibility?.tel }}</div>
            <el-tooltip
              class="item"
              effect="dark"
              :content="manageGovResponsibility?.post"
              placement="bottom"
            >
              <div class="person-dept">{{ manageGovResponsibility?.post }}</div>
            </el-tooltip>
          </div>
        </div>
      </div>

      <base-title class="mt13">注册登记</base-title>
      <div class="register-area">
        <div class="register-bg mt12">
          <el-image
            :src="registerPhoto"
            preview-teleported
            fit="cover"
            :preview-src-list="[registerPhoto]"
            class="register-photo"
          />
        </div>
        <div class="register-right">
          <div class="register-item">
            <div class="register-icon reg-icon"></div>
            <div class="register-info">
              <div class="reg-label">注册登记号</div>
              <div class="reg-value no">{{ registerInfo?.zcdjh }}</div>
            </div>
          </div>
          <div class="r-row">
            <div class="register-item">
              <div class="register-icon time-icon"></div>
              <div class="register-info">
                <div class="reg-label">发证时间</div>
                <div class="reg-value st">{{ registerInfo?.zcdjrq }}</div>
              </div>
            </div>

            <div class="register-item">
              <div class="register-icon valid-icon"></div>
              <div class="register-info">
                <div class="reg-label">有效期</div>
                <div class="reg-value et">{{ registerInfo?.zcdjyxq }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <base-title class="mt3">信息共享</base-title>
      <div class="share-area" @click="showShareMsgDialog">
        <div class="dep-icons">
          <div class="dep-item"><span>水利部</span></div>
          <div class="dep-item"><span>长江委</span></div>
        </div>
        <div class="arrow-icons">
          <div class="arrow-icon"></div>
          <div class="arrow-icon"></div>
        </div>
        <div class="deep-box">
          <div class="item-icon data">数据中心</div>
          <div class="item-icon manage">矩阵管理</div>
          <div class="item-icon sk">青山嘴水库</div>
        </div>
      </div>
    </left-region>

    <right-region style="color: #fff">
      <base-title>
        水库基本信息
        <template #extra>
          <div @click="showBaseInfoDialog" class="ml10 flx-align-center">
            <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
          </div>
        </template>
      </base-title>
      <div class="right-info-area">
        <div class="r-1-icon"></div>
        <div class="r-2-line"></div>
        <div class="r-info">
          <div class="right-row">
            <div class="info-icon-bg scale-icon">
              <div class="info-icon"></div>
            </div>
            <div class="col">
              <div class="info-label">工程规模</div>
              <div class="info-value c-1">{{ projectScale }}</div>
            </div>
          </div>

          <div class="right-row">
            <div class="info-icon-bg year-icon">
              <div class="info-icon"></div>
            </div>
            <div class="col">
              <div class="info-label">建成年份</div>
              <div class="info-value c-2">{{ baseInfo?.compDate?.split('-')[0] }}</div>
            </div>
          </div>

          <div class="right-row">
            <div class="info-icon-bg loc-icon">
              <div class="info-icon"></div>
            </div>
            <div class="col">
              <div class="info-label">所在流域</div>
              <div class="info-value c-3">{{ baseInfo?.basin || '--' }}</div>
            </div>
          </div>

          <div class="right-row">
            <div class="info-icon-bg river-loc-icon">
              <div class="info-icon"></div>
            </div>
            <div class="col">
              <div class="info-label">所在河流</div>
              <div class="info-value c-4">{{ baseInfo?.river || '--' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div>
        <base-title>
          水文特征
          <template #extra>
            <div @click="showHydroInfoDialog" class="ml10 flx-align-center">
              <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
            </div>
          </template>
        </base-title>
        <div class="hydro-area">
          <div class="hydro-row">
            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon annual-icon"></div>
                <div class="col">
                  <div class="hydro-label">水库调节特性</div>
                  <div class="hydro-value">{{ rsrrgtpfm || '--' }}</div>
                </div>
              </div>
            </div>

            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon rain-icon"></div>
                <div class="col">
                  <div class="hydro-label">集雨面积</div>
                  <div class="hydro-value">{{ swtzInfo?.catchmentArea || '--' }} km²</div>
                </div>
              </div>
            </div>
          </div>

          <div class="hydro-row">
            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon total-icon"></div>
                <div class="col">
                  <div class="hydro-label">总库容</div>
                  <div class="hydro-value">{{ swtzInfo?.totCap || '--' }} 万m³</div>
                </div>
              </div>
            </div>

            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon adjust-icon"></div>
                <div class="col">
                  <div class="hydro-label">调洪库容</div>
                  <div class="hydro-value">{{ swtzInfo?.storFlCap || '--' }} 万m³</div>
                </div>
              </div>
            </div>
          </div>

          <div class="hydro-row">
            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon defense-icon"></div>
                <div class="col">
                  <div class="hydro-label">防洪库容</div>
                  <div class="hydro-value">{{ swtzInfo?.flcoCap || '--' }} 万m³</div>
                </div>
              </div>
            </div>

            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon water-level-icon"></div>
                <div class="col">
                  <div class="hydro-label">校核洪水位</div>
                  <div class="hydro-value">{{ swtzInfo?.chfllv || '--' }} m</div>
                </div>
              </div>
            </div>
          </div>

          <div class="hydro-row">
            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon design-icon"></div>
                <div class="col">
                  <div class="hydro-label">设计洪水位</div>
                  <div class="hydro-value">{{ swtzInfo?.dsfllv || '--' }} m</div>
                </div>
              </div>
            </div>

            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon dead-icon"></div>
                <div class="col">
                  <div class="hydro-label">死水位</div>
                  <div class="hydro-value">{{ swtzInfo?.deadLev || '--' }} m</div>
                </div>
              </div>
            </div>
          </div>

          <div class="hydro-row">
            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon flood-icon"></div>
                <div class="col">
                  <div class="hydro-label">防洪高水位</div>
                  <div class="hydro-value">{{ swtzInfo?.uppLevFlco || '--' }} m</div>
                </div>
              </div>
            </div>

            <div class="hydro-col">
              <div class="hydro-item">
                <div class="hydro-icon normal-icon"></div>
                <div class="col">
                  <div class="hydro-label">正常蓄水位</div>
                  <div class="hydro-value">{{ swtzInfo?.normWatLev || '--' }} m</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </right-region>

    <share-msg-dialog v-model="shareMsgDialogVisible" />

    <reservoir-info-dialog v-model="ridVisible" ref="ridRef" />

    <base-dialog
      v-model:showDialog="showCurrentDialog"
      append-to-body
      :show-footer="false"
      align-center
      top="0"
      width="40vw"
      :title="currentDialogTitle"
      style="--el-text-color-regular: #fff"
    >
      <div class="dialog-content">
        <!-- <el-image
          class="dialog-img"
          v-if="currentDialogContent.img"
          :src="currentDialogContent.img"
          :preview-src-list="[currentDialogContent.img]"
          :preview-teleported="true"
        /> -->
        <div v-html="currentDialogContent.content"></div>
      </div>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { getDict } from '@/alova_api/methods/dict'
import ShareMsgDialog from './components/ShareMsgDialog.vue'
import {
  getReservoirInfo,
  getResponsibilityInfo,
  getFileListByPathPrefix,
  getSwtzInfo,
  getZcdjInfo
} from '@/alova_api/methods/reservoir'
import { useRequest, useWatcher } from 'alova/client'
import { getDownLoadUrlById } from '@/api/module/attachment'
import { useMap3dStore } from '@/stores/modules/map3d'
import { markerConfig } from '@/hooks/useMapMarker'
import DOMPurify from 'dompurify'
const { addComPointLayer, removeLayer, pointHeight } = useMap3dStore()
const shareMsgDialogVisible = ref(false)

const riRef = useTemplateRef('ridRef')
const ridVisible = ref(false)

const currentDialogTitle = ref('')
const showCurrentDialog = ref(false)

// 水库基本信息
const { data: baseInfo, send } = useRequest(() => getReservoirInfo(), {
  immediate: false,
  initialData: {}
})

// 落实监管-责任人详情
const { data: responsibilityInfo } = useRequest(() => getResponsibilityInfo())

//地方政府负责人
const localGovResponsibility = computed(() => {
  return responsibilityInfo.value?.attWrpPers?.find(item => item.pertp === 'A')
})

//主管部门负责人
const mainGovResponsibility = computed(() => {
  return responsibilityInfo.value?.attWrpPers?.find(item => item.pertp === 'D')
})

//管理部门负责人
const manageGovResponsibility = computed(() => {
  return responsibilityInfo.value?.attWrpPers?.find(item => item.pertp === 'E')
})

// 水文特征
const { data: swtzInfo } = useRequest(() => getSwtzInfo())

// 注册登记信息
const { data: registerInfo } = useRequest(() => getZcdjInfo())

const { data: rrInfo } = useRequest(() => getFileListByPathPrefix('四全-全覆盖-水库介绍', 'mix'), {
  immediate: true,
  initialData: []
})

const registerPhoto = computed(() => {
  return getDownLoadUrlById(registerInfo.value?.['zcdjFileId'] || '')
})

// 字典
const { data: dict } = useRequest(() => getDict())

const projectScaleId = computed(() => {
  return dict.value?.find(item => item.dictCode === 'PROJECT_SCALE')?.id
})

const rsrrgtpfmId = computed(() => {
  return dict.value?.find(item => item.dictCode === 'RESERVOIR_REGULATION')?.id
})

const { data: projectScaleDict, send: sendProjectScaleDict } = useWatcher(
  () => getDict(projectScaleId.value),
  [projectScaleId]
)

const { data: rsrrgtpfmDict, send: sendRsrrgtpfmDict } = useWatcher(
  () => getDict(rsrrgtpfmId.value),
  [rsrrgtpfmId]
)

const projectScale = computed(() => {
  return projectScaleDict.value?.find(item => item.dictValue === baseInfo.value?.engScal)?.dictName
})

const rsrrgtpfm = computed(() => {
  return rsrrgtpfmDict.value?.find(item => item.dictValue === swtzInfo.value?.rsrrgtpfm)?.dictName
})

const showShareMsgDialog = () => {
  shareMsgDialogVisible.value = true
}

const showBaseInfoDialog = () => {
  riRef.value?.open('基本信息')
}

const showHydroInfoDialog = () => {
  riRef.value?.open('水文特征')
}

const getDetailHtml = (stnm?: string) => {
  const item = rrInfo.value.find(item => item.title === stnm)
  return {
    content: DOMPurify.sanitize(item?.displayContent || ''),
    img: getDownLoadUrlById(item?.imageFileId || '')
  }
}

const currentDialogContent = computed(() => {
  return getDetailHtml(currentDialogTitle.value)
})

const addStationLayer = () => {
  const qszSk = rrInfo.value
    .filter(item => item.title.includes('青山嘴'))
    .map(i => ({ ...i, lat: +i.lat, lon: +i.lon, h: +i.dtmel || pointHeight, stnm: i.title }))
  const otherSk = rrInfo.value
    .filter(item => !item.title.includes('青山嘴'))
    .map(i => ({ ...i, lat: +i.lat, lon: +i.lon, h: +i.dtmel || pointHeight, stnm: i.title }))
  addComPointLayer({
    id: 'stationLayer-qsz',
    data: qszSk,
    posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
    symbol: {
      billboard: {
        image: markerConfig.RR_DYNAMIC_QSZ,
        width: 35,
        height: 35
      },
      label: {
        minRange: 10,
        maxRange: 220000,
        pixelOffset: [0, -50]
      }
    },
    click: function (e) {
      const attr = e.overlay.attr || {}
      currentDialogTitle.value = attr.stnm
      showCurrentDialog.value = true
    },
    zoom: {
      minDist: 110000
    }
  })
  addComPointLayer({
    id: 'stationLayer',
    data: otherSk,
    posAttr: { x: 'lon', y: 'lat', z: 'h' }, //位置属性
    symbol: {
      billboard: {
        image: markerConfig.RR_DYNAMIC
      },
      label: {
        minRange: 10,
        maxRange: 220000,
        pixelOffset: [0, -35]
      }
    },
    click: function (e) {
      const attr = e.overlay.attr || {}
      currentDialogTitle.value = attr.stnm
      showCurrentDialog.value = true
    },
    zoom: {
      minDist: 110000
    }
  })
}

watch(
  () => rrInfo.value,
  () => {
    removeLayer('stationLayer')
    removeLayer('stationLayer-qsz')
    nextTick(() => {
      addStationLayer()
    })
  },
  {
    deep: true,
    immediate: true
  }
)

onMounted(() => {
  send()
  sendProjectScaleDict()
  sendRsrrgtpfmDict()
})

onUnmounted(() => {
  removeLayer('stationLayer')
  removeLayer('stationLayer-qsz')
})
</script>

<style lang="scss" scoped>
.dialog-content {
  :deep(strong) {
    font-weight: 900;
  }
  :deep(img) {
    width: 100%;
  }
  .dialog-img {
    width: 100%;
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.content {
  position: relative;
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 70px;

  :deep(.left-region-content) {
    justify-content: space-between;
  }

  :deep(.right-region-content) {
    justify-content: space-between;
  }

  .row {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
  }

  .col {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: center;
  }

  .w-full {
    width: 100%;
  }
}

.pos-icon {
  width: 66px;
  height: 54px;
  margin-right: 12px;
  background: url('@/assets/images/fullCoverage/1-pos-icon.png') no-repeat center;
  background-size: 100% 100%;
}

.txt {
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  text-align: left;
}

.sub-txt {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 400;
  color: #7ff;
  white-space: nowrap;
}

.tag-title {
  width: 76px;
  height: 24px;
  padding-left: 20px;
  margin-bottom: 7px;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  background: url('@/assets/images/fullCoverage/2-subtitle-bg.png') no-repeat center;
  background-size: 100% 100%;
}

.sub-infos {
  margin-top: 11px;
}

.mt2 {
  margin-top: 20px;
}

/* 水库功能区域 */
.func-area {
  display: flex;
  align-items: center;
}

.fun-title {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  text-align: left;
}

.func-btns {
  display: flex;
  gap: 10px;
}

.func-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 54px;
  height: 54px;
  font-size: 14px;
  font-weight: 400;

  &.func-btn-0 {
    color: #ffd060;
    background: url('@/assets/images/fullCoverage/3-fun-yellow.png') no-repeat center;
    background-size: 100% 100%;
  }

  &.func-btn-1 {
    color: #47ffd5;
    background: url('@/assets/images/fullCoverage/4-fun-green.png') no-repeat center;
    background-size: 100% 100%;
  }

  &.func-btn-2 {
    color: #67e6ff;
    background: url('@/assets/images/fullCoverage/5-fun-blue.png') no-repeat center;
    background-size: 100% 100%;
  }
}

/* 人员信息区域 */
.person-area {
  display: flex;
  gap: 4px;
}

.person-box {
  width: 143px;
  height: 118px;
  background: url('@/assets/images/fullCoverage/6-blue-man-bg.png') no-repeat center;
  background-size: 100% 100%;

  &.green-man {
    background: url('@/assets/images/fullCoverage/8-green-man-bg.png') no-repeat center;
    background-size: 100% 100%;
  }

  &.yellow-man {
    background: url('@/assets/images/fullCoverage/10-yellow-man-bg.png') no-repeat center;
    background-size: 100% 100%;
  }
}

.person-title {
  padding-top: 8px;
  padding-left: 11px;
  font-size: 14px;
  color: #fff;
  text-align: left;
}

.person-info {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding-left: 10px;
  margin-top: 10px;
}

.row-info {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
}

.avatar-icon {
  width: 17px;
  height: 17px;
  background: url('@/assets/images/fullCoverage/7-blue-man.png') no-repeat center;
  background-size: cover;

  &.green-man {
    background: url('@/assets/images/fullCoverage/9-green-man.png') no-repeat center;
    background-size: cover;
  }

  &.yellow-man {
    background: url('@/assets/images/fullCoverage/11-yellow-man.png') no-repeat center;
    background-size: cover;
  }
}

.person-dept {
  margin-top: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 14px;
  font-weight: 400;
  color: #fff;
  white-space: nowrap;
  opacity: 0.5;
}

.person-name {
  font-size: 14px;
  font-weight: 700;
  color: #fff;
}

.person-phone {
  margin-top: 2px;
  font-size: 16px;
  font-weight: 400;
  color: #fff;
}

/* 注册登记区域 */
.register-area {
  display: flex;
  gap: 20px;
}

.register-bg {
  box-sizing: border-box;
  width: 120px;
  height: 108px;
  padding: 7px 10px;
  background: url('@/assets/images/fullCoverage/12-register-bg.png') no-repeat center;
  background-size: 100% 100%;

  .register-photo {
    width: 99px;
    height: 64px;
  }
}

.cert-icon {
  width: 100px;
  height: 120px;
  background: url('@/assets/images/fullCoverage/13-no-icon.png') no-repeat center;
  background-size: contain;
}

.register-right {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 10px;
}

.r-row {
  display: flex;
  flex-direction: row;
  gap: 10px;
}

.register-item {
  display: flex;
  align-items: center;
}

.register-icon {
  width: 42px;
  height: 47px;
  margin-right: 10px;
}

.reg-icon {
  background: url('@/assets/images/fullCoverage/13-no-icon.png') no-repeat center;
  background-size: contain;
}

.time-icon {
  background: url('@/assets/images/fullCoverage/14-st-icon.png') no-repeat center;
  background-size: contain;
}

.valid-icon {
  background: url('@/assets/images/fullCoverage/15-et-icon.png') no-repeat center;
  background-size: contain;
}

.register-info {
  flex: 1;
}

.reg-label {
  font-size: 14px;
  color: #fff;
}

.reg-value {
  font-size: 16px;
  font-weight: 700;
  color: #7ff;

  &.st {
    color: #ffd060;
  }

  &.et {
    color: #42e75b;
  }
}

/* 信息共享区域 */
.share-area {
  display: flex;
  flex-direction: column;
  cursor: pointer;

  .dep-icons {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 114px;

    .dep-item {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 64px;
      height: 64px;
      font-size: 14px;
      font-weight: 400;
      color: #fff;

      span {
        position: relative;
        z-index: 1;
      }

      &::after {
        position: absolute;
        z-index: 0;
        width: 100%;
        height: 100%;
        content: '';
        background: url('@/assets/images/fullCoverage/16-share-bg.png') no-repeat center;
        background-size: 100% 100%;
        animation: rotate 10s linear infinite;
      }
    }
  }

  .arrow-icons {
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 0 104px;
    margin-top: -16px;

    .arrow-icon {
      width: 12px;
      height: 33px;
      background: url('@/assets/images/fullCoverage/17-arrow-l.png') no-repeat center;
      background-size: 100% 100%;

      &:last-child {
        transform: rotate(180deg);
      }
    }
  }

  .deep-box {
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 408px;
    height: 146px;
    margin-top: -20px;
    background: url('@/assets/images/fullCoverage/23-share-bg.png') no-repeat center;
    background-size: 100% 100%;

    .item-icon {
      box-sizing: border-box;
      width: 108px;
      height: 76px;
      padding-top: 9px;
      font-size: 14px;
      font-weight: 400;
      text-align: center;
      cursor: pointer;

      &.data {
        background: url('@/assets/images/fullCoverage/20-data-icon.png') no-repeat center;
        background-size: 100% 100%;
      }

      &.manage {
        margin-top: -30px;
        background: url('@/assets/images/fullCoverage/21-manage-icon.png') no-repeat center;
        background-size: 100% 100%;
      }

      &.sk {
        background: url('@/assets/images/fullCoverage/22-sk-icon.png') no-repeat center;
        background-size: 100% 100%;
      }
    }
  }
}

/* 右侧基本信息区域 */
.right-info-area {
  display: flex;
  flex-direction: row;
  align-items: center;

  .r-1-icon {
    width: 90px;
    height: 90px;
    background: url('@/assets/images/fullCoverage/r-1-icon.png') no-repeat center;
    background-size: cover;
  }

  .r-2-line {
    width: 84px;
    height: 222px;
    background: url('@/assets/images/fullCoverage/r-2-line.png') no-repeat center;
    background-size: contain;
  }

  .r-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-left: 8px;

    .right-row {
      display: flex;
      flex-direction: row;
      align-items: center;

      .col {
        margin-left: 13px;
      }
    }

    .info-icon-bg {
      position: relative;
      display: flex;
      width: 64px;
      height: 64px;

      &::after {
        position: absolute;
        z-index: 0;
        width: 100%;
        height: 100%;
        content: '';
        animation: rotate 10s linear infinite;
      }

      .info-icon {
        z-index: 1;
        width: 38px;
        height: 38px;
        margin: auto;
      }
    }

    .scale-icon {
      &::after {
        background: url('@/assets/images/fullCoverage/r-3-bg.png') no-repeat center;
        background-size: contain;
      }

      .info-icon {
        background: url('@/assets/images/fullCoverage/r-4-icon.png') no-repeat center;
        background-size: contain;
      }
    }

    .year-icon {
      &::after {
        background: url('@/assets/images/fullCoverage/r-5-bg.png') no-repeat center;
        background-size: contain;
      }

      .info-icon {
        background: url('@/assets/images/fullCoverage/r-6-icon.png') no-repeat center;
        background-size: contain;
      }
    }

    .loc-icon {
      &::after {
        background: url('@/assets/images/fullCoverage/r-7-bg.png') no-repeat center;
        background-size: contain;
      }

      .info-icon {
        background: url('@/assets/images/fullCoverage/r-8-icon.png') no-repeat center;
        background-size: contain;
      }
    }

    .river-loc-icon {
      &::after {
        background: url('@/assets/images/fullCoverage/r-9-bg.png') no-repeat center;
        background-size: contain;
      }

      .info-icon {
        background: url('@/assets/images/fullCoverage/r-10-icon.png') no-repeat center;
        background-size: contain;
      }
    }

    .info-label {
      margin-right: 10px;
      font-size: 14px;
      font-weight: 400;
      color: #feffff;
    }

    .info-value {
      font-family: YouSheBiaoTiHei;
      font-size: 20px;
      font-weight: 400;

      &.c-1 {
        color: #7ff;
      }

      &.c-2 {
        color: #ffd060;
      }

      &.c-3 {
        color: #57c7ff;
      }

      &.c-4 {
        color: #42e75b;
      }
    }
  }
}

/* 水文特征区域 */
.hydro-area {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
}

.hydro-row {
  display: flex;
  gap: 10px;
  height: 70px;
}

.hydro-col {
  flex: 1;
}

.hydro-item {
  display: flex;
  align-items: center;
  width: 220px;
}

.hydro-icon {
  box-sizing: border-box;
  flex-shrink: 0;
  width: 66px;
  height: 66px;
  margin-right: 6px;
}

.annual-icon {
  background: url('@/assets/images/fullCoverage/r-11-icon.png') no-repeat center;
  background-size: contain;
}

.rain-icon {
  background: url('@/assets/images/fullCoverage/r-13-icon.png') no-repeat center;
  background-size: contain;
}

.total-icon {
  background: url('@/assets/images/fullCoverage/r-14-icon.png') no-repeat center;
  background-size: contain;
}

.adjust-icon {
  background: url('@/assets/images/fullCoverage/r-15-icon.png') no-repeat center;
  background-size: contain;
}

.defense-icon {
  background: url('@/assets/images/fullCoverage/r-16-icon.png') no-repeat center;
  background-size: contain;
}

.water-level-icon {
  background: url('@/assets/images/fullCoverage/r-17-icon.png') no-repeat center;
  background-size: contain;
}

.design-icon {
  background: url('@/assets/images/fullCoverage/r-18-icon.png') no-repeat center;
  background-size: contain;
}

.dead-icon {
  background: url('@/assets/images/fullCoverage/r-19-icon.png') no-repeat center;
  background-size: contain;
}

.flood-icon {
  background: url('@/assets/images/fullCoverage/r-20-icon.png') no-repeat center;
  background-size: contain;
}

.normal-icon {
  background: url('@/assets/images/fullCoverage/r-21-icon.png') no-repeat center;
  background-size: contain;
}

.hydro-label {
  font-size: 14px;
  font-weight: 400;
  color: #fff;
}

.hydro-value {
  display: flex;
  align-items: center;
  width: 146px;
  height: 35px;
  padding-left: 8px;
  font-family: YouSheBiaoTiHei;
  font-size: 20px;
  font-weight: 400;
  color: #fff;
  background: url('@/assets/images/fullCoverage/r-12-val-bg.png') no-repeat center;
  background-size: 100% 100%;
}

:deep(.el-row) {
  margin: 0 !important;
}

:deep(.el-col) {
  padding: 0 !important;
}
</style>
