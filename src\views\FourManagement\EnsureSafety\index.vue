// 保障安全
<template>
  <div class="container">
    <left-region>
      <div class="left flx-1 overflow-hide flx-column">
        <base-title title="水库划界" />
        <div class="reservoir-division ml10 mt10">
          <arrow-title>管理范围和保护范围</arrow-title>
          <div class="mp-range mt16 mb16">
            <div
              v-for="(val, index) of mpRange"
              :key="val.label"
              :class="['mp-range-item flx', 'mp-range-item-' + index]"
            >
              <div class="mp-range-item-icon"></div>
              <div class="mp-range-item-content flx-column">
                <div class="mp-range-item-label">{{ val.label }}</div>
                <div class="mp-range-item-value">
                  {{ val.value }}
                  <span class="fs-14 mp-range-item-unit">{{ val.unit }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="flx-align-center mb18">
            <arrow-title v-show="activeName === 0">划界批复</arrow-title>
            <span class="no-active mr22 pointer" v-show="activeName !== 0" @click="activeName = 0">
              划界批复
            </span>
            <arrow-title v-show="activeName === 1">饮用水源地批复</arrow-title>
            <span class="no-active ml22 pointer" v-show="activeName !== 1" @click="activeName = 1">
              饮用水源地批复
            </span>
          </div>
          <div class="division-container" v-show="activeName === 0">
            <el-carousel
              class="h-full"
              motion-blur
              :autoplay="false"
              v-show="divisionData.length"
              indicator-position="none"
              :arrow="divisionData.length > 1 ? 'always' : 'never'"
            >
              <el-carousel-item v-for="(item, index) in divisionData" :key="index">
                <div :class="['division-carousel', 'division-carousel-' + item.length]">
                  <div
                    v-for="(val, index2) of item"
                    :key="val.id"
                    :class="[
                      'division-carousel-item',
                      'division-carousel-item-' + val.id,
                      val.isNodata ? '' : 'pointer'
                    ]"
                    @click="previewFilelist(item, index2, Boolean(val.isNodata))"
                  >
                    <div
                      :class="[
                        'file-pre flx-center',
                        val.isNodata ? 'file-pre-no' : '',
                        'file-pre-' + val.zwdlx
                      ]"
                    >
                      <Pdf
                        v-if="!val.isNodata && !val.isGeting && val.zwdlx === 'pdf'"
                        :pdf-url="previewUrl + val.file_path"
                        class="file-preview"
                        @onRendered="src => onRendered(src, index, index2)"
                      />
                      <img
                        v-if="!val.isNodata"
                        v-show="val.src"
                        class="full file-preview-img"
                        :src="String(val.src)"
                      />
                    </div>
                    <el-tooltip effect="dark" :content="val.mc" placement="top-end" :hide-after="0">
                      <div class="file-name sle">
                        {{ val.mc }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <el-empty
              class="empty"
              :image-size="fontSize(100)"
              :image="emptyImage"
              description="暂无划界批复数据"
              v-show="!divisionData.length"
            />
          </div>
          <div class="division-container" v-show="activeName === 1">
            <el-carousel
              class="h-full"
              motion-blur
              :autoplay="false"
              v-show="waterResourceData.length"
              indicator-position="none"
              :arrow="waterResourceData.length > 1 ? 'always' : 'never'"
            >
              <el-carousel-item v-for="(item, index) in waterResourceData" :key="index">
                <div :class="['division-carousel', 'division-carousel-' + item.length]">
                  <div
                    v-for="(val, index2) of item"
                    :key="val.id"
                    :class="[
                      'division-carousel-item',
                      'division-carousel-item-' + val.id,
                      val.isNodata ? '' : 'pointer'
                    ]"
                    @click="previewFilelist(item, index2, Boolean(val.isNodata))"
                  >
                    <div
                      :class="[
                        'file-pre flx-center',
                        val.isNodata ? 'file-pre-no' : '',
                        'file-pre-' + val.zwdlx
                      ]"
                    >
                      <Pdf
                        v-if="!val.isNodata && !val.isGeting && val.zwdlx === 'pdf'"
                        :pdf-url="previewUrl + val.file_path"
                        class="file-preview"
                        @onRendered="src => onRendered(src, index, index2)"
                      />
                      <img
                        v-if="!val.isNodata"
                        v-show="val.src"
                        class="full file-preview-img"
                        :src="String(val.src)"
                      />
                    </div>
                    <el-tooltip effect="dark" :content="val.mc" placement="top-end" :hide-after="0">
                      <div class="file-name sle">
                        {{ val.mc }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
            <el-empty
              class="empty"
              :image-size="fontSize(100)"
              :image="emptyImage"
              description="暂无饮用水源地批复数据"
              v-show="!waterResourceData.length"
            />
          </div>
        </div>
        <base-title class="mt16" title="防汛演练" />
        <div class="flood-prevention-drill flx-1 flx-column overflow-hide mt16">
          <div class="flood-prevention-drill-types mb16">
            <div
              v-for="(val, index) of floodPreventionDrillTypes"
              :key="val"
              class="flood-prevention-drill-type pointer flx-align-center"
              @click="previewFilelist(floodPreventionDrillTypeData[val], 0)"
            >
              <div
                :class="[
                  'flood-prevention-drill-type-icon',
                  'flood-prevention-drill-type-icon-' + index
                ]"
              ></div>
              <div class="flood-prevention-drill-type-title">
                {{ val }}
              </div>
            </div>
          </div>
          <div class="flood-prevention-drill-list flx-1 overflow-y-auto">
            <div
              v-for="val of floodPreventionDrillList"
              :key="val.id"
              class="flood-prevention-drill-list-item flx"
            >
              <div class="flood-prevention-drill-list-item-img-box">
                <el-image
                  class="flood-prevention-drill-list-item-img full"
                  :src="
                    Array.isArray(val.drillPicture) && val.drillPicture[0]
                      ? previewUrl + val.drillPicture[0].file_path
                      : ''
                  "
                ></el-image>
              </div>
              <div class="flood-prevention-drill-list-item-content flx-column flx-1 overflow-hide">
                <div class="flood-prevention-drill-list-item-content-time">
                  {{ val.drillTime }}
                </div>
                <div class="flood-prevention-drill-list-item-content-title sle">
                  {{ val.drillAddress }}
                </div>
                <div class="flood-prevention-drill-list-item-content-desc sle">
                  {{ val.drillName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </left-region>
    <right-region>
      <div class="right flx-1 overflow-hide flx-column">
        <base-title>
          值班值守
          <!-- <template #extra>
            <div class="flx-align-center" @click="handleDutyDetails">
              <span class="mr10">详情</span>
              <el-icon><ArrowRight /></el-icon>
            </div>
          </template> -->
        </base-title>
        <div :class="['on-duty', calendarActive === 1 ? 'show-calendar-body' : '']">
          <el-calendar class="" ref="calendar" v-model="calendarDate">
            <template #header="{ date }">
              <div class="flx-align-center w-full">
                <div class="flx-align-center w-full mrAuto">
                  <div
                    class="month-btn flx-center prev-month pointer"
                    @click="selectDate('prev-month')"
                  >
                    <el-icon><ArrowLeft /></el-icon>
                  </div>
                  <span class="calendar-header-date">{{ date }}</span>
                  <div
                    class="month-btn flx-center next-month pointer"
                    @click="selectDate('next-month')"
                  >
                    <el-icon><ArrowRight /></el-icon>
                  </div>
                </div>
                <div class="flx-align-center ml10">
                  <div
                    :class="['calendar-header-btn pointer', calendarActive === 0 ? 'active' : '']"
                    @click="calendarActive = 0"
                  >
                    今日值班
                  </div>
                  <div
                    :class="['calendar-header-btn pointer', calendarActive === 1 ? 'active' : '']"
                    @click="calendarActive = 1"
                  >
                    本月值班
                  </div>
                </div>
              </div>
            </template>
            <template #date-cell="{ data }">
              <div
                :class="[data.isSelected ? 'is-selected' : '', 'on-duty-date-cell']"
                @click="getDateMsg(data)"
              >
                <div class="on-duty-day">{{ Number(data.day.split('-').slice(2).join('')) }}</div>
                <div class="on-duty-name">{{ calendarDayData[data.day] }}</div>
              </div>
            </template>
          </el-calendar>
          <div v-show="calendarActive === 0" class="on-duty-descriptions mt0">
            <div class="on-duty-descriptions-item on-duty-descriptions-item-0">
              <div class="on-duty-descriptions-item-label">值班日期：</div>
              <div class="on-duty-descriptions-item-content">{{ dateData.dateTime }}</div>
            </div>
            <div class="on-duty-descriptions-item on-duty-descriptions-item-1">
              <div class="on-duty-descriptions-item-label">值班领导：</div>
              <div class="on-duty-descriptions-item-content">{{ dateData.leader }}</div>
            </div>
            <div class="on-duty-descriptions-item on-duty-descriptions-item-2">
              <div class="on-duty-descriptions-item-label">主班人员：</div>
              <div class="on-duty-descriptions-item-content">{{ dateData.primary }}</div>
            </div>
            <div class="on-duty-descriptions-item on-duty-descriptions-item-3">
              <div class="on-duty-descriptions-item-label">副班日班：</div>
              <div class="on-duty-descriptions-item-content">{{ dateData.daytime }}</div>
            </div>
            <div class="on-duty-descriptions-item on-duty-descriptions-item-4">
              <div class="on-duty-descriptions-item-label">副班夜班：</div>
              <div class="on-duty-descriptions-item-content">{{ dateData.night }}</div>
            </div>
          </div>
        </div>
        <base-title>
          应急保障
          <!-- <template #extra>
            <el-select
              class="emergency-select"
              v-model="emergencyYear"
              @change="handleEmergencyYearChange"
            >
              <el-option v-for="item in yearOptions" :key="item" :label="item" :value="item" />
            </el-select>
          </template> -->
        </base-title>
        <div class="emergency-support flx-1 flx-column overflow-hide">
          <div class="emergency-support-tabs">
            <div
              v-for="val of emergencySupportTabs"
              :key="val.label"
              :class="[
                'emergency-support-tab pointer',
                emergencySupportTab === val.value ? 'active' : ''
              ]"
              @click="handleSwitchEmergencyTab(val.value)"
            >
              {{ val.label }}
            </div>
          </div>
          <div class="emergency-support-content flx-1 flx-column overflow-hide">
            <div class="emergency-support-content-header flx">
              <div
                v-for="val of emergencySupportColumn"
                :key="val.label"
                class="flx-1 emergency-support-content-item"
              >
                {{ val.label }}
              </div>
            </div>
            <div class="emergency-support-content-body flx-1">
              <div
                v-for="val of emergencySupportData"
                :key="val.id"
                class="emergency-support-content-item-container flx"
              >
                <div
                  v-for="item of emergencySupportColumn"
                  :key="item.prop"
                  class="flx-1 emergency-support-content-item flx-center"
                >
                  {{ val[item.prop] }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </right-region>
    <file-preview-dialog ref="FilePreviewDialogRef"></file-preview-dialog>
    <base-dialog
      :showDialog="dutyVisible"
      :append-to-body="true"
      class="preview-dialog"
      :title="dutyTitle"
      :has-padding="false"
      :show-footer="false"
      @close="dutyVisible = false"
    >
      <el-descriptions class="c-descriptions" :column="1" label-width="90" border>
        <el-descriptions-item label="值班日期">{{ dateData.dateTime }}</el-descriptions-item>
        <el-descriptions-item label="值班领导">{{ dateData.leader }}</el-descriptions-item>
        <el-descriptions-item label="主班人员">{{ dateData.primary }}</el-descriptions-item>
        <el-descriptions-item label="副班日班">{{ dateData.daytime }}</el-descriptions-item>
        <el-descriptions-item label="副班夜班">{{ dateData.night }}</el-descriptions-item>
      </el-descriptions>
    </base-dialog>
  </div>
</template>

<script setup lang="ts">
import { useMap3dStore } from '@/stores/modules/map3d'
import type { CalendarDateType, CalendarInstance } from 'element-plus'
import { ResultData } from '@/api/interface'
import { GetFileListByPathPrefixVO, File } from '@/api/interface/perfectingSystem/VO.d'
import {
  QueryDemarcatInfoVO,
  QueryDrillListVO,
  QueryDutyViewListVO
} from '@/api/interface/ensureSafety/VO.d'
import { getFileListByPathPrefix } from '@/api/module/fourInstitutional/perfectingSystem'
import {
  queryDemarcatInfo,
  queryDrillList,
  queryDutyViewList,
  getEmergency,
  getAllWarehouse
} from '@/api/module/fourManagement/ensureSafety/index'
import { useUserStore } from '@/stores/modules/user'
import { previewUrl, downLoad } from '@/api/module/attachment'
import { fontSize } from '@/utils'
import { useDictionary } from '@/stores/modules/dictionary'
import { useSetMapGeoJsonLayer } from '@/hooks/useSetMapGeoJsonLayer'
// import FilePreview from '@/components/FilePreview/index.vue'
import Pdf from '@/components/PdfPreview/index.vue'
import { getAssetsImg } from '@/utils/index'

const { addComPointLayer, pointHeight, removeLayer } = useMap3dStore()

const setMapGeoJsonLayer = new useSetMapGeoJsonLayer()

const emptyImage = new URL('@/assets/images/warning/no-data.png', import.meta.url).href

const { formatDict, initDictionary } = useDictionary()

const app = getCurrentInstance()
const dayjs = app?.proxy?.$dayjs

/* 管理范围和保护范围 */
const mpRange = ref([
  {
    label: '管理范围',
    value: 0,
    unit: 'k㎡'
  },
  {
    label: '保护范围',
    value: 0,
    unit: 'k㎡'
  },
  {
    label: '界桩',
    value: 0,
    unit: '个'
  },
  {
    label: '公告牌',
    value: 0,
    unit: '个'
  }
])
/* 管理范围和保护范围 */

/* 划界批复、饮用水源地批复 */
function onRendered(src, index, index2) {
  divisionData.value[index][index2].isGeting = true
  divisionData.value[index][index2].src = src
}

const activeName = ref(0)

const divisionData = ref<File[][]>([])

const waterResourceData = ref<File[][]>([])

const FilePreviewDialogRef = ref()

function previewFilelist(fileList, index, isNodata = false) {
  if (isNodata) {
    return
  }
  if (!fileList.length) {
    return ElMessage.warning('文件列表为空')
  }
  FilePreviewDialogRef.value.previewFileList(fileList, index)
}

async function checkFile(id) {
  const res = await downLoad({ id }).catch(e => {
    console.log(e)
  })
  if (res.size) {
    return true
  } else {
    return false
  }
}

// function formatFiles(fileList) {
//   let arr: File[] = []
//   if (Array.isArray(fileList)) {
//     if (fileList.length > 0 && fileList.length < 4) {
//       arr = JSON.parse(JSON.stringify(fileList))
//       for (let i = 0; i < 4 - fileList.length; i++) {
//         arr.push({
//           mc: '暂无数据',
//           zwdlx: '',
//           isNodata: true,
//           id: 'nodata_' + i
//         })
//       }
//     }
//   }

//   return arr
// }
/* 划界批复、饮用水源地批复 */

/* 防汛演练 */
const floodPreventionDrillList = ref<QueryDrillListVO[]>([])

const floodPreventionDrillTypes = ['大坝安全管理应急预案', '防汛抢险应急管理预案']

const floodPreventionDrillTypeData = ref<{
  [propName: string]: File[]
}>({
  大坝安全管理应急预案: [],
  防汛抢险应急管理预案: []
})
/* 防汛演练 */

/* 值班值守 */
const calendarDate = ref()
const calendarDayData = ref({})
const calendar = ref<CalendarInstance>()
const calendarActive = ref(0)

function selectDate(val: CalendarDateType) {
  if (!calendar.value) return
  calendar.value.selectDate(val)
  nextTick(() => {
    const month = dayjs(calendarDate.value).format('YYYY-MM')
    queryDuty(
      dayjs(month).startOf('month').format('YYYY-MM-DD'),
      dayjs(month).endOf('month').format('YYYY-MM-DD')
    )
  })
}

const allDuty = ref<QueryDutyViewListVO[]>([])
function queryDuty(beginDate, endDate) {
  queryDutyViewList({
    beginDate,
    endDate,
    wrpcd: userStore.userInfo?.assignWrpcdList[0] as string,
    pageNum: 1,
    pageSize: 10000
  })
    .then(res => {
      calendarDayData.value = {}
      if (res.status === 200 && Array.isArray(res.data)) {
        allDuty.value = res.data
        const day = dayjs().format('YYYY-MM-DD')
        for (const item of res.data) {
          calendarDayData.value[item.dateTime] = item.leader
          if (day === item.dateTime) {
            dateData.value = item
          }
        }
      }
    })
    .catch(e => console.log(e))
}

const dutyVisible = ref(false)
const dateData = ref<QueryDutyViewListVO>({})
function getDateMsg(data) {
  dateData.value = {}
  for (const item of allDuty.value) {
    if (item.dateTime === data.day) {
      dateData.value = item
      break
    }
  }
  dutyVisible.value = true
}

const dutyTitle = computed(() => {
  const dateArr = dateData.value.dateTime ? dateData.value.dateTime.split('-') : ''
  return Array.isArray(dateArr)
    ? dateArr[0] + '年' + Number(dateArr[1]) + '月' + Number(dateArr[2]) + '日 值班安排'
    : ''
})
/* 值班值守 */

/* 应急保障 */
const emergencySupportAllData = ref()

// const emergencyYear = ref(2025)

// const yearOptions = [2020, 2021, 2022, 2023, 2024, 2025]

const emergencySupportTabs = [
  {
    label: '物资',
    value: 0
  },
  {
    label: '队伍',
    value: 1
  },
  {
    label: '专家',
    value: 2
  }
]

const emergencySupportTab = ref(0)

// function handleEmergencyYearChange() {}

function handleSwitchEmergencyTab(val) {
  emergencySupportTab.value = val
  switch (emergencySupportTab.value) {
    case 0:
      emergencySupportColumn.value = materialsColumns
      emergencySupportData.value = emergencySupportAllData.value.materialsVOs.map(m => {
        return {
          ...m,
          materialsType: formatMaterialsType(m.materialsType)
        }
      })
      break
    case 1:
      emergencySupportColumn.value = emergencyTeamColumns
      emergencySupportData.value = emergencySupportAllData.value.emergencyTeamVOs
      break
    case 2:
      emergencySupportColumn.value = emergencyExpertsColumns
      emergencySupportData.value = emergencySupportAllData.value.emergencyExpertsVOs
      break
  }
}

function formatMaterialsType(value: number) {
  return formatDict('MATERIALS_TYPE', value)
}

const materialsColumns = [
  {
    label: '名称',
    prop: 'name'
  },
  {
    label: '单位',
    prop: 'dept'
  },
  {
    label: '数量',
    prop: 'xysl'
  },
  {
    label: '分类',
    prop: 'materialsType'
  }
]

const emergencyTeamColumns = [
  {
    label: '职务',
    prop: 'position'
  },
  {
    label: '姓名',
    prop: 'name'
  },
  {
    label: '联系电话',
    prop: 'phoneNum'
  }
]

const emergencyExpertsColumns = [
  {
    label: '姓名',
    prop: 'name'
  },
  {
    label: '单位名称',
    prop: 'unitName'
  },
  {
    label: '职称',
    prop: 'title'
  },
  {
    label: '专业',
    prop: 'specialty'
  }
]

const emergencySupportColumn = ref<
  {
    label: string
    prop: string
  }[]
>([])

const emergencySupportData = ref([
  {
    id: 0,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 1,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 2,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 3,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 4,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 5,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 6,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 7,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 8,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 9,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  },
  {
    id: 10,
    name: '桩木',
    unit: '条',
    num: 1,
    type: '抢险物资'
  }
])
/* 应急保障 */

/* 应急仓库 */
const pointLayerId = 'warehousePointLayer'
function queryAllWarehouse() {
  getAllWarehouse()
    .then(res => {
      if (res.status === 200 && res.data) {
        console.log(res)
        const arr = res.data.map(item => {
          return {
            id: item.id,
            name: item.name,
            stnm: item.name,
            type: 'warehouse',
            lgtd: item.lon,
            lttd: item.lat,
            dtmel: pointHeight
          }
        })
        addComPointLayer({
          id: pointLayerId,
          data: arr,
          symbol: {
            billboard: {
              image: getAssetsImg('common/marker/warehouse.png'),
              width: 30,
              height: 30
            },
            label: {
              maxRange: 10000
            }
          }
        })
      }
    })
    .catch(e => {
      console.log(e)
    })
}
/* 应急仓库 */

async function getData(path): Promise<ResultData<GetFileListByPathPrefixVO> | null> {
  let res: ResultData<GetFileListByPathPrefixVO> | null
  try {
    res = await getFileListByPathPrefix({ path })
  } catch (e) {
    console.log(e)
    ElMessage.error('获取文件数据失败')
    return null
  }
  return res
}

const userStore = useUserStore()

onMounted(async () => {
  initDictionary(['MATERIALS_TYPE'])

  // 水库划界 管理范围和保护范围
  queryDemarcatInfo({ wrpcd: userStore.userInfo?.assignWrpcdList[0] as string })
    .then(res => {
      const data = res.data as QueryDemarcatInfoVO
      if (res.status === 200 && data) {
        mpRange.value[0].value = data.managerScope as number
        mpRange.value[1].value = data.protectScope as number
        mpRange.value[2].value = data.boundaryPost as number
        mpRange.value[3].value = data.billboard as number
      }
    })
    .catch(e => console.log(e))

  // 防汛演练
  queryDrillList({
    pageSize: 15,
    pageNum: 1
  })
    .then(res => {
      if (res.status === 200 && Array.isArray(res.data)) {
        floodPreventionDrillList.value = res.data
      }
    })
    .catch(e => console.log(e))

  // 值班值表
  queryDuty(
    dayjs().startOf('month').format('YYYY-MM-DD'),
    dayjs().endOf('month').format('YYYY-MM-DD')
  )

  // 应急保障
  getEmergency({ wrpcd: userStore.userInfo?.assignWrpcdList[0] as string })
    .then(res => {
      if (res.status === 200 && res.data) {
        emergencySupportAllData.value = res.data
        handleSwitchEmergencyTab(0)
      }
    })
    .catch(e => console.log(e))

  const divisionRes = await getData('四管-保障安全-水库划界-划界批复')
  if (
    divisionRes?.status === 200 &&
    Array.isArray(divisionRes.data) &&
    Array.isArray(divisionRes.data[0].fileList)
  ) {
    for (const item of divisionRes.data[0].fileList) {
      if (['png', 'jpeg', 'jpg'].indexOf(item.zwdlx) !== -1) {
        item.src = previewUrl + item.file_path
      }
      item.checkFile = await checkFile(item.id)
      if (!item.checkFile) {
        item.isGeting = true
      }
    }
    const num = divisionRes.data[0].fileList.length
    divisionData.value = []
    if (divisionRes.data[0].fileList.length) {
      for (let i = 0; i < num; i++) {
        const item = divisionRes.data[0].fileList[i]
        if (divisionData.value.length) {
          const lastNum = divisionData.value.length - 1
          if (divisionData.value[lastNum].length < 4) {
            divisionData.value[lastNum].push(item)
          } else {
            divisionData.value.push([item])
          }
        } else {
          divisionData.value.push([item])
        }
      }
    }
  }

  const waterResourceRes = await getData('四管-保障安全-水库划界-饮用水水源地批复')
  if (
    waterResourceRes?.status === 200 &&
    Array.isArray(waterResourceRes.data) &&
    Array.isArray(waterResourceRes.data[0].fileList)
  ) {
    for (const item of waterResourceRes.data[0].fileList) {
      if (['png', 'jpeg', 'jpg'].indexOf(item.zwdlx) !== -1) {
        item.src = previewUrl + item.file_path
      }
      item.checkFile = await checkFile(item.id)
      if (!item.checkFile) {
        item.isGeting = true
      }
    }
    const num = waterResourceRes.data[0].fileList.length
    waterResourceData.value = []
    if (waterResourceRes.data[0].fileList.length) {
      for (let i = 0; i < num; i++) {
        if (waterResourceData.value.length) {
          const lastNum = waterResourceData.value.length - 1
          if (waterResourceData.value[lastNum].length < 4) {
            waterResourceData.value[lastNum].push(waterResourceRes.data[0].fileList[i])
          } else {
            waterResourceData.value.push([waterResourceRes.data[0].fileList[i]])
          }
        } else {
          waterResourceData.value.push([waterResourceRes.data[0].fileList[i]])
        }
      }
    }
  }

  const floodPreventionDrillTypeOneRes =
    await getData('四管-保障安全-防汛演练-大坝安全管理应急预案')
  if (
    floodPreventionDrillTypeOneRes?.status === 200 &&
    Array.isArray(floodPreventionDrillTypeOneRes?.data) &&
    Array.isArray(floodPreventionDrillTypeOneRes?.data[0]?.fileList)
  ) {
    floodPreventionDrillTypeData.value['大坝安全管理应急预案'] =
      floodPreventionDrillTypeOneRes?.data[0]?.fileList
  }

  const floodPreventionDrillTypeTwoRes =
    await getData('四管-保障安全-防汛演练-防汛抢险应急管理预案')
  if (
    floodPreventionDrillTypeTwoRes?.status === 200 &&
    Array.isArray(floodPreventionDrillTypeTwoRes?.data) &&
    Array.isArray(floodPreventionDrillTypeTwoRes?.data[0].fileList)
  ) {
    floodPreventionDrillTypeData.value['防汛抢险应急管理预案'] =
      floodPreventionDrillTypeTwoRes?.data[0].fileList
  }

  queryAllWarehouse()

  nextTick(() => {
    // 设置地图图层
    setMapGeoJsonLayer.layerRender([
      {
        category: '5', //水库管理范围图层分类字典值
        textColor: 'rgb(253, 187, 29)',
        borderColor: 'rgb(253, 187, 29)',
        labelKey: '名称|name',
        text: '水库管理范围',
        zoom: true
      },
      {
        category: '6', //水库保护范围图层分类字典值
        textColor: 'rgb(245,128,128)',
        borderColor: 'rgb(245,128,128)',
        labelKey: '名称|name|',
        text: '水库保护范围',
        zoom: true
      }
    ])
  })
})

onBeforeUnmount(() => {
  removeLayer(pointLayerId)
  setMapGeoJsonLayer.removeAllLayer()
})
</script>

<style scoped lang="scss">
.mp-range {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px 0;

  .mp-range-item {
    .mp-range-item-icon {
      width: 48px;
      height: 48px;
      margin-right: 8px;
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .mp-range-item-label {
      font-size: 16px;
    }

    .mp-range-item-value {
      display: flex;
      align-items: baseline;
      font-family: YouSheBiaoTiHei, sans-serif;
      font-size: 25px;
      text-shadow: 0 4px 6.8px #00000040;
    }

    .mp-range-item-unit {
      margin-left: 2px;
      font-family: sans-serif;
    }

    &.mp-range-item-0 {
      .mp-range-item-icon {
        color: #7ff;
        background-image: url('@/assets/images/ensureSafety/mp-range-item-bg-0.png');
      }

      .mp-range-item-value,
      .mp-range-item-unit {
        color: #7ff;
      }
    }

    &.mp-range-item-1 {
      .mp-range-item-icon {
        color: #42e75b;
        background-image: url('@/assets/images/ensureSafety/mp-range-item-bg-1.png');
      }

      .mp-range-item-value,
      .mp-range-item-unit {
        color: #42e75b;
      }
    }

    &.mp-range-item-2 {
      .mp-range-item-icon {
        color: #ffd060;
        background-image: url('@/assets/images/ensureSafety/mp-range-item-bg-2.png');
      }

      .mp-range-item-value,
      .mp-range-item-unit {
        color: #ffd060;
      }
    }

    &.mp-range-item-3 {
      .mp-range-item-icon {
        color: #ff8a4b;
        background-image: url('@/assets/images/ensureSafety/mp-range-item-bg-3.png');
      }

      .mp-range-item-value,
      .mp-range-item-unit {
        color: #ff8a4b;
      }
    }
  }
}

.no-active {
  font-family: YouSheBiaoTiHei, sans-serif;
  font-size: 18px;
  color: rgb(119 255 255 / 60%);
}

.division-container {
  height: 196px;

  .division-carousel {
    display: grid;
    grid-template-columns: repeat(4, 85px);
    gap: 8px;
    justify-content: center;

    &.division-carousel-1 {
      display: flex;
      gap: 0;
      align-items: center;
      justify-content: center;

      .division-carousel-item {
        width: 320px;

        .file-pre {
          height: 160px;
        }

        &:nth-child(1) {
          margin-top: 0;
        }
      }
    }

    &.division-carousel-2 {
      grid-template-columns: repeat(2, 1fr);

      .division-carousel-item {
        width: 190px;

        .file-pre {
          height: 160px;
        }

        &:nth-child(1),
        &:nth-child(2) {
          margin-top: 0;
        }
      }
    }

    &.division-carousel-3 {
      grid-template-columns: repeat(3, 1fr);

      .division-carousel-item {
        width: 130px;

        .file-pre {
          height: 133px;
        }

        &:nth-child(2) {
          margin-top: 0;
        }

        &:nth-child(1),
        &:nth-child(3) {
          margin-top: 34px;
        }
      }
    }

    .division-carousel-item {
      width: 85px;

      &:nth-child(2),
      &:nth-child(3) {
        margin-top: 34px;
      }

      .file-pre {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: 122px;
        padding: 8px;
        overflow: hidden;
        background-image: url('@/assets/images/ensureSafety/division-carousel-item-bg.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;

        &.file-pre-no {
          &::after {
            width: 68px;
            height: 106px;
            content: '';
            background-image: url('@/assets/images/ensureSafety/division-carousel-item-nodata.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }

        &.file-pre-pdf,
        &.file-pre-doc,
        &.file-pre-docx {
          &::after {
            width: 100%;
            height: 100%;
            content: '';
            background-image: url('@/assets/images/common/pdf-tem.png');
            background-repeat: no-repeat;
            background-size: 100% 100%;
          }
        }

        .file-preview {
          position: absolute;
          z-index: -1;
        }
      }

      .file-name {
        position: relative;
        height: 30px;
        padding: 0 4px;
        margin-top: 8px;
        font-size: 14px;
        line-height: 26px;
        color: #fff;
        text-align: center;

        &::before {
          position: absolute;
          right: 0;
          bottom: 6px;
          left: 0;
          display: block;
          height: 15.42px;
          content: '';
          background-color: transparent;
          background-image: linear-gradient(180deg, #09f0 -5.88%, #09f 161.76%);
          opacity: 0.6;
        }

        &::after {
          position: absolute;
          right: 5px;
          bottom: 0;
          left: 5px;
          display: block;
          height: 13.61px;
          content: '';
          background-color: transparent;
          background-image: linear-gradient(180deg, #09f0 -5.88%, #09f 161.76%);
          opacity: 0.39;
        }
      }
    }
  }

  :deep(.el-carousel__arrow) {
    width: 28px;
    height: 28px;
    padding: 0;
    background-color: transparent;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  :deep(.el-carousel__arrow--left) {
    left: 0;
    background-image: url('@/assets/images/ensureSafety/el-carousel__arrow--left-bg.png');
  }

  :deep(.el-carousel__arrow--right) {
    right: 0;
    background-image: url('@/assets/images/ensureSafety/el-carousel__arrow--right-bg.png');
  }
}

.empty {
  :deep(.el-empty__description) {
    p {
      color: #fff;
    }
  }
}

.flood-prevention-drill-types {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  .flood-prevention-drill-type {
    background-image: url('@/assets/images/ensureSafety/flood-prevention-drill-type-bg.png');
    background-repeat: no-repeat;
    background-position: left bottom;
    background-size: 100% 60px;

    .flood-prevention-drill-type-icon {
      width: 66px;
      height: 64px;
      margin-right: 4px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 130% 130%;

      &.flood-prevention-drill-type-icon-0 {
        background-image: url('@/assets/images/ensureSafety/flood-prevention-drill-type-icon-0.png');
      }

      &.flood-prevention-drill-type-icon-1 {
        background-image: url('@/assets/images/ensureSafety/flood-prevention-drill-type-icon-1.png');
      }
    }

    .flood-prevention-drill-type-title {
      width: 107px;
      font-size: 16px;
      color: #fff;
    }
  }
}

.flood-prevention-drill-list {
  overflow-y: auto;

  .flood-prevention-drill-list-item {
    margin-bottom: 14px;
    background-image: url('@/assets/images/ensureSafety/flood-prevention-drill-list-item-bg.png');
    background-repeat: no-repeat;
    background-position: right bottom;
    background-size: 100% 60px;

    .flood-prevention-drill-list-item-img-box {
      width: 120px;
      min-width: 120px;
      height: 72px;
      padding: 3px;
      margin-right: 12px;
      background-image: url('@/assets/images/ensureSafety/flood-prevention-drill-list-item-img-box-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .flood-prevention-drill-list-item-img {
        border-radius: 4px;
      }
    }

    .flood-prevention-drill-list-item-content {
      justify-content: space-evenly;

      .flood-prevention-drill-list {
        overflow-y: auto;
      }

      .flood-prevention-drill-list-item-content-time {
        font-size: 14px;
        color: rgb(255 255 255 / 70%);
      }

      .flood-prevention-drill-list-item-content-title {
        font-size: 16px;
        color: #7ff;
      }

      .flood-prevention-drill-list-item-content-desc {
        font-size: 14px;
        color: #fff;
      }
    }
  }
}

.on-duty {
  width: 424px;
  margin: 10px auto 14px;

  :deep(.el-calendar__body) {
    display: none;
  }

  &.show-calendar-body {
    :deep(.el-calendar__body) {
      display: block;
    }
  }

  .on-duty-descriptions {
    .on-duty-descriptions-item {
      display: flex;
      height: 36px;
      margin-top: 14px;
      font-size: 16px;
      line-height: 36px;
      background: url('@/assets/images/ensureSafety/on-duty-descriptions-item-bg.png') no-repeat;
      background-size: 100% 100%;

      &::before {
        width: 58px;
        height: 36px;
        margin-right: 15px;
        content: '';
        background-repeat: no-repeat;
        background-size: 100% 100%;
      }

      &-0 {
        &::before {
          background-image: url('@/assets/images/ensureSafety/on-duty-descriptions-item-icon-0.png');
        }
      }

      &-1 {
        &::before {
          background-image: url('@/assets/images/ensureSafety/on-duty-descriptions-item-icon-1.png');
        }
      }

      &-2 {
        &::before {
          background-image: url('@/assets/images/ensureSafety/on-duty-descriptions-item-icon-2.png');
        }
      }

      &-3 {
        &::before {
          background-image: url('@/assets/images/ensureSafety/on-duty-descriptions-item-icon-3.png');
        }
      }

      &-4 {
        &::before {
          background-image: url('@/assets/images/ensureSafety/on-duty-descriptions-item-icon-4.png');
        }
      }
    }
  }

  .month-btn {
    width: 26px;
    height: 26px;
    font-size: 16px;
    color: #7ff;
    background-image: url('@/assets/images/ensureSafety/on-duty-btn-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &:hover {
      opacity: 0.8;
    }
  }

  .calendar-header-date {
    margin-right: auto;
    margin-left: auto;
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 20px;
    color: #7ff;
  }

  .calendar-header-btn {
    width: 80px;
    height: 28px;
    font-size: 14px;
    line-height: 28px;
    color: #fff;
    text-align: center;
    background-image: url('@/assets/images/ensureSafety/calendar-header-btn-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &.active {
      color: #7ff;
      background-image: url('@/assets/images/ensureSafety/calendar-header-btn-active-bg.png');
    }
  }

  :deep(.el-calendar) {
    background-color: transparent;
    background-image: url('@/assets/images/ensureSafety/on-duty-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  :deep(.el-calendar-day) {
    box-sizing: border-box;
    width: 100%;
    height: 56px;
    padding: 3px;
    text-align: center;
    background-color: rgb(0 0 0 / 28%);
    border: 2px solid transparent;
    border-radius: 2px;
  }

  :deep(.el-calendar__header) {
    padding: 10px 0 0 10px;
    border-bottom: none;
  }

  :deep(.el-calendar__body) {
    padding: 0 10px 14px;
  }

  :deep(.el-calendar-table) {
    display: flex;
    flex-direction: column;

    thead {
      display: flex;
      gap: 4px;
      width: 100%;

      tr {
        width: 100%;

        th {
          flex: 1;
          height: 42px;
        }
      }

      th {
        font-family: YouSheBiaoTiHei, sans-serif;
        font-size: 16px;
        color: #7ff;
      }
    }

    tbody {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    tr {
      display: flex;
      gap: 4px;

      &:first-child {
        td {
          border: none;
        }
      }
    }

    td {
      flex: 1;
      border: none;
      border-radius: 2px;

      &:first-child {
        border: none;
      }

      &.is-selected {
        .el-calendar-day {
          border-color: #7ff;
        }

        .on-duty-day {
          color: #7ff;
        }

        background-color: transparent;
      }

      .on-duty-day {
        font-family: YouSheBiaoTiHei, sans-serif;
        font-size: 16px;
        line-height: 22px;
        color: #fff;
      }

      .on-duty-name {
        width: 100%;
        height: 22px;
        font-size: 12px;
        line-height: 22px;
        text-align: center;
        background-color: #fff;
      }

      &:nth-child(1) {
        .on-duty-name {
          color: #ffd060;
          background-color: rgb(255 208 96 / 30%);
        }
      }

      &:nth-child(2) {
        .on-duty-name {
          color: #ff8a4b;
          background-color: rgb(255 138 75 / 30%);
        }
      }

      &:nth-child(3) {
        .on-duty-name {
          color: #ff4b4b;
          background-color: rgb(255 75 75 / 30%);
        }
      }

      &:nth-child(4) {
        .on-duty-name {
          color: #42e75b;
          background-color: rgb(66 231 91 / 30%);
        }
      }

      &:nth-child(5) {
        .on-duty-name {
          color: #2fb3e9;
          background-color: rgb(47 179 233 / 30%);
        }
      }

      &:nth-child(6) {
        .on-duty-name {
          color: #52b1ff;
          background-color: rgb(82 177 255 / 30%);
        }
      }

      &:nth-child(7) {
        .on-duty-name {
          color: #09f;
          background-color: rgb(0 153 255 / 30%);
        }
      }
    }

    &:not(.is-range) {
      td {
        &.prev,
        &.next {
          .el-calendar-day {
            color: rgb(255 255 255 / 40%);
          }

          .on-duty-day {
            color: rgb(255 255 255 / 40%);
          }

          .on-duty-name {
            background-color: rgb(9 85 143 / 30%);
            opacity: 0.4;
          }
        }
      }
    }
  }
}

.emergency-select {
  width: 90px !important;

  :deep(.el-select__wrapper) {
    background-color: transparent;
    box-shadow: unset !important;
  }

  :deep(.el-select__input),
  :deep(.el-select__placeholder) {
    font-size: 14px;
    color: #7ff;
  }

  :deep(.el-select__caret) {
    font-size: 20px !important;
    color: rgb(119 255 255 / 50%);
  }
}

.emergency-support {
  width: 424px;
  margin: 10px auto 0;

  .emergency-support-tabs {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;

    .emergency-support-tab {
      height: 34px;
      margin-bottom: 10px;
      font-size: 16px;
      line-height: 34px;
      color: #fff;
      text-align: center;
      background-image: url('@/assets/images/ensureSafety/emergency-support-tab-bg.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &.active {
        color: #7ff;
        background-image: url('@/assets/images/ensureSafety/emergency-support-tab-bg-active.png');
      }
    }
  }

  .emergency-support-content-header {
    height: 36px;
    font-size: 14px;
    line-height: 36px;
    color: #7ff;
    text-align: center;
    background-image: linear-gradient(0deg, #0080ff00 0%, #0080ff73 100%);
  }

  .emergency-support-content-body {
    overflow-y: auto;
    background-image: linear-gradient(180deg, rgb(0 128 255 / 15%) 0%, rgb(0 128 255 / 15%) 80%);
  }

  .emergency-support-content-item-container {
    box-sizing: border-box;
    min-height: 40px;
    padding: 6px 0;

    &:hover {
      background-image: linear-gradient(270deg, #fff0 0.13%, #00a7ff36 99.87%);

      .emergency-support-content-item {
        font-size: 16px;
        color: #7ff;
      }
    }
  }

  .emergency-support-content-item {
    font-size: 14px;
    color: #fff;
  }
}

.c-descriptions {
  :deep(.el-descriptions__body) {
    background-color: transparent;
  }

  :deep(.el-descriptions__label) {
    background-color: rgb(0 153 255 / 50%);
  }

  :deep(.el-descriptions__cell) {
    color: #fff;
    border-color: #3f7cba !important;
  }
}
</style>
