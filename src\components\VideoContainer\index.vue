<template>
  <div
    v-show="videoInfo.id"
    class="video-player"
    v-loading="loading"
    element-loading-background="rgba(0, 0, 0, 0.5)"
  >
    <div v-if="showVideo" class="video-js-box" @dblclick="handleDbClick">
      <video ref="videoPlayerRef" class="video-js"></video>
    </div>
    <div class="video-image" v-show="!showVideo">
      <!-- <img
        class="iframe-operation iframe-full"
        src="@/assets/images/waterEngineering/reservoirMange/VideoMonitoring/default-no.jpg"
      /> -->
      <el-icon class="video-play-icon" @click.stop="getUrl"></el-icon>
    </div>
  </div>
</template>

<script>
import "video.js/dist/video-js.css";
import videojs from "video.js";
import "videojs-flvjs-es6";
import $http from "@/utils/request";

export default {
  name: "VideoContainer",
  props: {
    videoInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      showVideo: false,
      loading: false,
      currentInstance: null,
    };
  },
  watch: {
    "videoInfo.id": {
      handler(newValue) {
        if (newValue) {
          this.showVideo = true;
          this.loading = true;
          this.$nextTick(() => {
            this.playVideo(this.videoInfo);
          });
        } else {
          this.showVideo = false;
          this.currentInstance?.dispose();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    playVideo(data) {
      // 第一个选中的要播放的video标签, 记得是video标签,
      if (this.$refs.videoPlayerRef) {
        this.currentInstance = videojs(
          this.$refs.videoPlayerRef,
          {
            language: "zh-CN", // 语言
            preload: "auto", // 预加载
            playsinline: true,
            autoplay: true, // 是否自动播放
            controls: false, // 是否显示控件
            fluid: false, // 是否自适应大小
            objectFit: "fill", // 填充模式
            muted: true, // 是否静音
            notSupportedMessage: "此设备视频暂时无法播放", // 不支持提示语
            techOrder: ["flvjs", "html5"], // 优先顺序
            flvjs: {
              // 播放器配置
              mediaDataSource: {
                isLive: true,
                hasAudio: false,
              },
              withCredentials: true, // 跨域请求是否携带cookie
              cors: true, // 是否允许跨域请求
            },
            html5: {
              // 播放器配置
              hls: {
                withCredentials: true,
              },
            },
          },
          () => {
            videojs.hook("beforeerror", (player, err) => {
              if (err) {
                //
                ElMessage.error(
                  "获取设备信息失败，原因包括网络异常、电源异常等，请联系管理员排查现场"
                );
                this.showVideo = false;
              }
              // 清除错误，避免 error 事件在控制台抛出错误
              return null;
            });
          }
        );

        // 暂停播放器
        this.currentInstance.pause();
        // 重置播放器
        this.currentInstance.reset();

        setTimeout(() => {
          this.currentInstance.src([
            { src: data.flv, type: "video/x-flv" },
            { src: data.m3u8, type: "application/x-mpegURL" },
          ]);
          this.currentInstance.load();
          this.currentInstance.play();
          this.loading = false;
        });
      }
    },
    handleDbClick() {
      if (!this.currentInstance) return;
      const isFullScreen = this.currentInstance.isFullscreen();
      isFullScreen
        ? this.currentInstance?.exitFullscreen()
        : this.currentInstance?.requestFullscreen();
    },
    // 获取视频地址
    async getUrl() {
      this.loading = true;
      let { ip, code, password } = this.videoInfo;

      if (process.env.NODE_ENV === "development") {
        const url = new URL(ip);
        ip = (password ? "/video1" : "/video2") + url.pathname;
      }

      let data = {};
      if (password) {
        data = {
          opentype: "streaminfo",
          stcd: code,
          appKey: password,
        };
      } else {
        data = {
          ftype: "videoList",
          deviceName: code,
        };
      }

      const res = await $http.postFormData(ip, data).catch(() => {
        this.$message.error(
          "获取设备信息失败，原因包括网络异常、电源异常等，请联系管理员排查现场"
        );
        this.showVideo = false;
        this.loading = false;
      });

      if (password) {
        if (res.code === 0) {
          this.showVideo = true;
          this.$nextTick(() => {
            this.playVideo({
              flv: res.data.flv,
              flvHd: res.data.flv_hd,
              m3u8: res.data.m3u8,
              m3u8Hd: res.data.m3u8_hd,
            });
          });
        } else {
          this.$message.error(
            res.msg ||
              "获取设备信息失败，原因包括网络异常、电源异常等，请联系管理员排查现场"
          );
          this.showVideo = false;
          this.loading = false;
        }
      } else {
        this.showVideo = true;
        const { liveAddress, hdAddress, flv, flvHd } = res.reals[0];
        this.$nextTick(() => {
          this.playVideo({
            flv: flv,
            flvHd: flvHd,
            m3u8: liveAddress,
            m3u8Hd: hdAddress,
          });
        });
      }
    },
  },
  mounted() {},
  beforeDestroy() {
    this.currentInstance?.dispose();
  },
};
</script>
<style lang="scss" scoped>
.video-player {
  position: relative;
  width: 100%;
  height: 100%;
  .video-js-box {
    background-color: #000000 !important;
  }
  .video-js-box,
  .video-js {
    width: 100%;
    height: 100%;
    background-color: transparent;
  }
  .video-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    .video-play-icon {
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 51;
      font-size: 60px !important;
      color: #1296db;
      cursor: pointer;
      transform: translate(-50%, -50%);
    }
    img {
      width: 100%;
      height: 100%;
    }
  }
}

::v-deep .video-js .vjs-tech {
  object-fit: fill;
}
</style>
