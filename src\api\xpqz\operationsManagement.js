import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getGcxcList: apiUrl.defaultUrl + '/xpqz/operationsManagement/getGcxcList',
  getGcxcDetail: apiUrl.defaultUrl + '/xpqz/operationsManagement/getGcxcDetail',
  deleteGcxc: apiUrl.defaultUrl + '/xpqz/operationsManagement/deleteGcxc',
  saveGcxc: apiUrl.defaultUrl + '/xpqz/operationsManagement/saveGcxc',
  getWxyhList: apiUrl.defaultUrl + '/xpqz/operationsManagement/getWxyhList',
  saveWxyh: apiUrl.defaultUrl + '/xpqz/operationsManagement/saveWxyh',
  deleteWxyh: apiUrl.defaultUrl + '/xpqz/operationsManagement/deleteWxyh',
  getYhtzList: apiUrl.defaultUrl + '/xpqz/operationsManagement/getYhtzList',
  saveYhtz: apiUrl.defaultUrl + '/xpqz/operationsManagement/saveYhtz',
  deleteYhtz: apiUrl.defaultUrl + '/xpqz/operationsManagement/deleteYhtz',
  statisticsYhtz:
    apiUrl.defaultUrl + '/xpqz/operationsManagement/statisticsYhtz',
  statisticsWxyh:
    apiUrl.defaultUrl + '/xpqz/operationsManagement/statisticsWxyh'
}

/**
 * 运行管理-工程巡查列表接口
 * @param data
 */
export function getGcxcList (data) {
  return $http.post(api.getGcxcList, data)
}

/**
 * 运行管理-工程巡查接口
 * 根据id查询详细信息
 */
export function getGcxcDetail (data) {
  return $http.post(api.getGcxcDetail, data)
}

/**
 * 运行管理-工程巡查删除接口
 */
export function deleteGcxc (data) {
  return $http.post(api.deleteGcxc, data)
}

/**
 * 运行管理-工程巡查新增、修改接口
 */
export function saveGcxc (data) {
  return $http.post(api.saveGcxc, data)
}

/**
 * 运行管理-维修养护列表接口
 */
export function getWxyhList (data) {
  return $http.post(api.getWxyhList, data)
}

/**
 * 运行管理-维修养护新增 / 修改接口
 */
export function saveWxyh (data) {
  return $http.post(api.saveWxyh, data)
}

/**
 * 运行管理-维修养护删除接口
 */
export function deleteWxyh (data) {
  return $http.post(api.deleteWxyh, data)
}

/**
 * 运行管理-隐患台账统计图表接口
 */
export function statisticsWxyh (data) {
  return $http.post(api.statisticsWxyh, data)
}

/**
 * 运行管理-隐患台账列表接口
 */
export function getYhtzList (data) {
  return $http.post(api.getYhtzList, data)
}

/**
 * 运行管理-隐患台账修改
 */
export function saveYhtz (data) {
  return $http.post(api.saveYhtz, data)
}

/**
 * 运行管理-隐患台账删除
 */
export function deleteYhtz (data) {
  return $http.post(api.deleteYhtz, data)
}

/**
 * 运行管理-隐患台账统计图表
 */
export function statisticsYhtz (data) {
  return $http.post(api.statisticsYhtz, data)
}
