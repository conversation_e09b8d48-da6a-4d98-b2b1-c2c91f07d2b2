/* 运行监控-预报调度 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getHydrologicList: apiUrl.defaultUrl + '/yb/swyb/pageList',
  getHydrologicDetail: apiUrl.defaultUrl + '/yb/swyb/getResult',
  exportHydrologicScheme: apiUrl.defaultUrl + '/yb/swyb/export',
  deleteHydrologicScheme: apiUrl.defaultUrl + '/yb/swyb/delete',

  getReservoirDispatchList: apiUrl.defaultUrl + '/yb/dispatch/pageList',
  exportReservoirDispatch: apiUrl.defaultUrl + '/yb/dispatch/export',
  deleteReservoirDispatch: apiUrl.defaultUrl + '/yb/dispatch/delete',
  getDispatchDetail: apiUrl.defaultUrl + '/yb/dispatch/getResult'
}

/**
 * 运行监控-预报调度-水文预报方案-列表
 * id
 */
export function getHydrologicList(params) {
  return $http.post(api.getHydrologicList, params)
}
/**
 * 运行监控-预报调度-水文预报方案-详情
 * id
 */
export function getHydrologicDetail(params) {
  return $http.get(api.getHydrologicDetail, params)
}

/**
 * 运行监控-预报调度-水文预报方案-删除
 */
export function deleteHydrologicScheme(params) {
  return $http.get(api.deleteHydrologicScheme, params)
}

/**
 * 运行监控-预报调度-水文预报方案-导出
 * id
 */
export function exportHydrologicScheme(params) {
  return $http.getDownLoad(api.exportHydrologicScheme, params, undefined, "blob")
}

/**
 * 运行监控-预报调度-水库调度-列表
 * id
 */
export function getReservoirDispatchList(params) {
  return $http.post(api.getReservoirDispatchList, params)
}

/**
 * 运行监控-预报调度-水库调度-详情
 * id
 */
export function getDispatchDetail(params) {
  return $http.get(api.getDispatchDetail, params)
}
/**
 * 运行监控-预报调度-水库调度-删除
 */
export function deleteReservoirDispatch(params) {
  return $http.get(api.deleteReservoirDispatch, params)
}

/**
 * 运行监控-预报调度-水库调度-导出
 * id
 */
export function exportReservoirDispatch(params) {
  return $http.getDownLoad(api.exportReservoirDispatch, params, undefined, "blob")
}