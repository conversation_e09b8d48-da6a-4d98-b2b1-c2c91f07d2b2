/* 基础管理-经费管理-经费明细 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteById: apiUrl.defaultUrl + '/scoreFundDetails/deleteById',
  insert: apiUrl.defaultUrl + '/scoreFundDetails/insert',
  list: apiUrl.defaultUrl + '/scoreFundDetails/list',
  update: apiUrl.defaultUrl + '/scoreFundDetails/update'
}

/**
 * 经费管理-经费明细-删除
 * id
 */
export function deleteById (data) {
  return $http.post(api.deleteById, data)
}

/**
 * 经费管理-经费明细-新增
 * "fundId": 0, 年度经费id
 * "id": 0, 主键
 * "imgId": "string", 图片id
 * "jflx": 0, 经费类型：1-维修养护经费，2-运行管理经费
 * "sxjf": 0, 所需经费
 * "xgwjId": "string", 相关文件id
 * "yt": "string" 用途
 */
export function insert (data) {
  return $http.post(api.insert, data)
}

/**
 * 经费管理-经费明细-分页查询
 * "fundId": 0, 年度经费id
 * "id": 0, 主键
 * "imgId": "string", 图片id
 * "jflx": 0, 经费类型：1-维修养护经费，2-运行管理经费
 * "sxjf": 0, 所需经费
 * "xgwjId": "string", 相关文件id
 * "yt": "string" 用途
 */
export function list (data) {
  return $http.post(api.list, data)
}

/**
 * 经费管理-经费明细-编辑
 * "fundId": 0, 年度经费id
 * "id": 0, 主键
 * "imgId": "string", 图片id
 * "jflx": 0, 经费类型：1-维修养护经费，2-运行管理经费
 * "sxjf": 0, 所需经费
 * "xgwjId": "string", 相关文件id
 * "yt": "string" 用途
 */
export function update (data) {
  return $http.post(api.update, data)
}
