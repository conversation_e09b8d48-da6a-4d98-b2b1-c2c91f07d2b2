<template>
  <div class="editor-container">
    <Toolbar
      class="editor-toolbar"
      :editor="editor"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      class="editor-con"
      v-model="htmlVal"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onCreated="onEditorCreated"
    />
  </div>
</template>

// 富文本编辑器，在线文档：https://www.wangeditor.com/
<script>
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'
import { upload, previewUrl } from '@/api/attachment'
export default {
  name: 'WEditor',
  components: {
    Editor,
    Toolbar
  },
  model: {
    prop: 'customProp', // 自定义属性名
    event: 'update:customProp' // 自定义事件名 方式一
  },
  props: {
    customProp: {
      type: String,
      default: ''
    },
    toolbarConfig: {
      type: Object,
      default: () => {
        return {
          excludeKeys: ['group-image', 'group-video'], // 默认不显示视频上传、网络图片上传
          insertKeys: {
            index: 20,
            keys: 'uploadImage'
          }
        }
      }
    },
    config: {
      type: Object,
      default: () => {
        return {}
      }
    },
    mode: {
      type: String,
      default: 'default'
    },
    uploadModule: {
      type: String,
      default: 'common'
    },
    imgParams: { // 图片上传参数
      type: Object,
      default: () => {
        return {}
      }
    },
    videoParams: { // 视频上传参数
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      editor: null,
      editConfig: {
        placeholder: '请输入内容...',
        MENU_CONF: {
          uploadImage: {
            allowedFileTypes: ['image/png', 'image/jpeg'],
            customUpload: async (file, insertFn) => {
              this.uploadFileFn(file, insertFn)
            }
          }
        }
      }
    }
  },
  beforeDestroy () {
    const editor = this.editor
    if (editor == null) return
    editor.destroy()
  },
  computed: {
    htmlVal: {
      get () {
        return this.customProp
      },
      set (val) {
        // 防止富文本内容为空时，校验失败
        if (this.editor.isEmpty()) val = ''
        this.$emit('update:customProp', val)
      }
    },
    reEditorConfig () {
      const obj = JSON.parse(JSON.stringify(this.editorConfig))
      obj.MENU_CONF.uploadImage = { ...obj.MENU_CONF.uploadImage, ...this.imgParams }
      obj.MENU_CONF.uploadVideo = { ...obj.MENU_CONF.uploadVideo, ...this.videoParams }
      return obj
    },
    editorConfig () {
      return { ...this.editConfig, ...this.config }
    }
  },
  methods: {
    onEditorCreated (editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    async uploadFileFn (file, insertFn) {
      // 自定义图片上传
      const formData = new FormData()
      formData.append('file', file)
      formData.append('module', this.uploadModule)
      let fileUrl
      try {
        const res = await upload(formData)
        if (res.status === 200 && res.data) {
          fileUrl = previewUrl + res.data.file_path
        }
        insertFn(fileUrl)
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-container {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  height: 100%;
  border: 1px solid #ccc;
}

.editor-toolbar {
  border-bottom: 1px solid #ccc
}

.editor-con {
  flex: 1;
  overflow: hidden;
}
</style>
