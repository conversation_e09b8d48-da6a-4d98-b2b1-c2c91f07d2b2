/* 基础管理-档案管理-文件 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteById: apiUrl.defaultUrl + '/scoreDocFile/deleteById',
  fileUpload: apiUrl.defaultUrl + '/scoreDocFile/fileUpload',
  fileUpdate: apiUrl.defaultUrl + '/scoreDocFile/fileEdit',
  list: apiUrl.defaultUrl + '/scoreDocFile/list',
  listExport: apiUrl.defaultUrl + '/scoreDocFile/listExport',
  getDocsOverview: apiUrl.defaultUrl + '/moreProject/getDocsOverview',
  getDocsByProjectId: apiUrl.defaultUrl + '/moreProject/getDocsByProjectId',
  getJZFileListByPathDictCode: apiUrl.defaultUrl + '/fileBindDict/getJZFileListByPathDictCode',
  saveFileBindDict: apiUrl.defaultUrl + '/fileBindDict/saveFileBindDict',
  getDocClassify: apiUrl.defaultUrl + '/moreProject/getDocClassify',
}

/**
 * 基础管理-档案管理-文件-删除
 */
export function deleteById(data) {
  return $http.post(api.deleteById, data)
}

/**
 * 基础管理-档案管理-文件-新增
 * "docId": "string", 档案分类id
 * "file": "object" 文件
 */
export function fileUpload(data) {
  return $http.postUpLoadFile(api.fileUpload, data)
}

/**
 * 基础管理-档案管理-文件-编辑
 */
export function fileUpdate(data) {
  return $http.postUpLoadFile(api.fileUpdate, data)
}

/**
 * 基础管理-档案管理-文件-分页查询
 * "docId": "string"
 */
export function list(data, params) {
  return $http.postParams(api.list, data, params)
}

/**
 * 基础管理-档案管理-文件-导出
 *
 */
export function fileListExport(data, contentType, responseType) {
  return $http.postDownLoad(api.listExport, data, contentType,
    responseType, false, true)
}
/**
 * 基础管理-档案管理-多工程档案总览
 * "docId": "string"
 */
export function getDocsOverview(data, params) {
  return $http.postParams(api.getDocsOverview, data, params)
}
export function getDocsByProjectId(data) {
  return $http.get(api.getDocsByProjectId, data)
}
/**
 * 根据字典id获取矩阵展示设置列表文件
 * @param data
 * @param params
 * @returns {*}
 */
export function getJZFileListByPathDictCode(params) {
  return $http.get(api.getJZFileListByPathDictCode, params)
}

/**
 * 获取档案数据分类列表
 * @param params
 * @param {string} params.wrpcd
*/
export function getDocClassifyByWrpcd (params) {
  return $http.get(api.getDocClassify, params)
}

export function saveFileBindDict(data, params) {
  return $http.postParams(api.saveFileBindDict, data, params)
}
