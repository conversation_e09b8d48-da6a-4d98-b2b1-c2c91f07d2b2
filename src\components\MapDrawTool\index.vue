<template>
  <div class="map-draw-tool">
    <div class="tool-tips">
      <div class="tool-tips-clear">
        点击
        <img class="clear-icon" src="@/assets/map/delete.png" alt="" />
        清除数据
      </div>
      <div v-if="type !== 'Point'">双击结束画图</div>
    </div>
    <Map map-id="map-draw-id" class="map-box" ref="Map" />
  </div>
</template>

<script>
import Map from "@/views/Map";

export default {
  name: "MapDrawTool",
  components: { Map },
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: "Point", // 'Point' | 'LineString' | 'LinearRing' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon' | 'GeometryCollection' | 'Circle'
    },
  },
  data() {
    return {};
  },
  mounted() {
    this.startDraw();
    setTimeout(() => {
      this.$refs.Map.zoomTo(13, [101.48753186063489, 25.11740941202258]);
    }, 800);
  },
  beforeDestroy() {
    this.$refs.Map.removeInteraction();
  },
  methods: {
    startDraw() {
      console.log("startDraw", this.value);
      this.$refs.Map.startDraw(
        "mapDrawLayer",
        {
          strokeColor: "#ED7961",
          fillColor: "rgba(236,114,89,0.3)",
        },
        this.type,
        this.value,
        (data) => {
          this.$emit("input", data);
          this.$emit("draw-end", data);
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.map-draw-tool {
  position: relative;
  height: 400px;

  .tool-tips {
    position: absolute;
    top: 12px;
    left: 12px;
    display: flex;
    align-items: center;
    color: #fff;
    z-index: 99999;

    .tool-tips-clear {
      display: flex;
      align-items: center;
      margin-right: 12px;

      .clear-icon {
        height: 16px;
        width: 16px;
        margin: 0 3px;
      }
    }
  }

  .map-box {
    height: 100%;
    width: 100%;
  }
}
</style>
