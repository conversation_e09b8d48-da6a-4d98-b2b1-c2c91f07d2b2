/* 历史数据分析 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getChooseStation: apiUrl.defaultUrl + '/coc/getChooseStation',
  setStation: apiUrl.defaultUrl + '/coc/setStation',
  getWaterAndRain: apiUrl.defaultUrl + '/hisStatistics/getWaterAndRain',
  getDynamo: apiUrl.defaultUrl + '/hisStatistics/getDynamo',
  getDbSafeStcd: apiUrl.defaultUrl + '/coc/getDbSafeStcd',
  getDbS: apiUrl.defaultUrl + '/hisStatistics/getDbS'
}

/**
 * 历史数据分析-获得站点数组
 *  data：{
 * @param appKey:水库类型
 * @param type:rz 重要站点水位统计站点  rain 降雨量累计柱状图站点
 * @param stnm:搜索站点名称
 * @param pageNum:页面（不传则返回全部）
 * @param pageSize:大小（不传则返回全部）
 * }
 */
export function getChooseStationApi (data) {
  return $http.get(api.getChooseStation, data)
}
/**
 * 历史数据分析-水位、雨量设置指定站点
 * data：{
 * @param appKey:水库类型
 * @param type:rz 重要站点水位统计站点  rain 降雨量累计柱状图站点
 * @param senIds:站点id数组
 * }
 */
export function setStationApi (data) {
  return $http.post(api.setStation, data)
}
/**
 * 历史数据分析-水雨情获取折线图数据
 * data：{
 * @param appKey:水库类型
 * @param stcd:站点数组id
 * @param beginTime:开始时间
 * @param endTime:结束时间
 * }
 */
export function getWaterAndRainApi (data) {
  return $http.post(api.getWaterAndRain, data)
}
/**
 * 历史数据分析-发电机组获取折线图数据
 * data：{
 * @param appKey:水库类型
 * @param type:发电机组类型
 * @param beginTime:开始时间
 * @param endTime:结束时间
 * }
 */
export function getDynamoApi (data) {
  return $http.post(api.getDynamo, data)
}
/**
 * 获取测点数据
 */
export function getDbSafeStcd () {
  return $http.get(api.getDbSafeStcd)
}
/**
 * 获取统计数据
 */
export function getDbS (data) {
  return $http.post(api.getDbS, data)
}
