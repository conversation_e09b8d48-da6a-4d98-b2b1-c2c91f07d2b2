/* 运行管护-工程巡查 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  deleteGcxc: apiUrl.defaultUrl + "/operaEngineeringInspection/deleteGcxc",
  getGcxcDetail:
    apiUrl.defaultUrl + "/operaEngineeringInspection/getGcxcDetail",
  getGcxcList:
    apiUrl.defaultUrl + "/operaEngineeringInspection/getGcxcList",
  saveGcxc: apiUrl.defaultUrl + "/operaEngineeringInspection/saveGcxc",
  exportGcxc: apiUrl.defaultUrl + "/operaEngineeringInspection/getGcxcListExport",
  getCharts: apiUrl.defaultUrl + "/operaEngineeringInspection/charts",
  overviewList: apiUrl.defaultUrl + "/operaEngineeringInspection/overviewList",
};

/**
 * 工程巡查-删除
 */
export function deleteGcxc(data) {
  return $http.post(api.deleteGcxc, data);
}

/**
 * 工程巡查-根据id查询详细信息
 */
export function getGcxcDetail(data) {
  return $http.post(api.getGcxcDetail, data);
}

/**
 * 工程巡查-工程巡查列表
 * id
 */
export function getGcxcList(data) {
  return $http.post(
    `${api.getGcxcList}?pageNum=${data.pageNum}&pageSize=${data.pageSize}`,
    data
  );
}

/**
 * 工程巡查-工程巡查修改
 */
export function saveGcxc(data) {
  return $http.post(api.saveGcxc, data);
}
/**
 * 工程巡查-工程巡查导出
 */
export function exportGcxc(data, contentType, responseType) {
  return $http.postDownLoad(api.exportGcxc, data, contentType, responseType, false, true)
}
/**
 * 工程巡查-工程巡查图表
 */
export function getCharts(data) {
  return $http.post(api.getCharts, data);
}
/**
 * 运行管理-工程巡查省市总览列表
 */
export function overviewList(data) {
  return $http.post(api.overviewList, data);
}
