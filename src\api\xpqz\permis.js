import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  tree: apiUrl.defaultUrl + '/xpqz/permis/tree',
  list: apiUrl.defaultUrl + '/xpqz/permis/list',
  add: apiUrl.defaultUrl + '/xpqz/permis/add',
  update: apiUrl.defaultUrl + '/xpqz/permis/update',
  delete: apiUrl.defaultUrl + '/xpqz/permis/deleteMenu'
}

/**
 * 权限树状结构接口
 * @param data
 * username string
 * password string
 */
export function getPremisTree () {
  return $http.post(api.tree, { enabled: 1 })
}

/**
 * 权限列表接口
 */
export function getPermisList () {
  return $http.post(api.list, undefined)
}

/**
 * 新增权限接口
 */
export function addPermis (data) {
  return $http.post(api.add, data)
}

/**
 * 修改权限接口
 */
export function updatePermis (data) {
  return $http.post(api.update, data)
}

/**
 * 删除权限接口
 */
export function deletePermis (data) {
  return $http.post(api.delete, data)
}
