<template>
  <el-tree class="filter-tree" ref="tree" node-key="id" :data="areaData" :props="{ children: 'children', label: 'projectName' }"
    :default-expanded-keys="defaultKey" highlight-current @node-click="treeNodeClick">
    <template slot-scope="{ node, data }">
      <div class="custom-node ellipsis-text">
        <span class="custom-icon">
          <i v-if="!data.children || !data.children.length" class=""></i>
          <i v-else-if="node.expanded" class="el-icon-minus"></i>
          <i v-else class="el-icon-plus"></i>
        </span>

        <el-tooltip class="item" effect="dark" :content="node.label" placement="top-start">
          <span class="custom-label">{{ node.label }}</span>
        </el-tooltip>
      </div>
    </template>
  </el-tree>
</template>

<script>
// 用于“水雨情”及“综合评分”
export default {
  name: 'MeasuredPointTree',
  props: {
    areaData: {
      type: Array,
      default: () => []
    },
    defaultKey: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 点击节点的回调函数
    treeNodeClick (data) {
      this.$emit('treeNodeClick', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-tree {
  height: 100%;
  overflow: auto;

  .custom-node {
    font-size: 18px;
    font-weight: 500;
    color: #222F40;
    line-height: 42px;
    .custom-icon i {
      margin-right: 28px;
    }
    .custom-icon i[class*="el-icon-"] {
      display: inline-block;
      width: 16px;
      height: 16px;
      color: #1064DA;
      border: 1px solid #1064DA;
      margin-right: 12px;
      text-align: center;
      line-height: 16px;
      font-size: 12px;
    }
  }
}
::v-deep(.el-tree-node__content) {
  height: auto !important;
}
::v-deep(.el-tree-node__expand-icon) {
  opacity: 0;
  position: absolute;
  width: 24px;
  height: 24px;
  box-sizing: border-box;
  padding: 0;
}
::v-deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: transparent;
}
.el-tree-node.is-current > .el-tree-node__content .custom-node {
  color: #0A4EAD;
}
</style>
