<template>
  <div class="project-search-container">
    <el-form :class="['form', columns.length && showMore ? 'open' + Math.ceil(columns.length / 4) : '', 'stdQueryProject']" :model="form" :inline="true" ref="form">
      <el-form-item class="form-item" label="工程类型：" prop="projectType">
        <el-select
          class="sys-type-select"
          @change="handleTypeChange"
          v-model="form.projectType"
          placeholder="请选择"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item class="form-item" label="行政区划：" prop="areaCode">
        <el-cascader
          v-model="form.areaCode"
          @change="handleAreaCodeChange"
          placeholder="请选择行政区划"
          :options="areaTree"
          collapse-tags
          popper-class="area-cascader-popper"
          :props="{ checkStrictly: true, expandTrigger: 'hover' }"
          clearable>
          <template slot-scope="{ data }">
            <span>{{ data.label }}</span>
          </template>
        </el-cascader>
      </el-form-item>
      <el-form-item class="form-item" label="工程名称：" prop="projectName">
        <!-- <el-select
          v-model="form.projectName"
          popper-class="std-popper"
          filterable
          clearable
          placeholder="请选择项目"
          @change="selectProject"
        >
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.projectName"
            :value="item.id"
          >
          </el-option>
        </el-select> -->
        <el-input v-model="form.projectName" placeholder="请输入工程名称关键字"></el-input>
      </el-form-item>
      <el-form-item class="form-item-btns">
        <el-button type="primary" icon="el-icon-search" @click="() => search()">查询</el-button>
        <el-button class="colorCyan" icon="el-icon-refresh" @click="resetForm">重置</el-button>
        <el-button class="colorYellow" v-if="columns.length > 1" @click="showMore = !showMore"><i :class="['el-icon-arrow-down', 'arrow', showMore ? 'arrow2' : '']"></i>更多</el-button>
        <slot v-for="val of btnColumns" :name="'btn-' + val.value"></slot>
      </el-form-item>
      <div class="full-w" v-if="columns.length > 0">
        <el-form-item v-for="item of columns" :key="item.value + item.label" :prop="item.value" :label="item.label + '：'">
          <slot :name="'form-' + item.value">
            <component
              :is="item.type"
              v-model="form[item.value]"
              v-bind="item.attrs"
              :type="item.dataType"
              :disabled="item.disabled"
              :placeholder="item.placeholder"
              :default-time="item.defaultTime"
              :value-format="item.valueFormat"
              :range-separator="item.rangeSeparator || '-'"
              :start-placeholder="item.startPlaceholder" :end-placeholder="item.endPlaceholder"
              :style="item.attrs && item.attrs.style ? item.attrs.style : 'width: 100%'"
              @click.native="
                handleEvent(item.event, form[item.value], 'click', item.value)
              "
              @change="handleEvent(item.event, form[item.value], 'change', item.value)"
              @input="handleEvent(item.event, form[item.value], 'input', item.value)"
              @blur="handleEvent(item.event, form[item.value], 'blur', item.value)"
            >
              <template v-if="item.type === 'el-select'">
                <el-option
                  v-for="(childItem, childIndex) in item.list"
                  :key="childIndex"
                  :label="childItem.label"
                  :value="childItem.value"
                >
                </el-option>
              </template>
              <template v-if="item.type === 'el-radio-group'">
                <el-radio
                  v-for="(childItem, childIndex) in item.list"
                  :key="childIndex"
                  :label="childItem.value"
                >
                  {{ childItem.label }}
                </el-radio>
              </template>
            </component>
          </slot>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
/* 根据“行政区划”及“项目名称”搜索对应的项目 */
import { mapState, mapMutations, mapActions } from 'vuex'
import buildingTypeMap from '@/utils/buildingTypeMap'
import { cascaderOptions } from '@/map/cascaderOptions'
import { areaTree } from '@/api/OperationMonitoring/waterAndRain'
import util from '@/utils/utils'
import { getStorage } from '@/utils/storage'
// import DescrForm from '@/components/elCommon/descrForm/descrForm'
export default {
  name: 'ProjectSearch',
  // components: { DescrForm },
  props: {
    // 表单参数
    columns: {
      type: Array,
      default: () => []
    },
    // 按钮
    btnColumns: {
      type: Array,
      default: () => []
    },
    defaultParams: {
      type: Object,
      default: () => {}
    }
  },
  data () {
    return {
      form: {
        areaCode: '',
        projectName: '',
        projectType: ''
      },
      options: [
        {
          value: '1',
          label: '水库'
        },
        {
          value: '2',
          label: '水闸'
        },
        {
          value: '3',
          label: '堤防'
        }
      ],
      cascaderOptions,
      isShowList: false,
      buildingTypeMap,
      projectName: '',
      hasQueryOp: false,
      areaTree: [],
      showMore: false,
      columnsKey: []
    }
  },
  computed: {
    ...mapState({
      projectInfo: state => getStorage('std-projectInfo'),
      projectList: state => state.project.projectList || getStorage('std-projectList')
    }),
    projectId () {
      return this.projectInfo.id
    }
  },
  created () {
    this.getAreaTree()
  },
  mounted () {
    if (this.rojectList?.length) {
      this.dataHandlng()
    } else {
      this.search()
    }
    // window.addEventListener("click", this.listClick);
    // window.addEventListener("keydown", this.enterSubmit);
  },
  beforeDestroy () {
    // window.removeEventListener("click", this.listClick);
    // window.removeEventListener("keydown", this.enterSubmit);
  },
  watch: {
    // 'form.projectType': function () {
    //   // this.getAreaTree()
    // },
    columns: {
      handler (val) {
        if (Array.isArray(val)) {
          for (const v of val) {
            this.$set(this.form, v.value, v.data)
            this.columnsKey.push(v.value)
          }
        }
      },
      deep: true,
      immediate: true
    },
    defaultParams: {
      handler (val) {
        if (val && JSON.stringify(val) !== '{}') {
          for (const key in val) {
            this.form[key] = val[key]
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    ...mapMutations('project', {
      setProjectInfo: 'SET_PROJECT_INFO',
      setProjectType: 'SET_PROJECT_TYPE',
      setAreaCode: 'SET_AREACODE',
      setProject: 'SET_PROJECT'
    }),
    ...mapActions('project', { getProjectList: 'QUERYPROJECTLIST' }),
    // 表单事件派发
    handleEvent (event, item, data, type) {
      this.$emit('handleEvent', { event, item, data, type })
    },
    getAreaTree () {
      areaTree({ }).then(res => {
        if (res.status === 200) {
          if (Array.isArray(res.data)) {
            this.areaTree = res.data
            this.loopFormatTreeData(this.areaTree)
          }
        }
      }).catch(e => {
        console.log(e)
      })
    },
    /**
     * 树状列表的数据进行格式化
    */
    loopFormatTreeData (data, areaCodeArray) {
      if (Array.isArray(data) && data.length > 0) {
        for (let i = 0, num = data.length; i < num; i++) {
          data[i].label = data[i].areaName
          data[i].value = data[i].areaCode
          data[i].children = Array.isArray(data[i].childList) && data[i].childList.length > 0 ? data[i].childList : null
          if (Array.isArray(areaCodeArray)) {
            data[i].areaCodeArray = [...areaCodeArray, data[i].areaCode]
          } else {
            data[i].areaCodeArray = [data[i].areaCode]
          }
          this.loopFormatTreeData(data[i].childList, data[i].areaCodeArray)
        }
      }
    },
    /**
     * 按下键盘enter键
    */
    enterSubmit (e) {
      if (e.keyCode === 13) {
        this.search()
      }
    },
    handleInput () {
      util.throttle(this.search, 500, this)
    },
    handleTypeChange (val) {
      this.form.projectName = ''
      // this.setProject(null)
      // this.setProjectType(val)
      // this.search()
    },
    handleAreaCodeChange (val) {
      this.form.projectName = ''
      // this.setProject(null)
      // this.setAreaCode(val)
      // this.search()
    },
    search () {
      // this.getProjectList().then(this.dataHandlng)
      const options = {
        projectType: this.form.projectType,
        areaCode: Array.isArray(this.form.areaCode) ? this.form.areaCode.length > 0 ? this.form.areaCode[this.form.areaCode.length - 1] : '' : '',
        projectName: this.form.projectName
      }
      if (Array.isArray(this.columnsKey) && this.columnsKey.length > 0) {
        for (const v of this.columnsKey) {
          options[v] = this.form[v]
        }
      }
      this.$emit('load', options)
    },
    dataHandlng () {
      let lxArr = []
      if (!this.hasQueryOp) {
        for (const val of this.projectList) {
          lxArr.push(val.projectType)
        }
        lxArr = [...new Set(lxArr)].sort((a, b) => {
          return Number(a) - Number(b)
        })
        const op = []
        const opData = [
          {
            label: '水库',
            value: '1'
          },
          {
            label: '水闸',
            value: '2'
          },
          {
            label: '堤防',
            value: '3'
          }
        ]
        for (const val of opData) {
          if (lxArr.indexOf(Number(val.value)) !== -1) {
            op.push(val)
          }
        }
        this.options = op
        this.form.projectType = op[0]?.value
        this.hasQueryOp = true
      }
      if (this.projectList.length) {
        this.getProjectMsg(this.projectList[0])
      } else {
        this.form.projectName = ''
      }
    },
    resetForm () {
      this.$refs.form.resetFields()
      this.search(true)
      this.isShowList = false
    },
    selectProject (val) {
      const obj = this.projectList.find(i => i.id === val)
      if (obj) {
        this.getProjectMsg(obj)
      }
      // this.setProject(obj || '')
    },
    getProjectMsg (val) {
      // this.setProjectInfo(val)
      // this.setProjectType(val.projectType + '')
      this.isShowList = false
      this.form.projectType = val.projectType + ''
    },
    listClick (e) {
      if (
        e &&
        e.target &&
        e.target.className &&
        typeof e.target.className === 'string' &&
        e.target.className.indexOf('project-list') === -1
      ) {
        this.isShowList = false
      }
    },
    typeConvert (val, bool) {
      const TYPE = parseInt(val)
      let src = ''
      switch (TYPE) {
        case 1:
          src = require('@/assets/score/' +
            (bool ? 'icon-sk' : 'icon-sk-white') +
            '.png')
          break
        case 2:
          src = require('@/assets/score/' +
            (bool ? 'icon-sz' : 'icon-sz-white') +
            '.png')
          break
        case 3:
          src = require('@/assets/score/' +
            (bool ? 'icon-df' : 'icon-df-white') +
            '.png')
          break
      }
      return src
    }
  }
}
</script>

<style lang="scss" scoped>
.project-search-container {
  position: relative;
  padding-left: 10px;
  color: #ffffff;
}

.form {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  height: 2.73vw;
  transition: height 0.3s;
  overflow: hidden;
  &.open1 {
    height: 5.46vw;
  }
  &.open2 {
    height: 8.19vw;
  }
  ::v-deep(.el-form-item__label) {
    line-height: 48px;
  }
  ::v-deep(.el-input__inner) {
    width: 300px;
  }
  ::v-deep(.el-button) {
    padding: 0;
    width: 100px;
    height: 48px;
    line-height: 48px;
  }
}

.form-item {
  margin-right: 20px;
  position: relative;
}

.form-item-btns {
  flex: 1;
  ::v-deep(.el-form-item__content) {
    width: 100%;
    display: flex;
  }

  .project-msg {
    display: flex;
    align-items: center;

    img {
      width: 24px;
      height: 24px;
      margin-right: 10px;
    }

    .project-name {
      font-size: 30px;
      font-weight: 600;
      line-height: 48px;
    }

    .project-type {
      margin-left: 20px;
      line-height: 36px;
      display: block;
      border-radius: 4px;
      padding: 0 20px;
      font-size: 16px;
    }
  }
}

.project-list {
  z-index: 999;
  box-sizing: border-box;
  position: absolute;
  top: 70px;
  width: 500px;
  height: 316px;
  right: 0;
  left: 0;
  display: none;
  flex-direction: column;
  border-radius: 4px;
  z-index: 9;
  background-color: #ffffff;
  padding: 8px;
  background-image: linear-gradient(0deg, #d8e8ff, #4191ff);

  .arrow {
    position: absolute;
    top: -18px;
    left: 12px;
    width: 0;
    height: 0;
    border-left: transparent 16px solid;
    border-right: transparent 16px solid;
    border-bottom: #4191ff 20px solid;
  }

  &.project-list-open {
    display: flex;
    box-shadow: 0 0 10px rgb(204, 204, 204, 0.5);
  }

  .empty {
    background-color: rgb(255, 255, 255, 0.8);
    height: 100%;
    padding: 0;
  }

  ul {
    height: 100%;
    overflow-y: auto;
    padding: 0 20px;
    background-color: rgb(255, 255, 255, 0.8);

    li {
      padding: 20px 0 20px 38px;
      text-align: left;
      cursor: pointer;
      font-size: 18px;
      color: #333333;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #d4deeb;
      background: url("@/assets/header/icon-serach.png") no-repeat;
      background-size: 18px 18px;
      background-position: 10px center;

      img {
        width: 24px;
        height: 24px;
        margin-right: 20px;
      }

      &:hover {
        color: #409eff;
      }
    }
  }
}

.ml20 {
  margin-left: 20px;
}

.mrAuto {
  margin-right: auto;
}

.colorCyan {
  color: white;
  border-color: #1A94DA;
  background-color: #1A94DA;
  &:hover {
    border-color: rgba(26, 148, 218, 0.8);
    background-color: rgba(26, 148, 218, 0.8);
  }
}

.colorYellow {
  color: #ffffff;
  background-color: rgb(230, 162, 60);
  border-color: rgb(230, 162, 60);

  .arrow {
    transform: rotate(0);
    transition: transform 0.3s;

    &.arrow2 {
      transform: rotate(-180deg);
    }
  }
}
</style>
