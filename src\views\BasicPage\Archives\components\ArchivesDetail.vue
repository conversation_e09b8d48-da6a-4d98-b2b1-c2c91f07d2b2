<template>
  <div class="archives-detail flex-column">
    <!-- 查询 -->
    <formItemComp
      ref="searchForm"
      :form-config="searchFormJson"
      :data="searchFormData"
      :list-type-info="listTypeInfo"
    >
      <template slot="form-btn">
        <div class="search-btn">
          <el-button type="primary" icon="el-icon-search" @click="search"
            >查询</el-button
          >
          <el-button @click="resetForm">重置</el-button>
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="showDialog('add')"
            >新增</el-button
          >
          <el-button
            :disabled="!selectionData.length"
            @click="handleMultiDel"
            icon="el-icon-delete"
            >删除</el-button
          >
          <el-button plain icon="el-icon-download" @click="exports"
            >导出</el-button
          >
        </div>
      </template>
    </formItemComp>
    <free-table
      class="flex-column flex-1"
      ref="FileTable"
      show-pagination
      height="100%"
      row-key="id"
      :data="tableData"
      :column="tableColumns"
      :header-cell-style="{ backgroundColor: '#DFECFF', color: '#0B0306' }"
      background
      paginationPosition="bottom"
      :total="pagination.total"
      :current-page.sync="pagination.page"
      @page-change="getList"
      @selection-change="handleSelectionChange"
    >
      <template #fileName="{ row }">
        <span class="text">{{ row.fileName }}</span>
      </template>
      <!-- <template #position="{row}">
        <span class="text max-two-line">{{ row.position }}</span>
      </template>
      <template #docNumber="{row}">
        <span class="text max-two-line">{{ row.docNumber }}</span>
      </template>
      <template #remark="{row}">
        <span class="text max-two-line">{{ row.remark }}</span>
      </template> -->
      <template #actions="{ row }">
        <div class="action-container">
          <el-button
            type="text"
            icon="el-icon-setting"
            @click="showDetails('jzzs', row)"
          >
            矩阵展示
          </el-button>
          <el-button type="text" icon="el-icon-view" @click="handlePreview(row)"
            >预览</el-button
          >
          <el-button
            type="text"
            icon="el-icon-edit"
            @click="showDialog('edit', row)"
            >编辑</el-button
          >
          <el-popconfirm
            title="是否删除该文档？"
            @confirm="handleDeleteFile(row)"
          >
            <el-button type="text" slot="reference" icon="el-icon-delete">
              删除
            </el-button>
          </el-popconfirm>
        </div>
      </template>
    </free-table>

    <!--    矩阵展示弹窗-->
    <task-dialog
      popup-width="90%"
      @updateVisible="details.show = false"
      :visible="details.show"
      :dialog-title="details.title"
      :show-btn="true"
      :custome-btn="true"
      :btns="details.customeBtn"
    >
      <formItemComp
        ref="searchForm"
        :form-config="detailsSearchFormJson"
        :data="detailsSearchFormData"
        :list-type-info="detailsListTypeInfo"
      >
      </formItemComp>
      <free-table
        class="flex-column flex-1"
        style="width: 100%"
        ref="detailsTable"
        :draggable="true"
        height="100%"
        row-key="index"
        :data="detailFileData"
        :column="detailsTableColumns"
        :header-cell-style="{ backgroundColor: '#DFECFF', color: '#0B0306' }"
        background
      >
        <template #actions="{ row }">
          <el-popconfirm title="是否删除该文档？" @confirm="removeFile(row)">
            <el-button type="text" slot="reference" icon="el-icon-delete">
              删除
            </el-button>
          </el-popconfirm>
        </template>
      </free-table>
    </task-dialog>

    <task-dialog
      popup-width="30%"
      @updateVisible="dialogInfo.show = false"
      @submitPopupData="save"
      :visible="dialogInfo.show"
      :dialog-title="dialogInfo.title"
    >
      <div class="form">
        <task-form
          ref="taskForm"
          :data="formData"
          :options="formObj"
          :formType="dialogInfo.type"
        >
          <template #form-fileName>
            <el-upload
              ref="fileUpload"
              action=""
              :show-file-list="false"
              accept="*"
              :before-upload="beforeUpload"
            >
              <div style="display: flex; align-items: center">
                <el-input v-model="formData.fileName" readonly style="flex: 1">
                </el-input>
                <el-button type="primary">浏览</el-button>
              </div>
            </el-upload>
          </template>
        </task-form>
      </div>
    </task-dialog>
    <FilePreviewDialog ref="filePreviewDialog" />
  </div>
</template>

<script>
import FormItemComp from "@/components/elCommon/searchForm/searchForm.vue";
import FreeTable from "@/components/elCommon/freeTable";
import TaskDialog from "@/components/elCommon/taskDialog/taskDialog.vue";
import TaskForm from "@/components/elCommon/taskForm/taskForm.vue";
import FilePreviewDialog from "@/components/elCommon/FilePreviewDialog.vue";
import { downLoad } from "@/api/attachment";
import {
  list as fileList,
  deleteById as delFileById,
  fileUpload,
  fileUpdate,
  fileListExport,
  getJZFileListByPathDictCode,
  saveFileBindDict,
} from "@/api/basic/docFile";
import { downloadFileByBlob } from "@/utils/utils";
import { getDictionaryTreeList } from "@/api/system/dict";

export default {
  name: "ArchivesDetail",
  components: {
    FormItemComp,
    FreeTable,
    TaskDialog,
    TaskForm,
    FilePreviewDialog,
  },
  props: {
    docId: {
      required: true,
      type: [String, Number],
    },
  },
  data() {
    return {
      dictTree: [],
      detailsSearchFormData: {},
      detailsSearchFormJson: {
        id: "form",
        className: "el-form-component",
        fieldList: [
          {
            label: "文件路径:",
            value: "filePath",
            type: "el-select",
            list: "filePathList",
            filterable: true,
          },
        ],
      },
      searchFormData: {}, // 查询表单
      searchFormJson: {
        id: "form",
        className: "el-form-component",
        fieldList: [
          {
            label: "存放位置:",
            value: "position",
            type: "el-input",
          },
          {
            label: "文号:",
            value: "docNumber",
            type: "el-input",
          },
          {
            label: "文件名:",
            value: "fileName",
            type: "el-input",
          },
          // slot插槽
          {
            label: "",
            value: "btn",
            type: "slot",
            colWidth: 18,
          },
        ],
      },
      listTypeInfo: {},
      detailsListTypeInfo: {
        filePathList: [],
      },
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
      },
      tableColumns: [
        { type: "selection", width: 55 },
        { prop: "fileName", slotScope: true, label: "文件名称", width: 450 },
        { prop: "createTime", label: "上传时间", width: 150 },
        { prop: "position", label: "存放位置", width: 80 },
        { prop: "docNumber", label: "文号", width: 60 },
        { prop: "remark", label: "备注", width: 100 },
        {
          label: "操作",
          prop: "actions",
          // width: 240,
          // fixed: "right",
          slotScope: true,
        },
      ],
      detailsTableColumns: [
        { prop: "fileName", label: "文件名称" },
        { prop: "sort", label: "排序" },
        {
          label: "操作",
          prop: "actions",
          width: 300,
          fixed: "right",
          slotScope: true,
        },
      ],
      tableData: [],
      selectionData: [],

      detailFileData: [],

      dialogInfo: {
        show: false,
        title: "",
        type: "",
      },
      details: {
        show: false,
        title: "",
        type: "",
        customeBtn: [
          {
            icon: "el-icon-check",
            type: "primary",
            text: "保存",
            clickEvent: () => {
              this.saveJzSetting();
            },
          },
          {
            icon: "el-icon-close",
            type: "default",
            text: "关闭",
            clickEvent: () => {
              this.details.show = false;
            },
          },
        ],
      },
      detailsTmp: {},
      formObj: {
        labelWidth: "100px",
        span: 24,
        fieldList: [
          {
            label: "文件",
            value: "fileName",
            type: "upload",
            attrs: {
              limit: 1,
              module: "basicPage",
              accept: "*",
              showTip: false,
              limitSize: 0,
            },
          },
          { label: "存放位置", value: "position", type: "el-input" },
          { label: "文号", value: "docNumber", type: "el-input" },
          {
            label: "备注",
            value: "remark",
            type: "el-input",
            attrs: { type: "textarea" },
          },
        ],
        rules: {
          fileName: [
            {
              required: true,
              message: "请选择文件",
            },
          ],
        },
        listTypeInfo: {},
      },
      formData: {
        fileName: "",
        position: "",
        docNumber: "",
        remark: "",
      },
      file: null,
    };
  },
  watch: {
    details: {
      handler(val) {
        if (!val.show) {
          // 弹窗关闭，情调数据
          this.detailFileData = [];
          this.detailsSearchFormData = {};
        }
      },
      deep: true,
      immediate: true,
    },

    detailsSearchFormData: {
      handler(val) {
        if (val && this.details.show) {
          // todo filePath 其实是字典的那个code
          this.getJZFileList(val.filePath)
            .then((res) => {
              if (res.status === 200) {
                const resLength = res.data.length;
                this.detailFileData = res.data.map((item, idx) => ({
                  ...item,
                  index: idx,
                  sort: idx + 1,
                }));
                const hasAtachIds = res.data.map((item) => item.attachId);
                // 判断，如果返回的文件列表中已有文件，则不重复添加
                if (!hasAtachIds.includes(this.detailsTmp.fileId)) {
                  this.detailFileData.push({
                    attachId: this.detailsTmp.fileId,
                    fileName: this.detailsTmp.fileName,
                    dictCode: val.filePath,
                    index: resLength,
                    sort: resLength + 1,
                  });
                }
              } else {
                this.$message({
                  type: "error",
                  message: res.message,
                });
              }
            })
            .catch((err) => {
              this.$message({
                type: "error",
                message: "请求异常",
              });
            });
        }
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.getDicts();
  },
  methods: {
    async loadDictTree() {
      if (this.dictTree.length === 0) {
        const res = await getDictionaryTreeList({ parentId: -1 });
        if (res.status === 200) {
          this.dictTree = res.data;
        }
      }
    },
    async getDicts() {
      await this.loadDictTree();
      const tasks = [];
      tasks.push(
        getDictionaryTreeList({
          parentId: this.dictTree.find(
            (item) => item.dictCode === "FILE_DISPLAY_CATEGORY"
          ).id,
        }).then((res) => {
          if (res.status === 200) {
            this.detailsListTypeInfo.filePathList = res.data.map((item) => ({
              value: item.dictValue,
              label: item.dictName,
            }));
          }
        })
      );
      if (tasks.length > 0) {
        await Promise.all(tasks);
      }
    },
    resetData() {
      this.$set(this.pagination, "page", 1);
      this.$set(this.pagination, "total", 0);
      this.tableData = [];
    },
    search() {
      this.$set(this.pagination, "page", 1);
      this.getList();
    },
    resetForm() {
      this.$set(this.pagination, "page", 1);
      this.$refs.searchForm.resetForm();
      this.searchFormData = {};
      this.getList();
    },
    getJZFileList(id) {
      return getJZFileListByPathDictCode({ dictCode: id });
    },
    getList() {
      fileList(
        {
          docId: this.docId,
          ...this.searchFormData,
        },
        {
          pageNum: this.pagination.page,
          pageSize: this.pagination.limit,
        }
      ).then((res) => {
        this.tableData = [];
        if (res.status === 200) {
          this.tableData = res.data.list;
          this.pagination.total = res.data.total;
        }
      });
    },
    /**
     * 文档多选回调函数
     */
    handleSelectionChange(list) {
      this.selectionData = list.map((val) => val.id);
    },
    handlePreview(val) {
      downLoad({ id: val.fileId }, "blob")
        .then((res) => {
          if (res) {
            const typeArr = val.fileName.split(".");
            this.$refs.filePreviewDialog.preview({
              ...val,
              id: val.fileId,
              zwdlx: typeArr[typeArr.length - 1],
              mc: val.fileName,
              fileData: window.URL.createObjectURL(new Blob([res])),
            });
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    /**
     * 矩阵很展示
     */
    showDetails(type, val) {
      this.details.show = true;
      this.details.type = type;
      this.details.title = "矩阵展示设置";
      this.detailsTmp = val;
    },
    /**
     * 删除多个文档
     */
    handleMultiDel() {
      this.$confirm("是否删除多个文档?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.handleDeleteFile(this.selectionData).then(() => {
            this.selectionData = [];
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    /**
     * 矩阵展示删除文件，只删除列表，点保存之后才更新真正的删除
     * @param data
     */
    removeFile(data) {
      this.detailFileData = this.detailFileData.filter(
        (item) => item.index !== data.index
      );
      this.updateSortOrder();
    },
    // 更新 sort 值
    updateSortOrder() {
      this.detailFileData.forEach((item, index) => {
        item.sort = index + 1;
      });
    },
    handleDeleteFile(data) {
      let idArr = [];
      if (Array.isArray(data)) {
        idArr = [...data];
      } else {
        idArr.push(data.id);
      }
      return delFileById(idArr)
        .then((res) => {
          if (res.status === 200) {
            this.$message.success("删除成功");
            this.$emit("queryData");
          }
        })
        .catch((e) => {
          console.log(e);
        });
    },
    // 文件导入提交前
    beforeUpload(file) {
      this.file = file;
      this.formData.fileName = file.name;
      return false;
    },
    /**
     * 附件列表变更的时候触发的回调函数，附件类型及大小的检测
     */
    handleFileChange(file, fileList) {
      const TYPE = [
        "png",
        "jpeg",
        "doc",
        "docx",
        "pdf",
        "xls",
        "xlsx",
        "ppt",
        "pptx",
        "rar",
        "zip",
      ];
      const RAWTYPE = [
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/vnd.ms-excel",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-powerpoint",
        "application/pdf",
        "image/png",
        "image/jpeg",
        "application/rar",
        "application/zip",
        "application/kswps",
        "application/kset",
        "application/ksdps",
        "application/x-zip-compressed",
        "application/x-rar-compressed",
        "application/x-compressed",
      ];
      const BOOL = RAWTYPE.some((val) => file.raw.type.indexOf(val) !== -1);
      console.log(file);
      const isLtdFileSize = file.size / 1024 / 1024 <= 50;

      if (!BOOL) {
        this.$message.error("上传附件只能是" + TYPE.join("、") + "格式!");
      }
      if (!isLtdFileSize) {
        this.$message.error("上传图片大小不能超过50M!");
      }
      if (!BOOL || !isLtdFileSize) {
        const currIdx = fileList.indexOf(file);
        fileList.splice(currIdx, 1);
      } else {
        if (file.status === "ready") {
          this.$refs.fileList.submit();
        }
      }
      for (const f in fileList) {
        if (file.name === fileList[f].name) {
          fileList[f] = {
            ...fileList[f],
            ...fileList[f].response,
          };
        }
      }
    },

    showDialog(type, row = {}) {
      this.dialogInfo.type = type;
      this.dialogInfo.show = true;
      this.file = null;
      this.dialogInfo.title = type === "add" ? "新增" : "编辑";
      this.$nextTick(() => {
        this.$refs.taskForm.$refs.form.resetFields();
        this.formData =
          type === "add"
            ? { fileName: "", position: "", docNumber: "", remark: "" }
            : JSON.parse(JSON.stringify(row));
      });
    },
    /**
     * 更新矩阵展示设置弹窗的文件
     */
    saveFileList() {
      console.log();
    },
    save() {
      const formData = new FormData();
      formData.append("docId", this.docId);
      formData.append("module", "common");
      formData.append("position", this.formData.position);
      formData.append("docNumber", this.formData.docNumber);
      formData.append("remark", this.formData.remark);
      if (this.file) {
        formData.append("file", this.file);
      }
      if (this.formData.id) {
        formData.append("id", this.formData.id);
      }
      if (this.formData.fileId) {
        formData.append("fileId", this.formData.fileId);
      }
      (this.dialogInfo.type === "add"
        ? fileUpload(formData)
        : fileUpdate(formData)
      )
        .then((res) => {
          if (res.status === 200) {
            this.$message.success("上传成功");
            this.dialogInfo.show = false;
            this.$emit("queryData");
          }
        })
        .catch((e) => {
          console.log(e);
          this.$message.error("上传失败，请稍后再试");
        });
    },

    // 导出
    exports() {
      const data = {
        docId: this.docId,
        ...this.searchFormData,
      };

      fileListExport(data, "", "arraybuffer").then((res) => {
        downloadFileByBlob(res.fileName, res.data, "application/zip");
      });
    },
    saveJzSetting() {
      if (this.detailsSearchFormData.filePath) {
        saveFileBindDict(this.detailFileData, {
          dictCode: this.detailsSearchFormData.filePath,
        })
          .then((res) => {
            if (res.status === 200) {
              this.$message.success("保存成功");
              this.details.show = false;
            }
          })
          .catch((err) => {
            console.log(err);
            this.$message.error("保存失败");
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.archives-detail {
  width: 100%;
  height: 100%;
}
:deep(.el-upload) {
  width: 100%;
}
.text {
  word-break: break-all;
  white-space: pre-wrap;
}
.action-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
</style>
