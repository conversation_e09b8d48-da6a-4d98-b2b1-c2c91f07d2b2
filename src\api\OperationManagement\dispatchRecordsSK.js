import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  pagelist: apiUrl.defaultUrl + '/skDispatchRecord/pagelist',
  deleteById: apiUrl.defaultUrl + '/skDispatchRecord/deleteById',
  save: apiUrl.defaultUrl + '/skDispatchRecord/save',
  exportList: apiUrl.defaultUrl + '/skDispatchRecord/pagelistExport',
}

/**
 * 运行管护-水库调度记录
 * @param data
 */
// 查询水库调度记录信息
export function pagelist(data) {
  return $http.post(api.pagelist, data)
}
// 删除水库调度记录
export function deleteById(data) {
  return $http.post(api.deleteById, data)
}
// 保存水库调度记录
export function save(data) {
  return $http.post(api.save, data)
}
// 导出水库调度记录
export function exportList(data, contentType, responseType) {
  return $http.postDownLoad(api.exportList, data, contentType, responseType, false, false)
}