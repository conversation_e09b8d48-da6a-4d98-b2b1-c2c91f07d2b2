<template>
  <div class="task-upload">
    <!-- 上传功能 -->
    <el-upload
      ref="taskUpload"
      class="es-projectmsg-imglist-upload stdClass"
      :class="{ 'upload-unshow': readonly, 'flex-vc': listType === 'table' }"
      v-bind="$attrs"
      action=""
      :limit="limit"
      :multiple="computedMultiple"
      :on-exceed="handleExceed"
      :list-type="listType"
      :auto-upload="false"
      :show-file-list="listType !== 'table'"
      :file-list="fileList"
      :on-progress="
        (event, file, fileList) => onUploadFileProcess(event, file, fileList)
      "
      :on-change="fileChange"
      :http-request="fileUpload"
    >
      <!-- 上传组件 -->
      <!-- 拖拽模式 -->
      <template v-if="$attrs.drag">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      </template>
      <!-- picture-card -->
      <i
        v-else-if="listType === 'picture-card'"
        class="el-icon-plus self-upload-icon"
      ></i>
      <!-- 点击按钮模式 -->
      <el-button v-else type="primary" class="task-upload-button el-icon-plus"
        >添加</el-button
      >

      <!-- 文件列表 -->
      <div
        slot="file"
        slot-scope="{ file }"
        :class="[listType === 'picture-card' ? 'file-pc' : 'file-t']"
      >
        <template v-if="listType === 'picture-card'">
          <img
            :src="file.file_path ? previewUrl + file.file_path : file.url"
            @click="previewFile(file.file_path)"
          />
          <button
            v-show="!readonly"
            type="button"
            @click="removeFile(file)"
            class="file-del"
          ></button>

          <span class="file-mask" v-show="readonly">
            <i
              class="el-icon-zoom-in"
              @click.stop="previewFile(file.file_path)"
            ></i>
          </span>
          <el-progress
            v-if="file.progressFlag"
            :show-text="false"
            :text-inside="true"
            :percentage="file.loadProgress"
          ></el-progress>
        </template>
        <template v-else-if="listType === 'text'">
          <span class="file-info"
            ><i class="el-icon-document"></i>{{ file.mc }}</span
          >
          <span class="file-operate">
            <i class="el-icon-download" @click="downloadFile(file)"></i>
            <i
              class="el-icon-close"
              v-if="!readonly"
              @click="removeFile(file)"
            ></i>
          </span>
        </template>
      </div>

      <slot name="tip" slot="tip">
        <div v-if="showTip && !readonly" class="upload__tip">{{ tip }}</div>
      </slot>
    </el-upload>
    <!-- 表格形式展示文件 -->
    <div v-if="listType === 'table'" class="file-table">
      <el-table border :data="fileList">
        <el-table-column type="index" label="序号" width="55">
        </el-table-column>
        <el-table-column label="文件名" prop="mc"> </el-table-column>
        <el-table-column prop="tm" label="上传日期"> </el-table-column>
        <el-table-column label="操作" width="140">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              class="el-icon-download"
              @click="downloadFile(row)"
              >下载</el-button
            >
            <el-button
              v-if="!readonly"
              type="text"
              class="el-icon-delete"
              @click="removeFile(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible="previewVisible"
      class="preview-dialog std-theme"
      top="0"
      width="80vw"
      title="预览"
      append-to-body
      @close="previewClose"
    >
      <FilePreview ref="previewRef" />
      <div slot="footer">
        <el-button
          class="theme-dialog-primary-btn"
          type="primary"
          @click="previewClose"
          >关 闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import FilePreview from "@/components/FilePreview/index.vue";
import { upload, deleteFile, downLoad, previewUrl } from "@/api/attachment";
export default {
  name: "TaskUpload",
  components: { FilePreview },
  props: {
    module: { type: String, default: "common" },
    moduleId: { type: [Number, String], default: "" },
    // 文件列表显示类型
    listType: { type: String, default: "text" },
    showTip: { type: Boolean, default: true },
    tip: {
      type: String,
      default: "支持扩展名：jpg、png",
    },
    readonly: { type: Boolean, default: false },
    fileList: {
      type: Array,
      default: () => [],
    },
    fileJudge: { type: [Function, Object], default: undefined },
    limit: {
      // 最大允许上传个数
      type: Number
    },
    limitSize: {
      // 限制文件大小，单位 m
      type: Number,
      default: 100,
    },
    // 是否支持多文件上传
    multiple: {
      type: Boolean,
      default: true,
    }
  },
  data() {
    return {
      previewUrl,
      previewVisible: false,
      deleteIds: [],
      newFileIds: [],
    };
  },
  computed: {
    filePreviewList() {
      return this.fileList.map((file) => previewUrl + file.file_path);
    },
    // 智能计算multiple属性：当limit为1时，自动设置为false
    computedMultiple() {
      if (this.limit === 1) {
        return false;
      }
      return this.multiple;
    },
  },
  methods: {
    handleExceed (files, fileList) {
      this.$message.warning(`当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`)
    },
    /**
     * upload组件的onProcess钩子的回调函数
     */
    onUploadFileProcess(e, file) {
      file.loadProgress = e.percent;
      if (e.percent !== 100) {
        file.progressFlag = true;
      }
    },
    /**
     * axios上传时onUploadProgress回调函数，获取文件上传进度
     */
    uploadFileProcess(event, file) {
      // 动态获取文件上传进度
      const percent = (event.loaded / event.total) * 100;
      file.onProgress({ percent });
    },
    /**
     * 默认上传条件判断
     */
    uploadFileJudge(file) {
      const TYPE = ["jpg", "png", "jpeg"];
      const BOOL = TYPE.some((val) => file.raw.type.indexOf(val) !== -1);
      const isLtdFileSize = file.size / 1024 / 1024 <= 10;

      if (!BOOL) {
        this.$message.error("上传附件只能是" + TYPE.join("、") + "格式!");
        return false;
      }
      if (!isLtdFileSize) {
        this.$message.error("上传文件大小不能超过100M!");
        return false;
      }
      return true;
    },
    fileDefaultJudge(file) {
      // 检查文件类型
      if (this.$attrs.accept && this.$attrs.accept !== "*") {
        const type = file.name
          .substring(file.name.lastIndexOf("."))
          .toLowerCase();
        const pType = `${file.raw.type.slice(0, file.raw.type.indexOf("/"))}/*`;
        const typeList = this.$attrs.accept.split(",").map((i) => {
          return i.trim().toLowerCase();
        });
        if (![type, pType, file.raw.type].some((i) => typeList.includes(i))) {
          this.$message.error(`上传附件只能是${typeList.join("、")}格式！`);
          return false;
        }
      }
      // 检查文件大小
      if (this.limitSize && file.size / 1024 / 1024 > this.limitSize) {
        this.$message.error(`上传文件大小不能超过${this.limitSize}M！`);
        return false;
      }

      return true;
    },
    /**
     * 文件上传
     */
    fileChange(file, fileList) {
      // 上传前文件条件限制判断
      const currIdx = fileList.indexOf(file);
      if (
        this.fileJudge ? !this.fileJudge(file) : !this.fileDefaultJudge(file)
      ) {
        fileList.splice(currIdx, 1);
        return;
      }

      if (file.status === "ready") {
        this.$refs.taskUpload.submit();
      }
      if (file.status === "success") {
        fileList[currIdx] = {
          ...fileList[currIdx],
          ...fileList[currIdx].response,
          progressFlag: false,
        };
        this.$emit("update:file-list", fileList);
      }
    },
    fileUpload(file) {
      const formData = new FormData();
      formData.append("file", file.file);
      formData.append("module", this.module);
      if(this.moduleId) {
        formData.append("module_id", this.moduleId);
      }
      upload(formData, (e) => this.uploadFileProcess(e, file))
        .then((res) => {
          if (res.status === 200 && res.data) {
            file.onSuccess(res.data);
            this.newFileIds.push(res.data.id);
          }
        })
        .catch((err) => {
          this.$message.error("上传失败，请稍后再试");
          file.onError(err);
        });
    },

    /**
     * 文件移除
     */
    removeFile(file) {
      this.deleteIds.push(file.id);
      this.$emit(
        "update:file-list",
        this.fileList.filter((i) => i.id !== file.id)
      );
    },

    /**
     * 文件预览
     */
    previewFile(url) {
      this.previewVisible = true;
      this.$nextTick(() => {
        this.$refs.previewRef.initImg(previewUrl + url);
      });
    },
    /**
     * 关闭图片预览窗口
     */
    previewClose() {
      this.$refs.previewRef.close();
      this.previewVisible = false;
    },

    /**
     * 文件下载
     */
    downloadFile(file) {
      downLoad({ id: file.id }, "arraybuffer").then((res) => {
        const link = document.createElement("a");
        const blob = new Blob([res]);
        link.style.display = "none";
        // 设置连接
        link.href = URL.createObjectURL(blob);
        link.download = file.mc;
        document.body.appendChild(link);
        // 模拟点击事件
        link.click();
        document.body.removeChild(link); // 下载完成移除元素
        window.URL.revokeObjectURL(link.href);
      });
    },

    /**
     * 文件删除-取消或保存操作时，删除已移除、新增的文件,组件文件移除没有调用删除接口，需要保存的时候调用该方法删除。
     * type: { 1: 已移除 2: 新增后取消 }
     */
    deleteFiles(type) {
      const ids = (type === 1 ? this.deleteIds : this.newFileIds).join(",");
      if (ids && ids.length) {
        deleteFile({ id: ids })
          .then((res) => {
            if (res.status === 200) {
              // this.$message.success('删除成功')
            } else {
              // this.$message.success(res.message || '删除失败')
            }
          })
          .catch((e) => {
            console.log(e);
            // this.$message.success('删除失败')
          });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.task-upload {
  width: 95%;
  ::v-deep(.upload-unshow .el-upload) {
    display: none;
  }
  ::v-deep(.el-upload-list--picture-card) {
    .el-upload-list__item {
      width: 157px;
      height: 118px;
      position: relative;
      &.is-success::before {
        content: "";
        position: absolute;
        width: 39px;
        height: 39px;
        right: 0;
        bottom: 0;
        background: url("@/assets/basic/file-tick.png") no-repeat;
        background-size: 100% 100%;
        z-index: 1;
      }
    }
  }
  ::v-deep(.el-upload--picture-card) {
    width: 157px;
    height: 118px;
    line-height: 118px;
    .self-upload-icon {
      color: #034daf;
      font-size: 26px;
      font-weight: 700;
    }
  }
  ::v-deep(.el-button[class*="el-icon-"] > span) {
    margin-left: 10px;
  }

  .upload__tip {
    font-size: 18px;
    color: #222f40;
  }
  .task-upload-button {
    margin-right: 15px;
  }

  .file-pc {
    width: 100%;
    height: 100%;
    position: relative;
    img {
      width: 100%;
      height: 100%;
      cursor: pointer;
      object-fit: contain;
    }

    .file-del {
      width: 32px;
      height: 32px;
      background: url("@/assets/basic/del.png") no-repeat;
      background-size: 100% 100%;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      border: none;
      cursor: pointer;
      border-radius: 50%;
      background-color: #ccc;
      display: none;

      &:hover,
      &:active {
        background-image: url("@/assets/basic/delHover.png");
      }
    }

    .file-mask {
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0;
      top: 0;
      left: 0;
      color: #ffffff;
      font-size: 24px;
      text-align: center;
      line-height: 118px;
      &:hover {
        opacity: 1;
      }
      > i {
        cursor: pointer;
      }
    }

    &:hover {
      .file-del {
        display: block;
      }
    }
  }
  .file-t {
    margin-bottom: 10px;
    padding: 0 16px;
    box-sizing: border-box;
    border-radius: 4px;
    background-color: rgba($color: #cccccc, $alpha: 0.3);
    line-height: 38px;
    display: flex;
    justify-content: space-between;
    font-size: 18px;
    .file-info {
      i {
        margin-right: 4px;
      }
    }
    .file-operate {
      opacity: 0;
      > i {
        cursor: pointer;
        margin-right: 5px;
      }
      .el-icon-close {
        position: initial;
        opacity: 1;
      }
    }
    &:hover {
      background-color: rgba($color: #cccccccc, $alpha: 0.5);
      .file-operate {
        opacity: 1;
      }
    }
  }
  .file-table {
    margin-top: 22px;
  }
}
.preview-dialog {
  ::v-deep .el-dialog__body {
    height: 84vh;
  }
  ::-webkit-scrollbar {
    display: block;
  }
}
</style>
