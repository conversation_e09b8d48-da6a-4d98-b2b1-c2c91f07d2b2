/**
 * 数值分布算法
 * @param distributeType 分布类型: 1-逐渐减少, 2-逐渐增大, 3-平均分布, 4-先大后小
 * @param totalValue 总的值
 * @param count 生成的个数
 * @returns 子项和为totalValue的数组，排列方式符合distributeType
 */
export function generateDistribution(distributeType: number, totalValue: number, count: number): number[] {
  if (count <= 0 || totalValue < 0) {
    return [];
  }

  const sum = (n: number): number => {
    return (n * (n + 1)) / 2;
  };

  let result: number[] = [];
  let num2: number[] = [];
  let num3: number[] = [];

  if (distributeType === 4) {
    // 先大后小
    let f1 = 0, f2 = 0;
    if (count % 2 === 0) {
      f1 = count / 2 + 1;
      f2 = count - f1;
    } else {
      f2 = Math.floor(count / 2);
      f1 = f2 + 1;
    }
    
    const summary1 = sum(f1);
    const summary2 = sum(f2);
    
    for (let i = 1; i <= f1; i++) {
      const num = Number((totalValue / 2 / summary1) * i).toFixed(2);
      num2.push(Number(num));
    }
    
    for (let i = 1; i <= f2; i++) {
      const num = Number((totalValue / 2 / summary2) * i).toFixed(2);
      num3.push(Number(num));
    }
    
    num3.sort((a, b) => b - a);
    result = num2.concat(num3);
  } else {
    const summary = sum(count);
    
    for (let index = 1; index <= count; index++) {
      let num = 0;
      if (distributeType === 3) {
        // 平均分布
        num = +Number(totalValue / count).toFixed(2);
      } else {
        // 逐渐变化
        num = +Number((totalValue / summary) * index).toFixed(2);
      }
      
      if (isNaN(Number(num))) {
        result.push(0);
      } else {
        result.push(Number(num));
      }
    }
  }

  // 根据分布类型排序
  if (distributeType === 1) {
    result.sort((a, b) => b - a); // 逐渐减少
  } else if (distributeType === 2) {
    result.sort((a, b) => a - b); // 逐渐增大
  }

  return result;
}