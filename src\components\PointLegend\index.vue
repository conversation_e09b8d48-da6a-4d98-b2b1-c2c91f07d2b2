<template>
  <div class="point-legned" v-if="!isSingleProject">
    <div :class="['legend-container', isShowLegend ? 'open' : '']">
      <div class="legend-title color-white">
        <h4>图例</h4>
        <i class="el-icon-close close-legend" @click="isShowLegend = false"></i>
      </div>
      <div class="legend-container-box">
        <el-checkbox-group class="flex flex-column" v-model="ownStateCheck" @change="handleStateCheckGroupChange">
          <el-checkbox class="mt20 flex-vc" v-for="val of checkTypeData" :label="val.value" :key="val.value" @change="isChecked => handlePStateCheckChange(isChecked, val.value)">
            <img v-if="val.src" class="check-img" :src="val.src" :alt="val.label">
            <span class="flex-vc color-white font20">{{ val.label }}</span>
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
    <button type="button" class="legend-btn" @click="isShowLegend = true" v-show="!isShowLegend">图例<i class="el-icon-caret-left"></i></button>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  name: 'PointLegend',
  props: {
    checkTypeData: {
      type: Array,
      default: () => []
    },
    stateCheck: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      isShowLegend: true,
      cancelCheckedState: null
    }
  },
  computed: {
    ...mapState({
      isSingleProject: state => state.project.isSingleProject
    }),
    ownStateCheck: {
      get () {
        return this.stateCheck
      },
      set (val) {
        this.$emit('update:stateCheck', val)
      }
    }
  },
  methods: {
    // “正常”、“异常”选项组的回调函数
    handleStateCheckGroupChange (val) {
      if (val.length === 0) {
        this.$message.warning('请至少选择一种状态')
        this.ownStateCheck = [this.cancelCheckedState]
        return false
      }
    },
    // 选择“正常”、“异常”选项的回调函数
    handlePStateCheckChange (isChecked, val) {
      this.cancelCheckedState = isChecked ? null : val
    }
  }
}
</script>

<style lang="scss" scoped>
.point-legned {
  position: absolute;
  bottom: 66px;
  right: 20px;
}
.legend-btn {
  width: 60px;
  height: 128px;
  color: white;
  cursor: pointer;
  font-size: 22px;
  font-weight: 700;
  border: 1px solid #368AFF;
  border-radius: 4px;
  background-color: transparent;
  background-image: linear-gradient(0deg, #0f4898, #114081);
}

.legend-container {
  width: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 0;
  border-radius: 4px;
  box-sizing: border-box;
  border: 0;
  opacity: 0;
  background-image: linear-gradient(0deg, rgba(15, 73, 153, 0.8), rgba(17, 64, 131, 0.8));
  transition-property: width,padding,border,opacity;
  transition-duration: 0.3s;

  &.open {
    width: 10vw;
    padding: 20px;
    border: 1px solid #368AFF;
    opacity: 1;
  }

  .legend-title {
    @include flexVC;
    justify-content: space-between;
    font-size: 22px;
    font-weight: 700;
  }

  .close-legend {
    cursor: pointer;
    color: #ffffff;

    &:hover, &:active {
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .legend-container-box {
    max-height: 28vh;
    overflow-y: auto;

    ::v-deep(.el-checkbox__label) {
      display: flex;
      align-items: center;
    }
    ::v-deep(.el-checkbox__input) {
      &.is-checked {
        .el-checkbox__inner {
          background-color: rgb(0, 117, 232);
          border-color: rgb(0, 117, 232);
        }
      }
      .el-checkbox__inner {
        width: 24px;
        height: 24px;
        &::after {
          transform: rotate(45deg) scaleY(1.4);
          top: 4px;
          left: 8px;
        }
      }
    }
  }
}

.legend-list {
  display: flex;
  flex-wrap: wrap;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-clip: padding-box;
  margin-top: 12px;
}

.legend-item {
  flex: 0 0 calc(50% - 6px);
  font-size: 18px;
  @include flexVC;
  color: #ffffff;
  margin-top: 16px;

  &:nth-child(odd) {
    margin-right: auto;
  }
}

.check-img {
  user-select: none;
  width: 18px;
  height: 18px;
  margin: 0 10px;
}
</style>
