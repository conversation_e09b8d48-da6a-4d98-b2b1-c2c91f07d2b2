<template>
  <el-select
    v-model="taskValue"
    multiple
    reserve-keyword
    style="width: 100%"
    :placeholder="placeholder"
    :disabled="disabled"
    v-bind="$attrs"
    @remove-tag="removeTag"
  >
    <el-option :value="''" label="全部" v-if="dataSource.length !== 0">
      <el-checkbox v-model="checkAll" @change="handleAllChange"
        >全部</el-checkbox
      >
    </el-option>
    <el-option
      v-for="(label, value) in dataSource"
      :key="value"
      :label="label"
      :value="value"
    >
      <el-checkbox
        v-model="checkOptions[value]"
        @change="handleItemChange(value)"
        >{{ label }}</el-checkbox
      >
    </el-option>
  </el-select>
</template>
<script lang="ts">
export default {
  name: "TaskSelect",
  props: {
    placeholder: { type: String, default: "请选择" },
    disabled: { type: Boolean, default: false },
    option: {
      type: Object,
      default: () => ({
        label: 'label',
        value: 'value'
      })
    },
    list: {
      type: [Object, Array],
      default: () => ({}),
    },
    modelValue: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      taskValue: [],
      dataSource: {},
      checkAll: false,
      checkOptions: {},
    };
  },
  watch: {
    modelValue: {
      handler(newVal) {
        if(this.taskValue.join('-') !== newVal.join('-')) {
          this.taskValue = JSON.parse(JSON.stringify(newVal));
        }
      },
      immediate: true
    },
    list: {
      handler(newVal) {
        // 处理下拉框选项格式
        if(Array.isArray(newVal)) {
          newVal.forEach(item => {
            this.dataSource[item[this.option.value || 'value']] = item[this.option.label || 'label']
          })
        } else {
          this.dataSource = newVal;
        }
        
        
        // 是否全选
        if (this.modelValue[0] === "") {
          this.checkAll = true;
        }
        Object.keys(this.dataSource).forEach((key) => {
          this.checkOptions[key] =
            this.modelValue[0] === "" || this.modelValue.includes(key);
        });
      },
      immediate: true,
    },
  },
  computed: {},
  methods: {
    // 全选
    handleAllChange(flag) {
      Object.keys(this.checkOptions).forEach((key) => {
        this.checkOptions[key] = flag;
      });
      this.$emit("update:modelValue", flag ? [""] : []);
    },
    // 选项复选框点击事件
    handleItemChange(value) {
      const selectActive = [...this.modelValue, this.checkOptions[value] && value]
      this.checkAll = selectActive.length === Object.keys(this.dataSource).length;      
      this.$emit("update:modelValue", this.checkAll ? [""] : selectActive);
    },
    // 清除事件
    removeTag(tag) {
      let selectActive = new Set(this.modelValue);
      if (tag !== "") {
        if(this.checkOptions.hasOwnProperty(tag)) {
          this.checkOptions[tag] = false;
          selectActive.delete(tag)
        }
      } else {
        Object.keys(this.checkOptions).forEach((key) => {
          this.checkOptions[key] = false;
        });
        selectActive.clear()
        this.checkAll = false;
      }      
      this.$emit("update:modelValue", [...selectActive]);
    }
  }
};
</script>