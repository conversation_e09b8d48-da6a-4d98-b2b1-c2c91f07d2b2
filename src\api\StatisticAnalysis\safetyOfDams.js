/* 报表-大坝安全 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";
const api = {
  safetyOfDamList: apiUrl.defaultUrl + "/dsc/getDamSafetyForms",
  safetyOfDamExport: apiUrl.defaultUrl + "/dsc/getDamSafetyDownFormsExcel",
  getNewDamSectionReport: apiUrl.defaultUrl + "/res-dam-osmotic/getNewDamSectionReport"
};

/**
 * 大坝安全列表查询
 */
export function safetyOfDamList(data) {
  return $http.get(api.safetyOfDamList, data);
}
// 大坝安全 导出
export function safetyOfDamExport(params, type) {
  return $http.getDownLoad(api.safetyOfDamExport, params, undefined, "blob");
}

// 安全监测 大坝安全
export function getNewDamSectionReport(params) {
  return $http.get(api.getNewDamSectionReport, params);
}
