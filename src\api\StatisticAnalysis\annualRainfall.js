/* 报表-年度降雨量 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";
const api = {
  getRainfallCountReport: apiUrl.defaultUrl + "/rlc/getRainfallCountReport",
  rainFaillExport: apiUrl.defaultUrl + "/rlc/rainFaillExport",
};

/**
 * 年度降雨量列表查询
 */
export function getRainfallCountReport(params) {
  return $http.get(api.getRainfallCountReport, params);
}
// 年度降雨量 导出
export function rainFaillExport(params) {
  return $http.getDownLoad(api.rainFaillExport, params, undefined, "blob");
}
