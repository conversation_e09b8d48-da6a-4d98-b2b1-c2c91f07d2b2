<template>
  <div class="container">
    <left-region>
      <div class="left flx-1 overflow-hide flx-column">
        <base-title title="法规制度" />
        <div class="laws-and-regulations">
          <label
            v-for="(val, index) of lawsAndRegulationsData"
            :key="val.endName"
            :class="['laws-and-regulations-label', 'pointer', 'title-' + index]"
            @click="fileListClick(val)"
          >
            {{ val.endName }}
          </label>
          <div
            v-for="(val, index) of lawsAndRegulationsData"
            :key="val.id"
            :class="['laws-and-regulations-num', 'flx-center', 'pointer', 'value-' + index]"
            @click="fileListClick(val)"
          >
            {{ val.fileCount }}
          </div>
        </div>
        <base-title title="标准化工作手册" />
        <div :class="['workbook', 'flx-column', workbookData.length ? '' : 'flx-hc']">
          <div
            v-for="val of workbookData"
            :key="val.id"
            class="workbook-item pointer overflow-hide flx-column"
            @click="fileClick(val)"
          >
            <el-tooltip effect="dark" :content="val.mc" placement="top-start">
              <div class="workbook-item-title mle">{{ val.mc }}</div>
            </el-tooltip>
            <!-- <span class="workbook-item-type">({{ val.type }})</span> -->
          </div>
          <no-data v-show="!workbookData.length" :isActual="true" />
        </div>
        <base-title class="mt15" title="库区森林防火" />
        <!-- <div class="forest-fire-prevention mt15 flx-1">
          <div
            v-for="(val, index) of forestFirePreventionData"
            :key="val.id"
            class="forest-fire-prevention-item pointer flx"
            @click="fileListClick(val)"
          >
            <div :class="['forest-fire-prevention-item-icon', 'icon-' + index]"></div>
            <div class="flx-column">
              <span class="forest-fire-prevention-item-label">{{ val.endName }}</span>
              <span class="forest-fire-prevention-item-value">{{ val.fileCount }}</span>
            </div>
          </div>
        </div> -->
        <div class="forest-fire-prevention-box mt15 flx-1">
          <div class="forest-fire-prevention-top flx">
            <div
              v-for="(val, index) of forestFirePreventionData.slice(0, 2)"
              :key="val.dictCode"
              :class="[
                'forest-fire-prevention-item pointer flx-column',
                'forest-fire-prevention-item-' + index
              ]"
              @click="fileListClick(val)"
            >
              <div
                :class="[
                  'forest-fire-prevention-item-text flx-align-center',
                  'forest-fire-prevention-item-text-' + val.dictCode
                ]"
              >
                {{ val.endName }}
              </div>
              <div class="forest-fire-prevention-item-value">{{ val.fileCount }}</div>
            </div>
          </div>
          <div class="forest-fire-prevention-bottom flx">
            <div
              v-for="(val, index) of forestFirePreventionData.slice(2)"
              :key="val.dictCode"
              :class="[
                'forest-fire-prevention-item pointer flx-column',
                'forest-fire-prevention-item-' + index
              ]"
              @click="fileListClick(val)"
            >
              <div
                :class="[
                  'forest-fire-prevention-item-text flx-align-center',
                  'forest-fire-prevention-item-text-' + val.dictCode
                ]"
              >
                {{ val.endName }}
              </div>
              <div class="forest-fire-prevention-item-value">{{ val.fileCount }}</div>
            </div>
          </div>
          <div class="forest-fire-prevention-middle"></div>
        </div>
      </div>
    </left-region>
    <right-region>
      <div class="right flx-1 overflow-hide flx-column">
        <base-title title="规章制度" />
        <div class="rules-and-regulations mb15 mt30">
          <label
            v-for="(val, index) of rulesAndRegulationsData"
            :key="val.endName"
            :class="['rules-and-regulations-label', 'pointer', 'title-' + index]"
            @click="fileListClick(val)"
          >
            {{ val.endName }}
          </label>
          <div
            v-for="(val, index) of rulesAndRegulationsData"
            :key="val.dictCode"
            :class="[
              'rules-and-regulations-num pointer flx-center',
              'rules-and-regulations-num-' + val.dictCode,
              'value-' + index
            ]"
            @click="fileListClick(val)"
          >
            {{ val.fileCount }}
          </div>
        </div>
        <base-title title="热门制度排行榜" />
        <div class="ranking-list mt15 pr10 flx-1 flx-column overflow-hide">
          <div class="ranking-list-header flx">
            <div class="ranking-list-header-label ranking-list-w1">名次</div>
            <div class="ranking-list-header-label pl10 pr10 flx-1">名称</div>
            <div class="ranking-list-header-label ranking-list-w2">阅读次数</div>
          </div>
          <div class="ranking-list-body flx-1" @scroll="handleScroll">
            <div
              v-for="(val, index) of rankingListData"
              :key="val.id"
              :class="[
                'ranking-list-item pointer flx-center overflow-hide',
                'ranking-list-item-' + index
              ]"
              @click="fileClick(val)"
            >
              <div
                :class="[
                  'ranking-list-item-index',
                  'ranking-list-w1',
                  'flx-center',
                  'fs-16',
                  'h-full',
                  index <= 2 ? 'ranking-list-item-index-' + index : ''
                ]"
              >
                {{ index <= 2 ? '' : index + 1 }}
              </div>
              <el-tooltip
                :disabled="scrollVisible"
                effect="dark"
                :content="val.mc"
                placement="top-end"
                :hide-after="0"
              >
                <div class="ranking-list-item-title mle flx-1 pl10 pr10 fs-16">{{ val.mc }}</div>
              </el-tooltip>
              <div class="ranking-list-item-num flx-align-center ranking-list-w2 h-full">
                {{ val.clickCount }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </right-region>
    <file-preview-dialog ref="filePreviewDialogRef"></file-preview-dialog>
  </div>
</template>

// 强化法治
<script setup lang="ts">
import { ResultData } from '@/api/interface'
import { GetFileListByPathPrefixVO, File } from '@/api/interface/perfectingSystem/VO.d'
import { getFileListByPathPrefix } from '@/api/module/fourInstitutional/perfectingSystem'
import noData from '@/views/FourPrevention/Warning/components/NoWarning.vue'
import { useSetMapGeoJsonLayer } from '@/hooks/useSetMapGeoJsonLayer'

const setMapGeoJsonLayer = new useSetMapGeoJsonLayer()

const filePreviewDialogRef = ref()

const lawsAndRegulationsData = ref([
  {
    id: 0,
    endName: '水法',
    fileCount: 0
  },
  {
    id: 1,
    endName: '防洪法',
    fileCount: 0
  },
  {
    id: 2,
    endName: '大坝安全管理',
    fileCount: 0
  },
  {
    id: 3,
    endName: '标准化管理',
    fileCount: 0
  },
  {
    id: 4,
    endName: '管理条例',
    fileCount: 0
  }
])
function fileListClick(val) {
  filePreviewDialogRef.value.previewFileList(val.fileList)
}

const workbookData = ref<File[]>([])
function fileClick(val) {
  filePreviewDialogRef.value.preview(val)
}

const forestFirePreventionData = ref([
  {
    dictCode: 0,
    endName: '森林防火应急预案',
    fileCount: 0
  },
  {
    dictCode: 1,
    endName: '森林防火通知',
    fileCount: 0
  },
  {
    dictCode: 2,
    endName: '森林防火工作总结',
    fileCount: 0
  },
  {
    dictCode: 3,
    endName: '森林防火演练',
    fileCount: 0
  }
])

const rulesAndRegulationsData = ref([
  {
    id: 0,
    endName: '值班管理制度',
    fileCount: 0,
    dictCode: 1
  },
  {
    id: 1,
    endName: '防洪度汛制度',
    fileCount: 0,
    dictCode: 2
  },
  {
    id: 2,
    endName: '防汛检查制度',
    fileCount: 0,
    dictCode: 1
  },
  {
    id: 3,
    endName: '防汛抢险制度',
    fileCount: 0,
    dictCode: 1
  },
  {
    id: 4,
    endName: '防汛抗旱工作制度',
    fileCount: 0,
    dictCode: 2
  },
  {
    id: 5,
    endName: '日常管理制度',
    fileCount: 0,
    dictCode: 3
  }
])

const rankingListData = ref([])

async function getData(path, type = 'file'): Promise<ResultData<GetFileListByPathPrefixVO> | null> {
  let res: ResultData<GetFileListByPathPrefixVO> | null
  try {
    res = await getFileListByPathPrefix({ path, type })
  } catch (e) {
    console.log(e)
    ElMessage.error('获取文件数据失败')
    return null
  }
  return res
}

/* 列表滚动时禁用tooltip —— start */
const scrollVisible = ref(false)
function handleScroll() {
  scrollVisible.value = true
  setTimeout(() => {
    scrollVisible.value = false
  }, 10)
}
/* 列表滚动时禁用tooltip —— end */

onMounted(async () => {
  const lawsAndRegulationsRes = await getData('四制-强化法治-法律法规')
  if (
    lawsAndRegulationsRes &&
    lawsAndRegulationsRes.status === 200 &&
    Array.isArray(lawsAndRegulationsRes.data)
  ) {
    lawsAndRegulationsData.value = lawsAndRegulationsRes.data
  }
  const rulesRes = await getData('四制-强化法治-政策法规')
  if (rulesRes && rulesRes.status === 200 && Array.isArray(rulesRes.data)) {
    lawsAndRegulationsData.value = [...lawsAndRegulationsData.value, ...rulesRes.data]
  }

  const forestFirePreventionRes = await getData('四制-强化法治-库区森林防火')
  if (
    forestFirePreventionRes &&
    forestFirePreventionRes.status === 200 &&
    Array.isArray(forestFirePreventionRes.data)
  ) {
    forestFirePreventionData.value = forestFirePreventionRes.data
  }
  const workbookRes = await getData('四制-强化法治-标准化工作手册')
  if (workbookRes && workbookRes.status === 200 && Array.isArray(workbookRes.data)) {
    workbookData.value = workbookRes.data[0].fileList
  }

  const rulesAndRegulationsRes = await getData('四制-强化法治-规章制度')
  if (
    rulesAndRegulationsRes &&
    rulesAndRegulationsRes.status === 200 &&
    Array.isArray(rulesAndRegulationsRes.data)
  ) {
    console.log('rulesAndRegulationsRes.data', rulesAndRegulationsRes.data)

    rulesAndRegulationsData.value = rulesAndRegulationsRes.data
  }
  const rankingListRes = await getData('', 'ranking')
  if (
    rankingListRes &&
    rankingListRes.status === 200 &&
    rankingListRes.data &&
    Array.isArray(rankingListRes.data) &&
    Array.isArray(rankingListRes.data[0].fileList)
  ) {
    rankingListData.value = rankingListRes.data[0].fileList.slice(0, 10)
  }

  nextTick(() => {
    setMapGeoJsonLayer.layerRender([
      {
        category: '7', // 库区森林防护范围图层分类字典值
        textColor: { r: 119, g: 255, b: 255, a: 1 },
        borderColor: { r: 243, g: 107, b: 108, a: 1 },
        labelKey: '名称|name|', // 在json数据里面的features数据中查找
        text: '库区森林防护范围',
        pixelOffset: [10, 100],
        zoom: true
      },
      {
        category: '4', // 库区森林防火范围，暂时使用“已取得产权的范围”图层代替
        textColor: { r: 119, g: 255, b: 255, a: 1 },
        borderColor: { r: 243, g: 107, b: 108, a: 1 },
        labelKey: 'Name',
        pixelOffset: [20, 100],
        text: '库区森林防火范围'
      }
    ])
  })
})

onBeforeUnmount(() => {
  setMapGeoJsonLayer.removeAllLayer()
})
</script>

<style lang="scss" scoped>
.laws-and-regulations {
  position: relative;
  width: 345px;
  height: 216px;
  margin: 28px auto 20px;
  background-image: url('@/assets/images/IntensifyTheRuleByLaw/laws-and-regulations-bg.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 386px;

  .laws-and-regulations-label {
    position: absolute;
    font-size: 16px;
    color: #fff;

    &.title-0 {
      top: -24px;
      right: 30px;
    }

    &.title-1 {
      bottom: 80px;
      left: -2px;
    }

    &.title-2 {
      top: -16px;
      left: 18px;
    }

    &.title-3 {
      right: 0;
      bottom: 112px;
    }

    &.title-4 {
      right: 80px;
      bottom: 60px;
    }
  }

  .laws-and-regulations-num {
    position: absolute;
    font-family: YouSheBiaoTiHei, sans-serif;
    color: #fff;
    text-shadow: 0 4px 6.8px rgb(0 0 0 / 25%);

    &.value-0 {
      top: 0;
      right: 28px;
      width: 63px;
      height: 63px;
      font-size: 30px;
    }

    &.value-1 {
      bottom: 36px;
      left: 21px;
      width: 43px;
      height: 43px;
      font-size: 24px;
    }

    &.value-2 {
      top: 8px;
      left: 8px;
      width: 50px;
      height: 50px;
      font-size: 30px;
    }

    &.value-3 {
      right: 9px;
      bottom: 61px;
      width: 51px;
      height: 50px;
      font-size: 24px;
    }

    &.value-4 {
      right: 89px;
      bottom: 6px;
      width: 53px;
      height: 50px;
      font-size: 24px;
    }
  }
}

.workbook {
  height: 196px;
  margin-left: 12px;
  overflow-y: auto;

  .workbook-item {
    position: relative;
    box-sizing: border-box;
    justify-content: space-between;
    width: 100%;
    max-height: 60px;
    padding: 8px 32px 8px 35px;
    margin-top: 8px;
    background-image: url('@/assets/images/IntensifyTheRuleByLaw/workbook-item-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    &::before {
      position: absolute;
      top: 14px;
      left: 14px;
      display: block;
      width: 10px;
      height: 11px;
      content: '';
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/workbook-item-icon.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .workbook-item-title {
      font-size: 16px;
      line-height: 24px;
      color: #fff;
    }

    .workbook-item-type {
      font-size: 12px;
      color: #7ff;
    }
  }
}

.forest-fire-prevention {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  margin-left: 30px;
  overflow-y: auto;

  .forest-fire-prevention-item {
    width: 200px;
    height: 80px;
    max-height: 80px;

    &:nth-child(even) {
      margin-left: auto;
    }

    .forest-fire-prevention-item-icon {
      width: 54px;
      height: 80px;
      margin-right: 12px;
      user-select: none;
      background-repeat: no-repeat;
      background-size: 100% 100%;

      &.icon-0 {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-icon-0.png');
      }

      &.icon-1 {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-icon-1.png');
      }

      &.icon-2 {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-icon-2.png');
      }

      &.icon-3 {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-icon-3.png');
      }
    }

    .forest-fire-prevention-item-label {
      margin-top: 18px;
      font-size: 16px;
      color: #fff;
    }

    .forest-fire-prevention-item-value {
      font-family: YouSheBiaoTiHei, sans-serif;
      font-size: 30px;
      color: #fff;
      text-shadow: 0 4px 6.8px rgb(0 0 0 / 25%);
    }
  }
}

.rules-and-regulations {
  position: relative;
  width: 100%;
  height: 260px;
  background-image: url('@/assets/images/IntensifyTheRuleByLaw/rules-and-regulations-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .rules-and-regulations-label {
    position: absolute;
    font-size: 14px;
    color: #fff;
    text-align: center;
    text-shadow: 0 4px 4px #0164c0;

    &.title-0 {
      top: 92px;
      left: 0;
      transform: skew(-4deg, -10deg);
    }

    &.title-1 {
      top: -18px;
      right: 90px;
      transform: skew(1deg, 13deg);
    }

    &.title-2 {
      top: 20px;
      right: 0;
      transform: skew(1deg, 13deg);
    }

    &.title-3 {
      top: 96px;
      right: 106px;
      transform: skew(1deg, 13deg);
    }

    &.title-4 {
      top: 140px;
      right: 0;
      transform: skew(1deg, 13deg);
    }

    &.title-5 {
      bottom: 84px;
      left: 146px;
      transform: skew(1deg, 13deg);
    }
  }

  .rules-and-regulations-num {
    position: absolute;
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 26px;
    text-shadow: 0 4px 6.8px rgb(0 0 0 / 25%);
    background-repeat: no-repeat;
    background-position-x: 4px;
    background-size: 100% 100%;

    &.value-0 {
      top: 116px;
      left: 16px;
      width: 60px;
      height: 60px;
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/rules-and-regulations-num-1.png');
      animation: jump 1.3s ease-in-out infinite;
    }

    &.value-1 {
      top: 4px;
      right: 116px;
      width: 60px;
      height: 60px;
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/rules-and-regulations-num-2.png');
      animation: jump 1.4s ease-in-out infinite;
    }

    &.value-2 {
      top: 46px;
      right: 18px;
      width: 60px;
      height: 60px;
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/rules-and-regulations-num-1.png');
      animation: jump 1.3s ease-in-out infinite;
    }

    &.value-3 {
      right: 126px;
      bottom: 82px;
      width: 60px;
      height: 60px;
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/rules-and-regulations-num-1.png');
      animation: jump 1.3s ease-in-out infinite;
    }

    &.value-4 {
      right: 23px;
      bottom: 34px;
      width: 60px;
      height: 60px;
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/rules-and-regulations-num-2.png');
      animation: jump 1.4s ease-in-out infinite;
    }

    &.value-5 {
      bottom: 24px;
      left: 150px;
      width: 48px;
      height: 60px;
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/rules-and-regulations-num-3.png');
      animation: jump 1.5s ease-in-out infinite;
    }

    &:hover {
      animation: none;
    }
  }
}

@keyframes jump {
  0%,
  100% {
    transform: translateY(0); /* 起始和结束时元素位于原位置 */
  }

  50% {
    transform: translateY(-4px); /* 在上移后稍微调整位置 */
  }
}

.ranking-list {
  .ranking-list-w1 {
    width: 70px;
    text-align: center;
  }

  .ranking-list-w2 {
    width: 100px;
  }

  .ranking-list-header-label {
    font-size: 14px;
    color: #fff;
  }

  .ranking-list-body {
    overflow-y: auto;
  }

  .ranking-list-item {
    height: 68px;
    margin-top: 8px;
    background-image: url('@/assets/images/IntensifyTheRuleByLaw/ranking-list-item-active.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .ranking-list-item-index {
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 16px;
    color: #7ff;
    text-align: right;
    background-image: url('@/assets/images/IntensifyTheRuleByLaw/ranking-list-item-index-bg.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 25px 25px;

    &.ranking-list-item-index-0 {
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/ranking-list-item-index-bg-one.png');
      background-size: 44px 44px;
    }

    &.ranking-list-item-index-1 {
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/ranking-list-item-index-bg-two.png');
      background-size: 44px 44px;
    }

    &.ranking-list-item-index-2 {
      background-image: url('@/assets/images/IntensifyTheRuleByLaw/ranking-list-item-index-bg-three.png');
      background-size: 44px 44px;
    }
  }

  .ranking-list-item-num {
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 30px;
    color: rgb(119 255 255);
    text-shadow: 0 4px 6.8px rgb(0 0 0 / 25%);
  }
}

.forest-fire-prevention-box {
  position: relative;

  .forest-fire-prevention-top {
    height: 85px;
    margin-bottom: 7px;
    background: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-top-bg.png')
      no-repeat;
    background-size: 100% 100%;
  }

  .forest-fire-prevention-bottom {
    height: 85px;
    background: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-bottom-bg.png')
      no-repeat;
    background-size: 100% 100%;
  }

  .forest-fire-prevention-middle {
    position: absolute;
    top: 48px;
    left: 174px;
    display: flex;
    justify-content: center;
    width: 90px;
    height: 90px;
    background: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-middle-bg.png')
      no-repeat;
    background-size: 100% 100%;

    &::after {
      position: relative;
      width: 55px;
      height: 55px;
      margin-top: 6px;
      content: '';
      background: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-middle-icon.png')
        no-repeat;
      background-size: 100% 100%;
      animation: jump2 1.5s ease infinite;
    }
  }

  .forest-fire-prevention-item {
    margin-top: 12px;

    &-0 {
      margin-right: auto;
      margin-left: 8px;
    }

    &-1 {
      margin-right: 30px;
    }
  }

  .forest-fire-prevention-item-text {
    font-size: 16px;

    &::before {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      content: '';
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    &-24 {
      &::before {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-text-24.png');
      }
    }

    &-25 {
      &::before {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-text-25.png');
      }
    }

    &-26 {
      &::before {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-text-26.png');
      }
    }

    &-27 {
      &::before {
        background-image: url('@/assets/images/IntensifyTheRuleByLaw/forest-fire-prevention-item-text-27.png');
      }
    }
  }

  .forest-fire-prevention-item-value {
    padding-left: 33px;
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 30px;
  }
}

@keyframes jump2 {
  0%,
  100% {
    top: 0;
  }

  50% {
    top: -4px;
  }
}
</style>
