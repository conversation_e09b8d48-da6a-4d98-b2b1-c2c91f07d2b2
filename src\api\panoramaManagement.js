// 全景图管理

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  panoramaList: apiUrl.defaultUrl + '/panoramaManagement/list',
  deletePanoramaById: apiUrl.defaultUrl + '/panoramaManagement/deleteById',
  savePanorama: apiUrl.defaultUrl + '/panoramaManagement/save',
  marksList: apiUrl.defaultUrl + '/panoramaPoint/marksList',
  skipList: apiUrl.defaultUrl + '/panoramaPoint/skipList',
  deletePointById: apiUrl.defaultUrl + '/panoramaPoint/deleteById',
  savePoint: apiUrl.defaultUrl + '/panoramaPoint/save'
}

/**
 * 查询场景列表
 * @param data
 */
export function panoramaList (data) {
  return $http.post(api.panoramaList, data)
}

/**
 * 删除场景
 */
export function deletePanoramaById (data) {
  return $http.post(api.deletePanoramaById, data)
}

/**
 * 保存场景
 */
export function savePanorama (data) {
  return $http.post(api.savePanorama, data)
}

/**
 * 标注列表查询
 */
export function marksList (data) {
  return $http.post(api.marksList, data)
}

/**
 * 跳转列表查询
 */
export function skipList (data) {
  return $http.post(api.skipList, data)
}

/**
 * 删除点位
 */
export function deletePointById (data) {
  return $http.post(api.deletePointById, data)
}

/**
 * 保存点位
 */
export function savePoint (data) {
  return $http.post(api.savePoint, data)
}
