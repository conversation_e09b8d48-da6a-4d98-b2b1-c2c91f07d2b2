<template>
  <div class="container">
    <left-region>
      <div class="left flx-1 overflow-hide flx-column">
        <base-title title="管理主体明确" />
        <div class="unit-message-container">
          <div class="unit-message-item flx-align-center">
            <span class="unit-message-item-label">管理单位名称:</span>
            <span class="unit-message-item-content">{{ managementSubjectData.mnun }}</span>
          </div>
          <div class="unit-message-item flx-align-center">
            <span class="unit-message-item-label">管理单位类别：</span>
            <span class="unit-message-item-content">{{ managementSubjectData.mnunTypeStr }}</span>
          </div>
          <div class="unit-message-item flx-align-center">
            <span class="unit-message-item-label">管理单位性质：</span>
            <span class="unit-message-item-content">{{ managementSubjectData.mnunNature }}</span>
          </div>
          <div class="unit-message-item flx-align-center">
            <span class="unit-message-item-label">管理单位责任人：</span>
            <span class="unit-message-item-content">{{
              managementSubjectData.mnunResponsible
            }}</span>
          </div>
          <div class="unit-message-item flx-align-center">
            <span class="unit-message-item-label">主管单位：</span>
            <span class="unit-message-item-content">{{ managementSubjectData.cmun }}</span>
          </div>
        </div>
        <base-title title="管理体制" class="mt20" />
        <div class="system-container overflow-hide mt20 flx-1 flx-column">
          <div class="system-number-statistics flx">
            <div class="system-number-statistics-item flx left">
              <div class="system-number-statistics-item-icon"></div>
              <div class="system-number-statistics-item-label flx-column">
                <span class="system-number-statistics-item-label-text">内设部门</span>
                <span class="system-number-statistics-item-label-number">
                  {{ orgAndPersonInfo.org_count }}
                </span>
              </div>
            </div>
            <div class="system-number-statistics-item flx right">
              <div class="system-number-statistics-item-icon"></div>
              <div class="system-number-statistics-item-label flx-column">
                <span class="system-number-statistics-item-label-text">总人数</span>
                <span class="system-number-statistics-item-label-number">
                  {{ orgAndPersonInfo.person_count }}
                </span>
              </div>
            </div>
          </div>
          <div class="system-type-statistics overflow-hide mt10 flx-1 flx-column">
            <div class="system-type-statistics-tab flx">
              <div
                :class="[
                  'system-type-statistics-tab-item flx-1 flx-center',
                  active === 1 ? 'active' : ''
                ]"
                @click="handleTabChange(1)"
              >
                职称分布
              </div>
              <div
                :class="[
                  'system-type-statistics-tab-item flx-1 ml5 flx-center',
                  active === 2 ? 'active' : ''
                ]"
                @click="handleTabChange(2)"
              >
                学历分布
              </div>
            </div>
            <div class="system-type-statistics-tab-pan overflow-hide mt25 flx flx-1">
              <div class="system-type-statistics-tab-pan-item full" ref="titleChartRef"></div>
              <div class="system-type-statistics-tab-pan-legend ml36 mt10 flx-column">
                <div
                  v-for="(val, index) of titleChartLegend"
                  :key="val.name"
                  :class="[
                    'system-type-statistics-tab-pan-legend-item',
                    'flx-align-center',
                    'system-type-statistics-tab-pan-legend-item-' + index,
                    val.selected ? '' : 'noSelected'
                  ]"
                  @click="handleLegendClick(val)"
                >
                  <div class="icon"></div>
                  <div class="flx-align-center">
                    <span class="system-type-statistics-tab-pan-legend-name mr4">
                      {{ val.name }}
                    </span>
                    <span class="system-type-statistics-tab-pan-legend-value">{{ val.value }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </left-region>
    <right-region>
      <div class="right flx-1 overflow-hide flx-column">
        <base-title title="工程产权明晰" />
        <div class="ownership-container mb20 mt10 pl10 flx">
          <div class="ownership-left">
            <div class="ownership-num flx-column">
              <div class="ownership-num-item flx" v-for="val of ownershipNum" :key="val.label">
                <div>
                  <div class="ownership-num-item-label">{{ val.label }}</div>
                  <span class="ownership-num-item-value">{{ val.value }}</span>
                  <span>m²</span>
                </div>
              </div>
            </div>
            <div class="ownership-text flx-column mt16">
              <div
                class="ownership-text-item mt4 flx"
                v-for="val of ownershipText"
                :key="val.label"
              >
                <span class="ownership-text-label">{{ val.label }}</span>
                <div class="ownership-text-value flx-1">{{ val.value }} {{ val.unit }}</div>
              </div>
            </div>
          </div>
          <div class="ownership-right flx-column">
            <div class="ownership-chart mb28" ref="ownershipChartRef"></div>
            <div class="ownership-btn flx-center" @click="showFiles">
              <el-icon class="mr10"> <Search /> </el-icon>查看附件
            </div>
          </div>
        </div>
        <base-title title="专业化管护" />
        <!-- <div class="protection-container overflow-hide flx-column">
          <div class="protection-tree">
            <el-tooltip
              v-for="val of protectionTreeList"
              :key="val.label"
              effect="dark"
              :content="val.label"
              placement="top"
            >
              <img
                :src="val.icon"
                :class="['protection-tree-item', val.className]"
                @click="protectionTreeClick(val)"
              />
            </el-tooltip>
          </div>
          <div class="protection-content">
            <div
              v-for="val of protectionTreeList"
              :key="val.label"
              :class="['protection-content-item', 'flx', val.className]"
              @click="protectionTreeClick(val)"
            >
              <div class="flx-column">
                <span class="protection-content-item-title">{{ val.label }}</span>
                <span class="protection-content-item-num">{{ val.value }}</span>
              </div>
            </div>
          </div>
        </div> -->
        <div class="protection-box">
          <div
            v-for="(val, index) of protectionTreeList"
            :key="val.label"
            :class="[
              'protection-item',
              'pointer',
              'flx-column',
              val.className,
              'protection-item-' + index
            ]"
            @click="protectionTreeClick(val)"
          >
            <span class="protection-item-text mb10">{{ val.label }}</span>
            <div class="protection-item-value">{{ val.value }}</div>
          </div>
        </div>
      </div>
    </right-region>

    <file-preview-dialog ref="FilePreviewDialogRef"></file-preview-dialog>
  </div>
</template>

// 完善体制
<script setup lang="ts">
import LeftRegion from '@/layouts/components/LeftRegion.vue'
import RightRegion from '@/layouts/components/RightRegion.vue'
import BaseTitle from '@/components/BaseTitle/index.vue'
import FilePreviewDialog from '@/components/FilePreviewDialog/index.vue'
import useEcharts from '@/hooks/useEcharts'
import { useUserStore } from '@/stores/modules/user'
import { createResponsiveFontSize, fontSize } from '@/utils'
import { previewUrl } from '@/api/module/attachment'
import {
  getManagementSubject,
  getOrgAndPersonInfo,
  getGccqByWrpcd,
  getFileListByPathPrefix
} from '@/api/module/fourInstitutional/perfectingSystem'
import {
  ManagementSubjectVO,
  OrgAndPersonInfoVO,
  CqFile,
  GetFileListByPathPrefixVO
} from '@/api/interface/perfectingSystem/VO.d'
import { useSetMapGeoJsonLayer } from '@/hooks/useSetMapGeoJsonLayer'

const setMapGeoJsonLayer = new useSetMapGeoJsonLayer()

const { getFontSize } = createResponsiveFontSize()

const { drawChart } = useEcharts()

const userStore = useUserStore()

const managementSubjectData = ref<ManagementSubjectVO>({
  cmun: '楚雄彝族自治州水务局',
  mnun: '楚雄州青山嘴水库工程建设管理局',
  mnunNature: '正处级公益一类事业单位',
  mnunResponsible: '冯忠顾',
  mnunType: 2,
  mnunTypeStr: '准公益性水管单位',
  resCode: 'A532301S2007'
})

// 管理体制
const orgAndPersonInfo = ref<OrgAndPersonInfoVO>({
  org_count: 0,
  person_count: 0,
  highest_education_code_1_name: '博士研究生',
  highest_education_code_1_count: 0,
  highest_education_code_2_name: '硕士研究生',
  highest_education_code_2_count: 0,
  highest_education_code_3_name: '本科',
  highest_education_code_3_count: 0,
  highest_education_code_4_name: '大专',
  highest_education_code_4_count: 0,
  highest_education_code_5_name: '中专',
  highest_education_code_5_count: 0,
  highest_education_code_99_name: '其他',
  highest_education_code_99_count: 0,
  title_code_1_name: '技术员',
  title_code_1_count: 0,
  title_code_2_name: '助理工程师',
  title_code_2_count: 0,
  title_code_3_name: '工程师',
  title_code_3_count: 0,
  title_code_4_name: '高级工程师',
  title_code_4_count: 0,
  title_code_5_name: '正高级工程师',
  title_code_5_count: 0,
  gcsUp: 0,
  bkUp: 0
})
const active = ref()
const titleChartLegend = ref<{ name: string; value: number; selected: boolean }[]>([])
function extractEducationData(data, keyPrefix = 'highest_education_code_') {
  let regex = /^highest_education_code_(\d+)_/
  if (keyPrefix === 'title_code_') {
    regex = /^title_code_(\d+)_/
  }
  const grouped = {}

  Object.keys(data).forEach(key => {
    const match = key.match(regex)
    if (match) {
      const id = match[1]
      if (!grouped[id]) grouped[id] = {}
      if (key.includes('_name')) {
        grouped[id].name = data[key]
      } else if (key.includes('_count')) {
        grouped[id].value = data[key]
      }
    }
  })

  return Object.values(grouped)
}

interface TitleChartData {
  value: number
  name: string
}

function handleTabChange(index: number) {
  active.value = index
  let titleChartOptionsData = [] as Array<TitleChartData>
  let pieLabel = ''
  let pieNum = 0
  if (index === 1) {
    titleChartOptionsData = extractEducationData(
      orgAndPersonInfo.value,
      'title_code_'
    ) as Array<TitleChartData>
    pieLabel = '工程师及以上人数'
    pieNum = orgAndPersonInfo.value.gcsUp || 0
  } else {
    titleChartOptionsData = extractEducationData(orgAndPersonInfo.value) as Array<TitleChartData>
    pieLabel = '本科及以上人数'
    pieNum = orgAndPersonInfo.value.bkUp || 0
  }
  titleChartLegend.value = titleChartOptionsData.map(t => {
    return {
      ...t,
      selected: true
    }
  })
  initTitleChart(titleChartOptionsData, pieLabel, pieNum)
}
const titleChartRef = ref()
const titleChart = shallowRef()
function initTitleChart(seriesData, pieLabel, pieNum) {
  const options = {
    color: ['#FFFFFF', '#FFD060', '#77FFFF', '#BDFC47', '#43E75C', '#FF8A4B', '#d74a2e', '#e742d1'],
    tooltip: {
      trigger: 'item',
      position: function (point) {
        return [point[0] + 10, point[1] + 10] // 在鼠标右侧10px下方显示悬浮窗。
      }
    },
    legend: {
      show: false
    },
    series: [
      {
        z: 3,
        type: 'pie',
        radius: ['76%', '82%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: 'transparent',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'center',
          color: '#4c4a4a',
          formatter: `{active|${pieLabel}}\n\r{total|${pieNum}}`,
          rich: {
            total: {
              fontSize: getFontSize(40),
              fontFamily: 'YouSheBiaoTiHei',
              color: '#FFFFFF'
            },
            active: {
              fontFamily: '微软雅黑',
              fontSize: getFontSize(16),
              color: '#ffffff'
            }
          }
        },
        labelLine: {
          show: false
        },
        data: seriesData
      }
    ]
  }
  if (!titleChart.value) {
    titleChart.value = drawChart(titleChartRef.value, options)
  } else {
    titleChart.value.setOption(options)
  }
}
function handleLegendClick(val) {
  val.selected = !val.selected
  titleChart.value.dispatchAction({
    type: 'legendToggleSelect',
    name: val.name
  })
}

// 工程产权
const ownershipFiles = ref<CqFile[]>([])
const ownershipNum = ref([
  {
    label: '用地总面积',
    value: 6873702
  },
  {
    label: '已取得产权面积',
    value: 69873702
  }
])
const ownershipText = ref([
  {
    label: '不动产编号：',
    value: '0134489988号'
  },
  {
    label: '产权人：',
    value: '楚雄彝族自治州青山嘴水库工程建设管理局'
  },
  {
    label: '颁证时间：',
    value: '2024年05月12号'
  },
  {
    label: '产权面积：',
    value: 69873702,
    unit: '㎡'
  }
])
const ownershipChartRef = ref()
const ownershipChart = shallowRef()
function initOwnershipChart() {
  const seriesData = [
    {
      label: '已取得产权面积',
      value: ownershipNum.value[1].value
    },
    {
      label: '未取得产权面积',
      value: ownershipNum.value[0].value - ownershipNum.value[1].value
    }
  ]
  const pieNum = (ownershipNum.value[1].value / ownershipNum.value[0].value) * 100

  const options = {
    color: ['#77FFFF', 'rgba(255, 255, 255, 0.26)'],
    legend: {
      show: false
    },
    // tooltip: {
    //   trigger: 'item',
    //   position: function (point, params, dom, rect, size) {
    //     const tooltipWidth = dom.offsetWidth || 100
    //     const [x, y] = point

    //     // 鼠标左侧，纵向稍下
    //     return [x - tooltipWidth, y + 10]
    //   }
    // },
    series: [
      {
        type: 'pie',
        radius: ['80%', '90%'],
        center: ['50%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderColor: 'transparent',
          borderWidth: 2
        },
        label: {
          show: true,
          position: 'center',
          formatter: `{num|${ownershipNum.value[1].value}}{active| m²}\n\r{active2|已取得产权面积}\n\r{total|${pieNum.toFixed(2)}%}`,
          rich: {
            num: {
              fontSize: getFontSize(26),
              color: '#7ff'
            },
            total: {
              fontSize: getFontSize(28),
              fontFamily: 'YouSheBiaoTiHei',
              color: '#77FFFF'
            },
            active: {
              fontFamily: '微软雅黑',
              fontSize: getFontSize(14),
              color: '#ffffff'
            },
            active2: {
              fontFamily: '微软雅黑',
              fontSize: getFontSize(14),
              padding: [fontSize(10), 0, fontSize(4), 0],
              color: '#ffffff'
            }
          }
        },
        labelLine: {
          show: false
        },
        data: seriesData
      }
    ]
  }
  if (!ownershipChart.value) {
    ownershipChart.value = drawChart(ownershipChartRef.value, options)
  } else {
    ownershipChart.value.setOption(options)
  }
}
function showFiles() {
  if (Array.isArray(ownershipFiles.value) && ownershipFiles.value.length > 0) {
    dialogTitle.value = '工程产权明晰'
    showDialog.value = true

    FilePreviewDialogRef.value.previewFileList(ownershipFiles.value)
    if (ownershipFiles.value.length) {
      handleClickFile(ownershipFiles.value[0])
    }
  } else {
    ElMessage.warning('暂无附件数据')
  }
}

// 专业化管护
const protectionTreeList = ref<GetFileListByPathPrefixVO[]>([])
const treeListIcon = {
  1: {
    icon: new URL(
      `@/assets/images/perfectingSystem/protection-tree-icon-seven.png`,
      import.meta.url
    ).href,
    className: 'protection-tree-icon-seven'
  },
  2: {
    icon: new URL(`@/assets/images/perfectingSystem/protection-tree-icon-one.png`, import.meta.url)
      .href,
    className: 'protection-tree-icon-one'
  },
  3: {
    icon: new URL(`@/assets/images/perfectingSystem/protection-tree-icon-two.png`, import.meta.url)
      .href,
    className: 'protection-tree-icon-two'
  },
  4: {
    icon: new URL(
      `@/assets/images/perfectingSystem/protection-tree-icon-three.png`,
      import.meta.url
    ).href,
    className: 'protection-tree-icon-three'
  },
  5: {
    icon: new URL(`@/assets/images/perfectingSystem/protection-tree-icon-four.png`, import.meta.url)
      .href,
    className: 'protection-tree-icon-four'
  },
  6: {
    icon: new URL(`@/assets/images/perfectingSystem/protection-tree-icon-five.png`, import.meta.url)
      .href,
    className: 'protection-tree-icon-five'
  },
  7: {
    icon: new URL(`@/assets/images/perfectingSystem/protection-tree-icon-six.png`, import.meta.url)
      .href,
    className: 'protection-tree-icon-six'
  },
  8: {
    icon: new URL(
      `@/assets/images/perfectingSystem/protection-tree-icon-eight.png`,
      import.meta.url
    ).href,
    className: 'protection-tree-icon-eight'
  },
  9: {
    icon: new URL(
      `@/assets/images/perfectingSystem/protection-tree-icon-night.png`,
      import.meta.url
    ).href,
    className: 'protection-tree-icon-night'
  }
}
const showDialog = ref(false)
const dialogTitle = ref('')
const fileType = ref('')
const fileUrl = ref('')
const activeFileId = ref()
const protectionFileList = ref([
  {
    id: 0,
    mc: '工程产权.pdf',
    file_path: 'attachments/2025/5/gccq/1e64fb61-c312-4b61-9b50-f9053f0c617c.pdf',
    zwdlx: 'pdf'
  }
])
const FilePreviewDialogRef = ref()
function protectionTreeClick(val) {
  if (Array.isArray(val.fileList) && val.fileList.length > 0) {
    dialogTitle.value = val.label
    showDialog.value = true

    FilePreviewDialogRef.value.previewFileList(val.fileList)
    if (Array.isArray(protectionFileList.value) && protectionFileList.value.length) {
      handleClickFile(protectionFileList.value[0])
    }
  } else {
    ElMessage.warning('暂无数据')
  }
}
function handleClickFile(val) {
  activeFileId.value = val.id
  fileUrl.value = previewUrl + val.file_path
  switch (val.zwdlx) {
    case 'pdf':
      fileType.value = 'pdf'
      break
    case 'png':
    case 'jpg':
    case 'jpeg':
      fileType.value = 'pdf'
      break
    case 'doc':
    case 'docx':
      fileType.value = 'doc'
      break
    case 'xls':
    case 'xlsx':
      fileType.value = 'xls'
      break
    case 'ppt':
    case 'pptx':
      fileType.value = 'ppt'
      break
    case 'txt':
    case 'text':
      fileType.value = 'txt'
      break
  }
}

onMounted(() => {
  getManagementSubject({ resCode: userStore.userInfo?.assignWrpcdList?.[0] })
    .then(res => {
      if (res.status === 200 && res.data) {
        managementSubjectData.value = {
          ...managementSubjectData.value,
          ...res.data
        }
      }
    })
    .catch(e => {
      console.log(e)

      ElMessage.error('获取管理主体信息失败')
    })
  getOrgAndPersonInfo({ resCode: userStore.userInfo?.assignWrpcdList?.[0] })
    .then(res => {
      if (res.status === 200 && res.data) {
        orgAndPersonInfo.value = {
          ...orgAndPersonInfo.value,
          ...res.data
        }
        handleTabChange(1)
      }
    })
    .catch(e => {
      console.log(e)

      ElMessage.error('获取管理体制信息失败')
    })
  getGccqByWrpcd({ wrpcd: userStore.userInfo?.assignWrpcdList?.[0] as string }).then(res => {
    if (res.status === 200 && res.data) {
      const data = res.data
      ownershipNum.value[0].value = data.totalLandArea
      ownershipNum.value[1].value = data.propertyArea
      ownershipText.value[0].value = data.realEstateNum
      ownershipText.value[1].value = data.propertyOwner
      ownershipText.value[2].value = data.certificateDate
      ownershipText.value[3].value = data.propertyArea
      ownershipFiles.value = Array.isArray(data.cqFiles) ? data.cqFiles : []
      initOwnershipChart()
    }
  })
  getFileListByPathPrefix({ path: '四制-完善体制-专业化管护' })
    .then(res => {
      if (res.status === 200 && Array.isArray(res.data)) {
        protectionTreeList.value = res.data.map(r => {
          return {
            ...treeListIcon[r.dictCode],
            value: r.fileCount,
            label: r.endName,
            fileList: r.fileList
          }
        })
      }
    })
    .catch(e => {
      console.log(e)
      ElMessage.error('获取专业化管护信息失败')
    })

  nextTick(() => {
    setMapGeoJsonLayer.layerRender([
      {
        category: '3', //用地总范围图层分类字典值
        textColor: { r: 119, g: 255, b: 255, a: 1 },
        borderColor: { r: 243, g: 107, b: 108, a: 1 },
        labelKey: 'Name',
        text: '用地总范围',
        zoom: true
      },
      {
        category: '4', //已取得产权的范围图层分类字典值
        textColor: { r: 119, g: 255, b: 255, a: 1 },
        borderColor: { r: 243, g: 107, b: 108, a: 1 },
        labelKey: 'Name',
        pixelOffset: [10, 100],
        text: '已取得产权范围'
      }
    ])
  })
})

onBeforeUnmount(() => {
  titleChart.value?.dispose()
  setMapGeoJsonLayer.removeAllLayer()
})
</script>

<style lang="scss" scoped>
.unit-message-container {
  .unit-message-item {
    box-sizing: border-box;
    width: 100%;
    height: 38px;
    padding-left: 46px;
    margin-top: 12px;
    margin-left: 14px;
    background: url('@/assets/images/perfectingSystem/unit-message-item-bg.png') no-repeat;
    background-size: 100% 100%;

    .unit-message-item-label {
      font-size: 16px;
      color: #fff;
    }

    .unit-message-item-content {
      font-size: 16px;
      font-weight: 500;
      color: #7ff;
    }
  }
}

.system-number-statistics-item {
  .system-number-statistics-item-icon {
    width: 90px;
    height: 110px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .system-number-statistics-item-label-text {
    box-sizing: border-box;
    width: 98px;
    height: 30px;
    padding-left: 16px;
    font-size: 16px;
    font-weight: 500;
    line-height: 30px;
    background: url('@/assets/images/perfectingSystem/system-number-statistics-item-label-text.png')
      no-repeat;
    background-size: 100% 100%;
  }

  .system-number-statistics-item-label-number {
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 40px;
    text-align: center;
  }

  &.left {
    .system-number-statistics-item-icon {
      background-image: url('@/assets/images/perfectingSystem/system-number-statistics-item-icon-left.png');
    }

    .system-number-statistics-item-label-text,
    .system-number-statistics-item-label-number {
      color: #7ff;
    }
  }

  &.right {
    margin-left: 20px;

    .system-number-statistics-item-icon {
      background-image: url('@/assets/images/perfectingSystem/system-number-statistics-item-icon-right.png');
    }

    .system-number-statistics-item-label-text,
    .system-number-statistics-item-label-number {
      color: #ffd060;
    }
  }
}

.system-type-statistics-tab-item {
  height: 34px;
  font-size: 16px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  background-image: url('@/assets/images/perfectingSystem/system-type-statistics-tab-item.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  &.active {
    color: #7ff;
    background-image: url('@/assets/images/perfectingSystem/system-type-statistics-tab-item-active.png');
  }
}

.system-type-statistics-tab-pan-item {
  width: 240px;
  height: 236px;
  background-image: url('@/assets/images/perfectingSystem/pie-chart-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.system-type-statistics-tab-pan-legend {
  overflow-y: auto;
}

.system-type-statistics-tab-pan-legend-item {
  cursor: pointer;

  .icon {
    width: 15px;
    height: 10px;
    margin-top: 4px;
    margin-right: 10px;
  }

  .system-type-statistics-tab-pan-legend-name {
    font-size: 16px;
    color: #fff;
  }

  .system-type-statistics-tab-pan-legend-value {
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 30px;
    text-shadow: 0 4px 6.8px #00000040;
  }

  &-0 {
    .icon {
      background-color: #fff;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #fff;
    }
  }

  &-1 {
    .icon {
      background-color: #ffd060;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #ffd060;
    }
  }

  &-2 {
    .icon {
      background-color: #7ff;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #7ff;
    }
  }

  &-3 {
    .icon {
      background-color: #bdfc47;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #bdfc47;
    }
  }

  &-4 {
    .icon {
      background-color: #43e75c;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #43e75c;
    }
  }

  &-5 {
    .icon {
      background-color: #ff8a4b;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #ff8a4b;
    }
  }

  &-6 {
    .icon {
      background-color: #d74a2e;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #d74a2e;
    }
  }

  &-7 {
    .icon {
      background-color: #e742d1;
    }

    .system-type-statistics-tab-pan-legend-value {
      color: #e742d1;
    }
  }

  &.noSelected {
    .icon {
      background-color: #ccc;
    }
  }
}

.ownership-left {
  flex: 1;
}

.ownership-num-item {
  .ownership-num-item-label {
    font-size: 14px;
    color: #7ff;
  }

  .ownership-num-item-value {
    padding-right: 10px;
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 30px;
    color: #7ff;
    text-shadow: 0 4px 6.8px #00000040;
  }

  &:nth-child(1) {
    margin-bottom: 6px;

    .ownership-num-item-label,
    .ownership-num-item-value {
      color: #fff;
    }

    &::before {
      background-image: url('@/assets/images/perfectingSystem/ownership-num-item-before-one.png');
    }
  }

  &::before {
    width: 4px;
    height: 60px;
    margin-right: 12px;
    content: '';
    background-image: url('@/assets/images/perfectingSystem/ownership-num-item-before-two.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.ownership-right {
  align-items: center;
}

.ownership-chart {
  width: 200px;
  height: 194px;
  background-image: url('@/assets/images/perfectingSystem/ownership-chart-bg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}

.ownership-text-item {
  font-size: 14px;
  color: #fff;
}

.ownership-btn {
  width: 108px;
  height: 34px;
  font-size: 14px;
  color: #7ff;
  cursor: pointer;
  background: radial-gradient(106.99% 117.88% at 0% 0%, #09fc 0%, #0099ff52 100%);
  border: 1px solid #2fb3e9;
  border-radius: 100px;
  box-shadow: 0 0 7px 0 #00b2ff inset;

  &:hover,
  &:active {
    opacity: 0.9;
  }
}

.protection-tree {
  position: relative;
  width: 340px;
  height: 250px;
  margin: 0 auto;
  background-image: url('@/assets/images/perfectingSystem/protection-tree.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .protection-tree-item {
    position: absolute;
    width: 56px;
    height: 56px;
    cursor: pointer;
    user-select: none;

    &.protection-tree-icon-one {
      top: 17px;
      right: 148px;
      animation: jump 1.4s ease-in-out infinite;
    }

    &.protection-tree-icon-two {
      top: 42px;
      right: 35px;
      animation: jump 1.5s ease-in-out infinite;
    }

    &.protection-tree-icon-three {
      top: 118px;
      right: 115px;
      animation: jump 1.4s ease-in-out infinite;
    }

    &.protection-tree-icon-four {
      top: 76px;
      left: 22px;
      animation: jump 1.5s ease-in-out infinite;
    }

    &.protection-tree-icon-five {
      bottom: 35px;
      left: 28px;
      animation: jump 1.4s ease-in-out infinite;
    }

    &.protection-tree-icon-six {
      right: 37px;
      bottom: 35px;
      animation: jump 1.5s ease-in-out infinite;
    }

    &.protection-tree-icon-seven {
      top: 60px;
      left: 104px;
      animation: jump 1.4s ease-in-out infinite;
    }

    &.protection-tree-icon-eight {
      top: 133px;
      left: 94px;
      animation: jump 1.4s ease-in-out infinite;
    }

    &.protection-tree-icon-night {
      top: 108px;
      right: 45px;
      animation: jump 1.4s ease-in-out infinite;
    }

    &:hover {
      animation: none;
    }
  }
}

@keyframes jump {
  0%,
  100% {
    transform: scale(1, 1);
  }

  50% {
    transform: scale(1.1, 1.1);
  }
}

.protection-content {
  display: grid;
  grid-template-columns: repeat(3, 128px);
  gap: 12px 27px;
  margin-top: 10px;
  overflow-y: auto;
}

.protection-content-item {
  box-sizing: border-box;
  width: 120px;
  height: 64px;
  padding: 4px 6px 4px 16px;
  cursor: pointer;

  // background-image: url('@/assets/images/perfectingSystem/system-type-statistics-tab-item.png');
  background-image: url('@/assets/images/perfectingSystem/protection-content-item-num-bg.png');
  background-repeat: no-repeat;
  background-position: 0 -4px;
  background-size: 100% 120%;

  &::after {
    width: 0;
    height: 0;
    margin-top: 4px;
    margin-left: auto;
    content: '';
    border-top: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 6px solid rgb(255 255 255 / 50%);
  }

  .protection-content-item-title {
    font-size: 14px;
    color: #fff;
  }

  .protection-content-item-num {
    font-family: YouSheBiaoTiHei, sans-serif;
    font-size: 30px;
    text-shadow: 0 4px 6.8px #00000040;
  }

  &.protection-tree-icon-one {
    .protection-content-item-num {
      color: #7ff;
    }
  }

  &.protection-tree-icon-two {
    .protection-content-item-num {
      color: #ffd060;
    }
  }

  &.protection-tree-icon-three {
    .protection-content-item-num {
      color: #42e75b;
    }
  }

  &.protection-tree-icon-four {
    .protection-content-item-num {
      color: #ff8a4b;
    }
  }

  &.protection-tree-icon-five {
    .protection-content-item-num {
      color: #52b1ff;
    }
  }

  &.protection-tree-icon-six {
    .protection-content-item-num {
      color: #bdfc46;
    }
  }

  &.protection-tree-icon-seven {
    .protection-content-item-num {
      color: #2fb3e9;
    }
  }

  &.protection-tree-icon-eight {
    .protection-content-item-num {
      color: #52b1ff;
    }
  }

  &.protection-tree-icon-night {
    .protection-content-item-num {
      color: #bdfc46;
    }
  }
}

.protection-file-list {
  width: 200px;
}

.protection-box {
  position: relative;
  width: 387px;
  height: 387px;
  margin: 36px 0 0 30px;
  background-image: url('@/assets/images/perfectingSystem/protection-box.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .protection-item {
    position: absolute;
    align-items: center;
    justify-content: center;

    .protection-item-text {
      font-size: 16px;
    }

    .protection-item-value {
      display: flex;
      align-items: center;
      justify-content: center;
      font-family: YouSheBiaoTiHei, sans-serif;
      font-size: 30px;
    }

    &.protection-item-0 {
      top: 0;
      left: 146px;

      .protection-item-value {
        width: 60px;
        height: 60px;
      }
    }

    &.protection-item-1 {
      top: 120px;
      left: 18px;

      .protection-item-value {
        width: 60px;
        height: 60px;
      }
    }

    &.protection-item-2 {
      top: 120px;
      right: 20px;

      .protection-item-value {
        width: 60px;
        height: 60px;
      }
    }

    &.protection-item-3 {
      bottom: 0;
      left: 80px;

      .protection-item-value {
        width: 60px;
        height: 60px;
      }
    }

    &.protection-item-4 {
      top: 2px;
      left: 2px;

      .protection-item-value {
        width: 50px;
        height: 50px;
      }
    }

    &.protection-item-5 {
      top: 4px;
      right: -9px;

      .protection-item-value {
        width: 50px;
        height: 50px;
      }
    }

    &.protection-item-6 {
      bottom: 82px;
      left: -8px;

      .protection-item-value {
        width: 50px;
        height: 50px;
      }
    }

    &.protection-item-7 {
      right: 83px;
      bottom: 0;

      .protection-item-value {
        width: 60px;
        height: 60px;
      }
    }

    &.protection-item-8 {
      right: -20px;
      bottom: 80px;

      .protection-item-value {
        width: 50px;
        height: 50px;
      }
    }
  }
}
</style>
