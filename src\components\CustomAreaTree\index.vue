<template>
  <div class="area">
    <div
      class="area-container"
      :class="{ 'area-container_collapse': isCollapse }"
    >
      <el-input
        v-if="showFilter"
        v-model="filterText"
        class="filter-input pb20"
        placeholder="输入名称搜索"
        prefix-icon="el-icon-search"
      />
      <el-tree
        class="c-el-tree flx-1"
        ref="treeRef"
        highlight-current
        :node-key="nodeKey"
        :data="treeData"
        :props="treeProps"
        :default-expanded-keys="[defaultKey]"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        v-bind="$attrs"
        @node-click="treeNodeClick"
      >
      </el-tree>
      <i
        class="el-icon-d-arrow-left collapse-btn"
        @click="isCollapse = !isCollapse"
      ></i>
    </div>

    <div class="main-container pl20">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: "CustomAreaTree",
  props: {
    showFilter: {
      type: Boolean,
      default: true,
    },
    nodeKey: {
      type: String,
      required: true,
    },
    treeData: {
      type: Array,
      required: true,
    },
    treeProps: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      isCollapse: false,
      filterText: "",
    };
  },
  computed: {
    defaultKey() {
      return this.treeData[0] ? this.treeData[0][this.nodeKey] : null;
    },
  },
  watch: {
    filterText(val) {
      this.$refs.treeRef && this.$refs.treeRef.filter(val);
    },
    defaultKey: {
      handler(newVal) {
        if (newVal != null) {
          this.treeNodeClick(this.treeData[0]);
          this.$nextTick(() => {
            this.$refs.treeRef && this.$refs.treeRef.setCurrentKey(newVal);
          });
        }
      },
      immediate: true,
    },
  },
  methods: {
    treeNodeClick(data) {
      this.$emit("nodeClick", data);
    },
    filterNode(value, data, node) {
      if (!value) return true;
      return node.label.includes(value);
    },
  },
};
</script>

<style lang="scss" scoped>
.area {
  display: flex;
  width: 100%;
  height: 100%;
  .area-container {
    position: relative;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    width: 300px;
    height: 100%;
    border-right: 1px solid #e6e6e6;
    transition: all 1s ease;
    :deep(.c-el-tree) {
      .el-tree-node__content {
        height: 35px;
      }
    }
    .filter-input {
      width: 300px;
      overflow: hidden;
      transition: all 1s ease;
      display: flex;
      flex-direction: row;
      align-items: center;
      :deep(.el-input__prefix) {
        top: auto;
      }
    }
    .collapse-btn {
      position: absolute;
      top: 6px;
      right: 0;
      font-size: 20px;
      color: #409eff;
      cursor: pointer;
    }
    .c-el-tree {
      overflow: auto;
    }
    &.area-container_collapse {
      width: 0;
      border-color: transparent;
      .filter-input {
        width: 0;
      }
      .collapse-btn {
        transform: rotate(180deg);
      }
    }
  }
  .main-container {
    flex: 1;
    height: 100%;
    overflow: hidden;
  }
}

.pb20 {
  padding-bottom: 20px;
}

.pl20 {
  padding-left: 20px;
}

.flx-1 {
  flex: 1;
}
</style>
