/* 基础管理-矩阵展示 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  delete: apiUrl.defaultUrl + '/matrixDisplay/delete',
  list: apiUrl.defaultUrl + '/matrixDisplay/pageList',
  add: apiUrl.defaultUrl + '/matrixDisplay/add',
  update: apiUrl.defaultUrl + '/matrixDisplay/update'
}

/**
 * 分页查询
 * @param {object} data
 * @param {string} data.title
 * @param {string} data.tmStart
 * @param {string} data.tmEnd
 * @param {string} data.displayCategory
 */
export function list (data) {
  const copyData = JSON.parse(JSON.stringify(data))
  delete copyData.pageNum
  delete copyData.pageSize
  return $http.post(api.list + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, copyData)
}

/**
 * 新增
 * @param {object} data
 * @param {string} data.wrpcd
 * @param {string} data.title
 * @param {string} data.tm
 * @param {string} data.displayCategory
 * @param {string} data.imageFileId
 * @param {string} data.displayContent
 * @param {string} data.docClassifyId
 */
export function add (data) {
  return $http.post(api.add, data)
}

/**
 * 更新
 * @param {object} data
 * @param {string} data.id
 * @param {string} data.wrpcd
 * @param {string} data.title
 * @param {string} data.tm
 * @param {string} data.displayCategory
 * @param {string} data.imageFileId
 * @param {string} data.displayContent
 * @param {string} data.docClassifyId
 */
export function update (data) {
  return $http.post(api.update, data)
}

/**
 * 删除
 * @param {string} id
*/
export function deleteData (id) {
  return $http.get(api.delete + '?id=' + id)
}

export default {
  list,
  add,
  update,
  deleteData
}
