/* 基础管理-工程状况-基本信息 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  deleteDb: apiUrl.defaultUrl + '/reservoirProjectData/deleteDb',
  deleteFaya: apiUrl.defaultUrl + '/reservoirProjectData/deleteFaya',
  deleteYhd: apiUrl.defaultUrl + '/reservoirProjectData/deleteYhd',
  deleteZrr: apiUrl.defaultUrl + '/reservoirProjectData/deleteZrr',
  getAqjdList: apiUrl.defaultUrl + '/reservoirProjectData/getAqjdList',
  getBaseInfoList: apiUrl.defaultUrl + '/reservoirProjectData/getBaseInfoList',
  getDbList: apiUrl.defaultUrl + '/reservoirProjectData/getDbList',
  getFayaList: apiUrl.defaultUrl + '/reservoirProjectData/getFayaList',
  getGcxyList: apiUrl.defaultUrl + '/reservoirProjectData/getGcxyList',
  getGlqkList: apiUrl.defaultUrl + '/reservoirProjectData/getGlqkList',
  getSwtzList: apiUrl.defaultUrl + '/reservoirProjectData/getSwtzList',
  getYhdList: apiUrl.defaultUrl + '/reservoirProjectData/getYhdList',
  getZcdjList: apiUrl.defaultUrl + '/reservoirProjectData/getZcdjList',
  getZrrList: apiUrl.defaultUrl + '/reservoirProjectData/getZrrList',
  saveAqjd: apiUrl.defaultUrl + '/reservoirProjectData/saveAqjd',
  saveBaseInfo: apiUrl.defaultUrl + '/reservoirProjectData/saveBaseInfo',
  saveDb: apiUrl.defaultUrl + '/reservoirProjectData/saveDb',
  saveFaya: apiUrl.defaultUrl + '/reservoirProjectData/saveFaya',
  saveGcxy: apiUrl.defaultUrl + '/reservoirProjectData/saveGcxy',
  saveGlqk: apiUrl.defaultUrl + '/reservoirProjectData/saveGlqk',
  saveSwtzInfo: apiUrl.defaultUrl + '/reservoirProjectData/saveSwtzInfo',
  saveYhd: apiUrl.defaultUrl + '/reservoirProjectData/saveYhd',
  saveZcdj: apiUrl.defaultUrl + '/reservoirProjectData/saveZcdj',
  saveZrr: apiUrl.defaultUrl + '/reservoirProjectData/saveZrr',
  getXyyxList: apiUrl.defaultUrl + '/reservoirProjectData/getXyyxList',
  saveXyyx: apiUrl.defaultUrl + '/reservoirProjectData/saveXyyx',
  deleteSsd: apiUrl.defaultUrl + '/reservoirProjectData/deleteSsd',
  getSsdList: apiUrl.defaultUrl + '/reservoirProjectData/getSsdList',
  saveSsd: apiUrl.defaultUrl + '/reservoirProjectData/saveSsd',

  getGccqList: apiUrl.defaultUrl + '/reservoirProjectData/getGccqList',
  saveGccq: apiUrl.defaultUrl + '/reservoirProjectData/saveGccq',

  getCurveList: apiUrl.defaultUrl + '/reservoirProjectData/getCurveList',
  saveCurve: apiUrl.defaultUrl + '/reservoirProjectData/saveCurve',
  deleteCurve: apiUrl.defaultUrl + '/reservoirProjectData/deleteCurve',

  getDocClassify: apiUrl.defaultUrl + '/moreProject/getDocClassify',

}

/**
 * 基础管理-基本信息-工程产权信息
 * "wrpcd": 工程代码
 */
export function getGccqList (data) {
  return $http.post(api.getGccqList, data)
}

/**
 * 基础管理-基本信息-工程产权信息
 */
export function saveGccq (data) {
  return $http.post(api.saveGccq, data)
}

/**
 * 基础管理-工程状况-大坝删除
 * id
 */
export function deleteDb (data) {
  return $http.post(api.deleteDb, data)
}

/**
 * 基础管理-工程状况-方案预案删除
 * id
 */
export function deleteFaya (data) {
  return $http.post(api.deleteFaya, data)
}

/**
 * 基础管理-工程状况-溢洪道删除
 * id
 */
export function deleteYhd (data) {
  return $http.post(api.deleteYhd, data)
}

/**
 * 输水洞列表接口
 */
export function getSsdList (data) {
  return $http.post(api.getSsdList, data)
}

/**
 * 输水洞编辑接口
 */
export function saveSsd (data) {
  return $http.post(api.saveSsd, data)
}

/**
 * 输水洞删除接口
 */
export function deleteSsd (data) {
  return $http.post(api.deleteSsd, data)
}

/**
 * 基础管理-工程状况-责任人删除
 * id
 */
export function deleteZrr (data) {
  return $http.post(api.deleteZrr, data)
}

/**
 * 下游影响接口
 */
export function getXyyxList (data) {
  return $http.post(api.getXyyxList, data)
}

/**
 * 下游影响编辑接口
 */
export function saveXyyx (data) {
  return $http.post(api.saveXyyx, data)
}

/**
 * 基础管理-工程状况-获取安全鉴定列表
 * "projectId": 项目ID
 */
export function getAqjdList (data) {
  return $http.post(api.getAqjdList, data)
}

/**
 * 基础管理-工程状况-获取基本信息列表
 * "projectId": 项目ID
 */
export function getBaseInfoList (data) {
  return $http.post(api.getBaseInfoList, data)
}

/**
 * 基础管理-工程状况-获取大坝列表列表
 * "projectId": 项目ID
 */
export function getDbList (data) {
  return $http.post(api.getDbList, data)
}

/**
 * 基础管理-工程状况-获取方案预案分页列表
 * "pageNum": 0,
 * "pageSize": 0,
 * "type": 0
 */
export function getFayaList (data) {
  return $http.post(api.getFayaList, data)
}

/**
 * 基础管理-工程状况-获取工程效益列表
 * "projectId": 项目ID
 */
export function getGcxyList (data) {
  return $http.post(api.getGcxyList, data)
}

/**
 * 基础管理-工程状况-获取管理情况列表
 * "projectId": 项目ID
 */
export function getGlqkList (data) {
  return $http.post(api.getGlqkList, data)
}

/**
 * 基础管理-工程状况-获取水文特征列表
 * "projectId": 项目ID
 */
export function getSwtzList (data) {
  return $http.post(api.getSwtzList, data)
}

/**
 * 基础管理-工程状况-获取溢洪道列表
 * "projectId": 项目ID
 */
export function getYhdList (data) {
  return $http.post(api.getYhdList, data)
}

/**
 * 基础管理-工程状况-获取注册登记列表
 * "projectId": 项目ID
 */
export function getZcdjList (data) {
  return $http.post(api.getZcdjList, data)
}

/**
 * 基础管理-工程状况-获取责任人列表列表
 * "projectId": 项目ID
 */
export function getZrrList (data) {
  return $http.post(api.getZrrList, data)
}

/**
 * 基础管理-工程状况-安全鉴定编辑
 * "aqpjdw": "string", // 安全评价单位
 * "aqpjdwzz": "string", // 安全评价单位资质
 * "dbaqlb": 0, // 大坝安全类别 int
 * "fhaqjdyj": 0, // 防洪安全鉴定意见 int
 * "id": 0,
 * "jdgcczdzywt": "string", // 鉴定工程存在的主要问题
 * "jdrq": "string", // 鉴定日期
 * "jdzzdw": "string", // 鉴定组织单位
 * "jgaqjdyj": 0, // 结构安全鉴定意见 int
 * "jsjgaqjdyj": 0, // 金属结构安全鉴定意见 int
 * "kzaqjdyj": 0, // 抗震安全鉴定意见 int
 * "projectId": "string", // 工程id
 * "qtgcwt": "string", // 其他工程问题
 * "sdbm": "string", // 审定部门
 * "sdbmjb": 0, // 审定部门级别 int
 * "slaqjdyj": 0 // 渗流安全鉴定意见 int
 */
export function saveAqjd (data) {
  return $http.post(api.saveAqjd, data)
}

/**
 * 基础管理-工程状况-基本信息编辑
 * @param {object} data
 * "bm": "string", // 别名/曾用名
 * "dzdfzjsd": 0, // 地震动峰值加速度 int
 * "dzjbld": 0, // 地震基本烈度 int
 * "gcdb": 0, // 工程等别 int
 * "gcgm": 0, // 工程规模 int
 * "gczt": 0, // 工程状态 int
 * "gldw": "string", // 管理单位 int
 * "gsbm": 0, // 归属部门 int
 * "id": 0,
 * "jcnf": "string", // 建成年份
 * "lgtd": "string", // 经度
 * "lttd": "string", // 纬度
 * "projectId": "string", // 工程id
 * "sfdzld": 0, // 设防地震烈度 int
 * "skdm": "string", // 水库代码
 * "skmc": "string", // 水库名称
 * "szhl": "string", // 所在河流
 * "szjm": 0, // 水准基面
 * "szly": "string", // 所在流域
 * "szxz": "string", // 所在乡镇
 * "szxzqh": "string", // 所在行政区划
 * "xmbz": "string", // 项目备注
 * "zcdjyf": 0, // 注册登记与否
 * "zgdw": "string", // 主管单位
 * "zyjzwjb": 0 // 主要建筑物级别
 */
export function saveBaseInfo (data) {
  return $http.post(api.saveBaseInfo, data)
}

/**
 * 基础管理-工程状况-大坝编辑
 * @param {object} data
 * "bdcd": "string", // 坝顶长度
 * "bdgc": "string", // 坝顶高程
 * "bdkd": "string", // 坝顶宽度
 * "bjdztj": "string", // 坝基地质条件
 * "bjfscs": 0, // 坝基防渗措施 int
 * "bx": 0, // 坝型 int
 * "dbmc": "string", // 大坝名称
 * "flqdgc": "string", // 防浪墙顶高程
 * "fstdgc": "string", // 防渗体顶高程
 * "fstxs": 0, // 防渗体形式 int
 * "id": 0,
 * "projectId": "string", // 工程id
 * "pstxs": 0, // 排水体型式 int
 * "sfylb": 0, // 是否溢流坝 0否 1是 int
 * "zdbg": "string" // 最大坝高
 */
export function saveDb (data) {
  return $http.post(api.saveDb, data)
}

/**
 * 基础管理-工程状况-方案预案编辑
 * @param {object} data
 * "bzdw": "string", // 编制单位
 * "createdTime": "string", // 创建时间
 * "fbrq": "string", // 发布日期
 * "id": 0,
 * "lxdh": "string", // 联系电话
 * "spbm": "string", // 审批部门
 * "sprq": "string", // 审批日期
 * "spwh": "string", // 审批文号
 * "type": 0, // 1 汛期调度运行计划 2 调度运用方案 3 大坝安全管理应急方案 4 防汛抢险应急预案
 * "updatedTime": "string", // 更新时间
 * "xzpfwjId": "string", // 批复文件ID
 * "xzxqddyxjhId": "string", // 汛期调度运行计划文件ID
 * "yxrq": "string", // 有效日期
 * "zhz": "string" // 指挥长
 */
export function saveFaya (data) {
  return $http.post(api.saveFaya, data)
}

/**
 * 基础管理-工程状况-工程效益编辑
 * @param {object} data
 * "cytjgs": "string", // 船运条件改善
 * "czdnpjgsl": "string", // 城镇多年平均供水量
 * "dmpjgsl": "string", // 多面平均供水量
 * "dnpjfdl": "string", // 多年平均发电量
 * "fhbhgd": "string", // 防洪保护耕地
 * "fhbhgl": "string", // 防洪保护公路
 * "fhbhmj": "string", // 防洪保护面积
 * "fhbhrk": "string", // 防洪保护人口
 * "fhbhtl": "string", // 防洪保护铁路
 * "fhbhxyscz": "string", // 防洪保护县以上城镇
 * "fhbhxz": "string", // 防洪保护乡镇
 * "gydnpjgsl": "string", // 工业多年平均供水量
 * "hdaqxl": "string", // 河道安全泄量
 * "hjxy": "string", // 环境效益
 * "id": 0,
 * "projectId": "string", // 工程id
 * "rxdnpjgsl": "string", // 人蓄多年平均供水量
 * "sjggmj": "string", // 设计灌溉面积
 * "sjngsl": "string", // 设计年供水量
 * "slfjqjb": 0, // 水利风景区级别 int
 * "yxggmj": "string", // 有效灌溉面积
 * "zdsgmj": "string", // 最大实灌面积
 * "zzjrl": "string" // 总装机容量
 */
export function saveGcxy (data) {
  return $http.post(api.saveGcxy, data)
}

/**
 * 基础管理-工程状况-管理情况编辑
 * @param {object} data
 * "gldwgcsysrs": "string", // 管理单位工程师以上人数
 * "gldwgjgcsysrs": "string", // 管理单位高级工程师以上人数
 * "gldwzgrs": "string", // 管理单位职工人数
 * "gslyjg": 0, // 归属流域机构 int
 * "id": 0,
 * "projectId": "string", // 工程id
 * "sdzf": "string" // 属地政府
 */
export function saveGlqk (data) {
  return $http.post(api.saveGlqk, data)
}

/**
 * 基础管理-工程状况-水文特征编辑
 * @param {object} data
 * "dhkr": "string", // 调洪库容
 * "dnpjjll": "string", // 多年平均径流量
 * "dnpjjsl": "string", // 多年平均降水量
 * "dnpjssl": "string", // 多年平均输沙量
 * "dnpjzdqw": "string", // 多年平均最低气温
 * "fhgsw": "string", // 防洪高水位
 * "fhkr": "string", // 防洪库容
 * "hdbj": "string", // 河道比降
 * "hdcd": "string", // 河道长度
 * "id": 0,
 * "jhcxq": "string", // 校核重现期
 * "jhhfll": "string", // 校核洪峰流量
 * "jhhsls": "string", // 校核洪水历时
 * "jhhsw": "string", // 校核洪水位
 * "jhhszl": "string", // 校核洪水总量
 * "jymj": "string", // 集雨面积
 * "lszgsw": "string", // 历史最高水位
 * "projectId": "string", // 工程id
 * "sjcxq": "string", // 设计重现期
 * "sjhfll": "string", // 设计洪峰流量
 * "sjhsw": "string", // 设计洪水位
 * "sjsrhszl": "string", // 设计三日洪水总量
 * "sjyrhszl": "string", // 设计一日洪水总量
 * "skr": "string", // 死库容
 * "sktjxn": 0, // 水库调节性能 int
 * "ssw": "string", // 死水位
 * "xlkr": "string", // 兴利库容
 * "ytksw": "string", // 预腾空水位
 * "zcxsw": "string", // 正常蓄水位
 * "zkr": "string" // 总库容
 */
export function saveSwtz (data) {
  return $http.post(api.saveSwtzInfo, data)
}

/**
 * 基础管理-工程状况-溢洪道编辑
 * @param {object} data
 * "bzwz": "string", // 布置位置
 * "dytj": "string", // 电源条件
 * "id": 0,
 * "kzfs": 0, // 控制方式 int
 * "projectId": "string", // 工程id
 * "qbjsl": "string", // 启闭机数量
 * "qbsb": 0, // 启闭设备 int
 * "xnxs": 0, // 消能型式
 * "ydgc": "string", // 堰顶高程
 * "ydjk": "string", // 堰顶净宽
 * "yhdmc": "string", // 溢洪道名称
 * "ylyxs": 0, // 溢流堰型式 int
 * "zdxl": "string", // 最大泄量
 * "zmcc": "string", // 闸门尺寸
 * "zmsl": "string", // 闸门数量
 * "zmxs": 0 // 闸门型式 int
 */
export function saveYhd (data) {
  return $http.post(api.saveYhd, data)
}

/**
 * 基础管理-工程状况-注册登记编辑
 * @param {object} data
 * "fzbm": "string", // 发证部门
 * "id": 0,
 * "projectId": "string", // 工程id
 * "zcdjh": "string", // 注册登记号
 * "zcdjjg": "string", // 注册登记机构
 * "zcdjrq": "string", // 注册登记日期
 * "zcdjyxq": "string" // 注册登记有效期
 */
export function saveZcdj (data) {
  return $http.post(api.saveZcdj, data)
}

/**
 * 基础管理-工程状况-责任人编辑
 * @param {object} data
 * "czhm": "string", // 传真号码
 * "dh": "string", // 电话
 * "dw": "string", // 单位
 * "dz": "string", // 地址
 * "id": 0,
 * "projectId": "string", // 工程ID
 * "xm": "string", // 责任人姓名
 * "zrrlx": "string", // 责任人类型
 * "zw": "string" // 职务
 */
export function saveZrr (data) {
  return $http.post(api.saveZrr, data)
}

/**
 * 水文特征-库容曲线列表
 * @param {object} data
 * "wrpcd": "string" // 工程ID
 */
export function getCurveList (data) {
  return $http.get(api.getCurveList, data)
}

export function saveCurve (data) {
  return $http.post(api.saveCurve, data)
}

export function deleteCurve (data) {
  return $http.post(api.deleteCurve, data)
}

/**
 * 根据工程code获取工程的档案数据分类列表
 * @param {object} data
 * "wrpcd": "string" // 工程ID
 */
export function getDocClassify (data) {
  return $http.get(api.getDocClassify, data)
}
