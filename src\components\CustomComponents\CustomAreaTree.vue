<template>
  <!-- 行政区划组件，左边行政区划，右边内容 -->
  <div class="area flex">
    <!-- 行政区划树 -->
    <div
      class="area-container"
      :class="{ 'area-container-collapse': isCollapse }"
    >
      <el-input
        class="filter-input pb20"
        prefix-icon="el-icon-search"
        placeholder="输入名称搜索"
        v-model="filterText"
      ></el-input>
      <el-tree
        class="filter-tree"
        ref="tree"
        node-key="id"
        highlight-current
        :data="areaData"
        :props="defaultProps"
        :default-expanded-keys="defaultKey"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        @node-click="treeNodeClick"
      >
        <template slot-scope="{ node, data }">
          <div class="custom-node">
            <span class="custom-icon">
              <i v-if="!data.childList || !data.childList.length" class=""></i>
              <i v-else-if="node.expanded" class="el-icon-minus"></i>
              <i v-else class="el-icon-plus"></i>
            </span>
            <el-tooltip
              :content="node.label"
              :disabled="!tooltipFlag"
              placement="top"
            >
              <div
                class="custom-label"
                :class="{ 'custom-label--active': activeArea === node.key }"
                @mouseenter="tooltipEvent($event)"
              >
                {{ node.label }}
              </div>
            </el-tooltip>
            <span v-if="data.projectNum != null">({{ data.projectNum }})</span>
          </div>
        </template>
      </el-tree>
      <img
        class="collapse-btn"
        src="@/assets/score/icon-collapse.png"
        @click="() => (isCollapse = !isCollapse)"
      />
    </div>

    <div class="main-container flex-1 pl20">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import { getStorage } from '@/utils/storage'
import { previewUrl } from '@/api/attachment'
import { areaTree } from '@/api/OperationMonitoring/waterAndRain'
export default {
  name: 'CustomAreaTree',
  props: {
    // 行政区划树是否显示工程
    showProject: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      isCollapse: false,
      filterText: '',
      defaultProps: {
        children: 'childList',
        label: 'label'
      },
      defaultKey: [],
      activeArea: null,
      areaData: [],
      tooltipFlag: false // 控制是否显示树列表过长时显示
    }
  },
  computed: {
    projectInfo () {
      return (
        getStorage('std-projectInfo')
      )
    },
    projectType () {
      return this.projectInfo?.projectType
    }
  },
  watch: {
    projectType: {
      handler (newVal, oldVal) {
        if (newVal && newVal !== oldVal) {
          this.loadAreaData()
        }
      },
      immediate: true
    },
    filterText (val) {
      this.$refs.tree.filter(val)
    }
  },
  created () {},
  methods: {
    ...mapMutations('project', {
      setProjectInfo: 'SET_PROJECT_INFO'
    }),
    tooltipEvent (event) {
      const ev = event.target
      this.tooltipFlag = ev.scrollWidth > ev.clientWidth
    },
    filterNode (value, data) {
      if (!value) return true
      return data.label.includes(value)
    },
    // 加载行政区划数据
    loadAreaData () {
      areaTree({ projectType: this.projectType }).then((res) => {
        if (res.status === 200 && res.data) {
          this.areaData = this.processData(res.data)

          if (this.showProject) {
            this.activeArea = this.projectInfo.id
          } else {
            this.activeArea = this.areaData[0]?.areaCode
            this.$emit('node-click', this.areaData[0] || {})
          }

          this.defaultKey = [this.activeArea]
          this.$refs.tree.setCurrentKey(this.activeArea)
        }
      })
    },
    processData (list) {
      return list.reduce((acc, item) => {
        let childList = this.processData(item.childList)
        if (this.showProject && item.scoreProjectList?.length) {
          const projectList = item.scoreProjectList.map((p) => {
            const obj = {
              ...p,
              areaName: item.areaName,
              label: p.projectName,
              projectPhoto: p.file ? previewUrl + p.file.file_path : null,
              childList: []
            }

            if (this.projectInfo.id === p.id) {
              this.$emit('node-click', obj)
            }

            return obj
          })

          // childList = projectList;
          childList = [...projectList, ...childList]
        }

        acc.push({
          ...item,
          id: item.areaCode,
          label: item.areaName,
          childList
        })

        return acc
      }, [])
    },
    // 树点击事件
    treeNodeClick (data) {
      if (data.appKey) {
        this.setProjectInfo(data)
      }
      this.activeArea = data.id
      this.$emit('node-click', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.area {
  height: 100%;
  width: 100%;
  .area-container {
    box-sizing: border-box;
    height: 100%;
    width: 230px;
    // margin-right: 20px;
    position: relative;
    border-right: 1px solid #e5e9f0;
    transition: all 1s ease;
    display: flex;
    flex-direction: column;
    .filter-input {
      width: 200px;
      overflow: hidden;
      transition: all 1s ease;
      ::v-deep(.el-input__prefix) {
        height: 48px;
      }
    }
    .el-tree {
      height: 100%;
      overflow: auto;

      .custom-node {
        // padding-right: 4px;
        font-size: 18px;
        font-weight: 500;
        color: #222f40;
        line-height: 42px;
        display: flex;
        align-items: center;
        overflow: hidden;
        .custom-icon i {
          margin-right: 28px;
        }
        .custom-icon i[class*="el-icon-"] {
          display: inline-block;
          width: 16px;
          height: 16px;
          color: #1064da;
          border: 1px solid #1064da;
          margin-right: 12px;
          text-align: center;
          line-height: 16px;
          font-size: 12px;
        }
        .custom-label {
          @include ellipsisText;
        }
        .custom-label--active {
          color: #0a4ead;
        }
      }
    }
    ::v-deep(.el-tree-node__content) {
      height: auto !important;
    }
    ::v-deep(.el-tree-node__expand-icon) {
      opacity: 0;
      position: absolute;
      width: 18px;
      height: 18px;
      box-sizing: border-box;
      padding: 0;
    }
    ::v-deep(.el-tree-node.is-current > .el-tree-node__content) {
      background-color: transparent;
    }
    .el-tree-node.is-current > .el-tree-node__content .custom-node {
      color: #0a4ead;
    }
    .collapse-btn {
      width: 16px;
      height: 22px;
      position: absolute;
      top: 0;
      right: 0;
      cursor: pointer;
    }

    &.area-container-collapse {
      width: 0 !important;
      border-color: transparent;
      .collapse-btn {
        transform: rotate(180deg);
      }

      .filter-input {
        width: 0;
      }
    }
  }
  .main-container {
    height: 100%;
    overflow: hidden;
  }
}
</style>
