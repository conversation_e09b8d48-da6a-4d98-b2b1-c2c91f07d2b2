<template>
  <base-dialog
    width="80vw"
    append-to-body
    :show-dialog="visible"
    @cancel="handleCancel"
    @confirm="handleConfirm"
    :title="currentPlan?.id ? '编辑预报方案' : '新增预报方案'"
    :btn-loading="btnLoading"
    lock-scroll
    top="0"
    align-center
  >
    <div class="detail-dialog">
      <div class="basic-info">
        <div class="section-title">基本信息</div>
        <el-form :model="formData" label-width="100px" class="form">
          <el-form-item label="预报时段:">
            <div class="time-range">{{ formData.beginTime }} 至 {{ formData.endTime }}</div>
          </el-form-item>
          <el-form-item label="方案名称:">
            <el-input
              v-model="formData.forecastName"
              placeholder="请输入方案名称"
              :maxlength="50"
            />
          </el-form-item>
          <el-form-item label="备注:">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
              :maxlength="200"
              style="--el-input-bg-color: transparent"
            />
          </el-form-item>
        </el-form>
      </div>

      <div class="forecast-setting">
        <div class="section-title">预报场景设置</div>
        <div class="setting-desc">
          未来降雨默认值为该预报时段的滚动预报值，未来水库出库流量默认值为模型滚动计算的规则调度出库流量值
        </div>
        <div class="section-title">快速编辑</div>
        <div class="rain-info">
          <!--  -->
          <div class="fe-row">
            <div class="t-input">
              <span class="label">编辑时间段</span>
              <el-date-picker
                class="dp"
                v-model="fastEditModel.range"
                type="datetimerange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                :disabled-date="disabledRangeDate"
                :disabled-minutes="disabeldMinutes"
                time-format="HH:00"
                format="MM-DD HH时"
                value-format="MM-DD HH时"
              />
            </div>
            <div class="t-input">
              <span class="label">编辑目标</span>
              <el-segmented
                class="sub-segmented c-el-segmented"
                v-model="fastEditModel.editType"
                :options="[
                  { label: '降雨量', value: 'rain' },
                  { label: '水库出库流量', value: 'outQ' }
                ]"
                @change="handleEditTypeChange"
              />
            </div>
            <div class="t-input" v-show="fastEditModel.editType === 'outQ'">
              <span class="label">水库</span>
              <el-select
                class="rt-select"
                v-model="fastEditModel.rTarget"
                multiple
                placeholder="所选时间段编辑的水库"
              >
                <el-option
                  v-for="item in rrTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>

            <div class="t-input" v-show="fastEditModel.editType === 'outQ'">
              <span class="label">单水库总出库流量</span>
              <div class="flx-align-center gap10">
                <el-input
                  class="val-ipt"
                  v-model="fastEditModel.totalOutQ"
                  clearable
                  placeholder="所选时间段总出库流量"
                  style="--el-input-bg-color: transparent; --el-text-color-regular: #fff"
                ></el-input>
                <span class="unit">m³/s</span>
              </div>
            </div>

            <div class="t-input" v-show="fastEditModel.editType === 'rain'">
              <span class="label">总雨量</span>
              <div class="flx-align-center gap10">
                <el-input
                  class="val-ipt"
                  v-model="fastEditModel.totalRain"
                  clearable
                  placeholder="所选时间段的总雨量"
                  style="--el-input-bg-color: transparent; --el-text-color-regular: #fff"
                ></el-input>
                <span class="unit">mm</span>
              </div>
            </div>

            <div class="t-input">
              <span class="label">数值分布方式</span>
              <el-segmented
                class="sub-segmented c-el-segmented"
                v-model="fastEditModel.valueType"
                :options="valueTypes"
                :key="valueTypeComponentKey"
                @change="handleValueTypeChange"
              />
            </div>
          </div>
          <base-button @click="resetDataVoMap">恢复默认</base-button>
        </div>
        <el-table
          :data="detailData.forecastMainFutureDataVoMap"
          style="width: 100%"
          height="400px"
          class="forecast-table c-blue-el-table"
          border
          show-summary
        >
          <el-table-column prop="futureDateStr" label="未来时间" width="120">
            <!-- <template #default="{ row }">
                <el-input v-model="row.futureDateStr"></el-input>
              </template> -->
            <template #default="{ row }">
              {{ row.futureDateStr }}
            </template>
          </el-table-column>
          <el-table-column prop="futureRainfall" label="未来降雨量" width="120">
            <template #default="{ row }">
              <el-input
                v-model.number="row.futureRainfall"
                clearable
                style="--el-input-bg-color: transparent; --el-text-color-regular: #fff"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            v-for="(value, key) in detailData.stnm"
            :key="key"
            :label="value"
            :prop="key"
          >
            <template #default="{ row }">
              <el-input
                v-model.number="row[key]"
                style="--el-input-bg-color: transparent; --el-text-color-regular: #fff"
                clearable
              ></el-input>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </base-dialog>
</template>

<script lang="ts" setup>
import { useWatcher } from 'alova/client'
import { getForecastDetailForEdit, saveDiyForecastMain } from '@/alova_api/methods/forecast'
import { ForecastPlanDetail } from '@/alova_api/type'
import { generateTimeRangeFromStrings, generateTimeRangeStrings } from '@/utils/date'
import dayjs from 'dayjs'
import { generateDistribution } from './gen'
import { deepMerge } from '@/utils'

const valueTypes = [...(['逐渐增加', '逐渐减小', '平均分布'] as const)]

// 接收预报ID，如果为undefined则为新增模式
const props = defineProps({
  currentPlan: {
    type: Object as PropType<Partial<ForecastPlanDetail>>,
    default: () => {}
  },
  addBaseForecastId: {
    type: [Number, String],
    default: undefined
  }
})

const visible = defineModel<boolean>({
  default: false
})

const emit = defineEmits(['success'])

// 表单数据，初始为默认值
const formData = ref<Partial<ForecastPlanDetail>>({
  beginTime: '',
  endTime: '',
  forecastName: '',
  remark: '',
  ...props.currentPlan
})

const fastEditModel = reactive<{
  range: [Date, Date] | [string, string] | [] //时间范围
  totalRain?: number //总雨量
  rTarget: string[] // 水库目标
  totalOutQ?: number //总出库流量
  valueType?: string //值分布方式
  editType: 'rain' | 'outQ' // 编辑目标
}>({
  range: [],
  totalRain: undefined,
  rTarget: [],
  totalOutQ: undefined,
  valueType: '',
  editType: 'rain'
})

let lastForecastMainFutureDataVoMap: ForecastPlanDetail['forecastMainFutureDataVoMap'] = []
const { data: detailData, send: fetchDetail } = useWatcher(
  () => getForecastDetailForEdit(Number(props.currentPlan?.id || props.addBaseForecastId)),
  [() => props.currentPlan?.id, () => props.addBaseForecastId],
  {
    immediate: false,
    initialData: {},
    force: true
  }
).onSuccess(() => {
  formData.value = {
    ...props.currentPlan,
    ...detailData.value
  }
  lastForecastMainFutureDataVoMap = JSON.parse(
    JSON.stringify(detailData.value.forecastMainFutureDataVoMap || [])
  )
  setInitialData()
})

const setInitialData = (resetFaseEditModelInput = false) => {
  nextTick(() => {
    fastEditModel.range = [
      dayjs(allowRange.value.trueRange[0]).format('MM-DD HH时'),
      dayjs(allowRange.value.trueRange[1]).format('MM-DD HH时')
    ]
    fastEditModel.rTarget = rrTypeOptions.value?.map(i => i.value)
    if (resetFaseEditModelInput) {
      fastEditModel.totalOutQ = undefined
      fastEditModel.totalRain = undefined
      fastEditModel.valueType = undefined
    }
  })
}

const allowRange = computed(() => {
  const allowRange = generateTimeRangeFromStrings(
    detailData.value.forecastMainFutureDataVoMap?.map(item => item.futureDateStr) || [],
    'MM-DD HH时'
  )
  const start = dayjs(allowRange[0]).set('hour', 0).set('minute', 0).set('second', 0)
  const end = dayjs(allowRange[1]).set('hour', 23).set('minute', 59).set('second', 59)
  return {
    //限制日期范围
    dateRange: [start.toDate(), end.toDate()],
    //限制时间
    trueRange: allowRange
  }
})

const disabledRangeDate = (date: Date) => {
  let disabled = false
  try {
    if (date < allowRange.value.dateRange[0] || date > allowRange.value.dateRange[1]) {
      disabled = true
    }
  } catch (error) {
    console.error(error)
  }
  return disabled
}

const disabeldMinutes = (): number[] => Array.from({ length: 60 }, (_, i) => i)

watch(
  () => fastEditModel.range,
  range => {
    //判断所选时间范围是否在允许范围内
    if (range?.length === 2) {
      if (dayjs(range[0], 'MM-DD HH时').toDate() < allowRange.value.trueRange[0]) {
        fastEditModel.range[0] = dayjs(allowRange.value.trueRange[0]).format('MM-DD HH时')
        ElMessage.warning('起始时间小于最小可选时间，已重置为最小时间')
      }
      if (dayjs(range[1], 'MM-DD HH时').toDate() > allowRange.value.trueRange[1]) {
        ;(fastEditModel.range as string[])[1] = dayjs(allowRange.value.trueRange[1]).format(
          'MM-DD HH时'
        )
        ElMessage.warning('结束时间大于最大可选时间，已重置为最大时间')
      }
    }
  },
  {
    deep: true
  }
)

const rrTypeOptions = computed(() => {
  const recordKeys = Object.keys(detailData.value.stnm || {})
  if (!recordKeys.length) {
    return []
  }
  return recordKeys.reduce<Array<{ label: string; value: string }>>((acc, cur) => {
    let label = detailData.value.stnm?.[cur] || ''
    const rrStrIndex = label.indexOf('水库')
    if (rrStrIndex !== -1) {
      label = label.slice(0, rrStrIndex + 2)
    }
    acc.push({
      label,
      value: cur
    })
    return acc
  }, [])
})

watch(
  () => visible.value,
  val => {
    if (val) {
      fetchDetail()
    }
  }
)

// 当详情数据加载完成后，更新表单数据
watch(
  () => detailData.value.forecastMainFutureDataVoMap,
  val => {
    if (val) {
      formData.value.forecastMainFutureDataVoMap = val
    }
  },
  { immediate: true }
)

const resetDataVoMap = () => {
  const target = detailData.value.forecastMainFutureDataVoMap
  target?.splice(0, target.length, ...JSON.parse(JSON.stringify(lastForecastMainFutureDataVoMap)))
  setInitialData(true)
}

const btnLoading = ref(false)

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (!formData.value.forecastName) {
    ElMessage.error('请输入方案名称')
    return
  }

  try {
    btnLoading.value = true

    // 准备保存的数据，合并表单数据和详情数据
    const saveData: Partial<ForecastPlanDetail> = {
      ...detailData.value,
      ...formData.value,
      // 确保表格数据是最新的
      forecastMainFutureDataVoMap: detailData.value.forecastMainFutureDataVoMap
    }
    // 调用保存接口
    await saveDiyForecastMain(saveData)

    ElMessage.success('保存成功')
    visible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败，请稍后重试')
  } finally {
    btnLoading.value = false
  }
}

const batchEditData = (
  total: number,
  valueType: string,
  targetField: string,
  range: [string, string]
) => {
  const dateRange = generateTimeRangeStrings(
    dayjs(range[0], 'MM-DD HH时'),
    dayjs(range[1], 'MM-DD HH时'),
    'hour',
    'MM-DD HH时'
  )
  const typeMap = {
    [valueTypes[0]]: 2,
    [valueTypes[1]]: 1,
    [valueTypes[2]]: 3
  }
  return generateDistribution(typeMap[valueType], total, dateRange.length)?.reduce<
    Record<string, Record<string, number>>
  >((acc, val, index) => {
    acc[dateRange[index]] = {
      [targetField]: val
    }
    return acc
  }, {})
}
const valueTypeComponentKey = ref(0)
const resetValueType = () => {
  fastEditModel.valueType = undefined
  valueTypeComponentKey.value++
}

const handleEditTypeChange = () => {
  //切换时清空另一目标的总值，如果有需求支持两种目标同时编辑，去掉即可
  if (fastEditModel.editType === 'outQ') {
    fastEditModel.totalRain = undefined
  } else {
    fastEditModel.totalOutQ = undefined
  }
}

watch(
  () => [
    fastEditModel.range,
    fastEditModel.totalRain,
    fastEditModel.rTarget,
    fastEditModel.totalOutQ
  ],
  resetValueType
)

const handleValueTypeChange = async () => {
  const { range, totalRain, rTarget, totalOutQ, valueType, editType } = fastEditModel
  if (!valueType) {
    return
  }
  if (!range?.length) {
    resetValueType()
    ElMessage.warning('请先选择需要批量修改的时间范围')
    return
  }
  const editArgs: [number, string, string, [string, string]][] = []
  if (totalRain) {
    editArgs.push([totalRain, valueType, 'futureRainfall', range as [string, string]])
  }
  if (rTarget?.length && totalOutQ) {
    rTarget.forEach(target => {
      editArgs.push([totalOutQ, valueType, target, range as [string, string]])
    })
  }
  if (!editArgs.length) {
    if (editType === 'rain') {
      ElMessage.warning('请先输入总雨量')
    } else {
      ElMessage.warning('请先选择水库并输入单个水库的总出库流量')
    }
    resetValueType()
    return
  }
  const mergeObject = editArgs.reduce<Record<string, Record<string, number>>>((obj, curArgs) => {
    const fieldValueMap = batchEditData(...curArgs)
    return deepMerge(fieldValueMap, obj)
  }, {})
  // console.log('mergeObject', mergeObject);
  for (const dateStr in mergeObject) {
    const val = mergeObject[dateStr]
    const targetIndex = detailData.value.forecastMainFutureDataVoMap?.findIndex(i =>
      i.futureDateStr.includes(dateStr)
    )
    if (targetIndex !== -1) {
      detailData.value.forecastMainFutureDataVoMap![targetIndex!] = {
        ...detailData.value.forecastMainFutureDataVoMap![targetIndex!],
        ...val
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-dialog {
  max-height: 70vh;
  overflow-y: auto;
  color: #fff;

  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
    color: #7ff;
  }

  .basic-info {
    margin-bottom: 24px;

    .time-range {
      line-height: 32px;
    }
  }

  .forecast-setting {
    .setting-desc {
      margin-bottom: 16px;
      line-height: 1.5;
    }

    .rain-info {
      margin-bottom: 16px;
      font-weight: bold;
      color: #7ff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .fe-row {
        display: flex;
        flex-direction: row;
        gap: 10px;
        .gap10 {
          gap: 10px;
        }
        :deep(.dp) {
          width: 260px;
          flex: 0;
        }
        .t-input {
          display: flex;
          flex-direction: column;
          gap: 10px;
          .label {
            flex-shrink: 0;
            // min-width: 100px;
            // text-align: right;
            color: #fff;
          }
          .val-ipt {
            width: 200px;
            flex-shrink: 0;
          }
          .rt-select {
            width: 260px;
            --el-fill-color-blank: transparent;
            --el-color-info: #7ff;
            --el-color-white: rgb(0, 0, 0);
            --el-color-info-light-8: rgba(24, 130, 251, 0.4);
            --el-color-info-light-9: rgba(24, 130, 251, 0.6);
          }
        }
      }
    }

    .table-container {
      margin-bottom: 16px;
      overflow-y: auto;
    }
  }

  :deep(.el-form) {
    .el-form-item__label {
      color: #fff;
    }

    .el-input__wrapper,
    .el-textarea__wrapper {
      background-color: rgba(0, 38, 89, 0.6);
    }

    .el-input__inner,
    .el-textarea__inner {
      color: #fff;
    }
  }

  :deep(.forecast-table) {
    --el-table-bg-color: transparent;
    --el-table-header-bg-color: rgba(3, 86, 181, 0.2);
    --el-table-tr-bg-color: rgba(0, 38, 89, 0.6);
    --el-table-header-text-color: rgb(4, 100, 234);
    --el-table-text-color: #fff;
    --el-table-row-hover-bg-color: #06338f85;
    --el-table-border-color: rgba(127, 255, 255, 0.2);

    .el-table__row td {
      border-color: rgba(127, 255, 255, 0.2);
    }
  }
}
</style>
