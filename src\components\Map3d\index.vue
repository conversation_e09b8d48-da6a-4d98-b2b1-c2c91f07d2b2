<template>
  <div class="map-3d-wrap flx-1">
    <div :id="mapId" class="map-3d-box"></div>
    <MapTools />
  </div>
</template>
<script lang="ts" setup>
// import { getImageUrl } from '@/utils/index';
import MapTools from './MapTools.vue'
import { useMap3dStore } from '@/stores/modules/map3d.ts'
import type { IModelOption, ICripEffectOption } from '@/stores/interface/map3d.d'
import { useRoute } from 'vue-router'
import { getGeojsonFileByCategory } from '@/api/module/attachment'
import { markerConfig } from '@/hooks/useMapMarker'
import { jsonToURL } from '@/utils/index'

interface IProps {
  mapId?: string
  saveDefaultId?: boolean
}

interface ISpecialPoints {
  lgtd: string
  lttd: string
  dtmel: string
  stnm: string
}

const props = withDefaults(defineProps<IProps>(), {
  mapId: 'map-3d-1', // 地图初始化窗口id, 多个窗口传不同id
  saveDefaultId: true // 默认保存当前窗口地图id, 分屏不用保存
})

const route = useRoute()
const map3dStore = useMap3dStore()
const isHlq = import.meta.env.VITE_NODE_ENV.includes('hlq')

const specialPoints: ISpecialPoints[] = [
  {
    lgtd: '101.48542129147697',
    lttd: '25.1183285503196',
    dtmel: '1820.000',
    stnm: '青山嘴水库'
  },
  {
    lgtd: '101.16575431268893',
    lttd: '25.234650112756935',
    dtmel: '1820.000',
    stnm: '毛板桥水库'
  },
  {
    lgtd: '101.08924549102045',
    lttd: '25.235106153502326',
    dtmel: '1820.000',
    stnm: '老厂河水库'
  },
  {
    lgtd: '101.40789682376699',
    lttd: '25.23187222989632',
    dtmel: '1820.000',
    stnm: '九龙甸水库'
  },
  {
    lgtd: '101.3889408381819',
    lttd: '25.091108754487447',
    dtmel: '1820.000',
    stnm: '西静河水库'
  }
]

// 初始化青山嘴水库geojson
const initQszWaterLayer = () => {
  getGeojsonFileByCategory(10)
    .then(res => {
      if (res.status === 200) {
        const opt = {
          id: 'qszUpStreamStaticPolygon',
          // data: import.meta.env.VITE_PUBLIC_PATH + `map/map3d/qsz/data/qsz-upstream.geojson`,
          data: res.data,
          order: 100,
          symbol: {
            enableFeatureLabel: false,
            featureLabel: {
              featureIdAttr: 'name',
              fillColor: { r: 0, g: 0, b: 0, a: 1 },
              outlineColor: { r: 0, g: 255, b: 255, a: 1 }, //字体描边颜色
              outlineWidth: 4, //描边宽度
              font: '14px', //字体设置
              showBackground: true, //是否显示背景颜色
              pixelOffset: [0, 10], //设置屏幕空间中距此标签原点的像素偏移量
              text: function (properties) {
                //设置标签内容的回调函数
                return properties.name
              },
              minRange: 100, //最小可视距离 5000
              maxRange: 4000 //最大可视距离 200000000
            },
            polygon: {
              // color: { r: 98, g: 183, b: 180, a: 0.5 },
              color: { r: 96, g: 166, b: 222, a: 0.5 },
              outlineColor: { r: 96, g: 166, b: 222, a: 0.5 },
              outlineWidth: 2,
              outline: true,
              fill: true,
              // minRange: 100.0, //最近可视距离
              // maxRange: 4000.0, //最远可视距离
              minLevel: 15, //最小地图显示层级
              maxLevel: 20 //最大地图显示层级
            }
          }
          // zoomParams: {
          //   expand: zoomParams || { E: 0.01, W: 0.01, N: 0.01, S: 0.01 } //从东西南北4个方向往外扩展0.1度
          // },
          // zoom: {
          //   minDist: 500000
          // },
        }
        map3dStore.addCustomGeojsonLayer(opt, props.mapId)
      }
    })
    .catch(e => {
      console.log(e)
    })
}

// 青山嘴动态水面
const initQszWaterEffectLayer = () => {
  const waterEffectLayerList = [
    {
      id: 'qsz-sk-polygon',
      value: 8,
      // url: import.meta.env.VITE_PUBLIC_PATH + 'map/map3d/qsz/data/qsz-sk-polygon.geojson',
      waterStyle: {
        default: {
          height: 1803 // 1818, 1833，1803
        }
      }
    },
    {
      id: 'qsz-lcj-polygon',
      value: 9,
      // url: import.meta.env.VITE_PUBLIC_PATH + 'map/map3d/qsz/data/longchuanjiangxiayou.geojson',
      waterStyle: {
        default: {
          height: 1786 // 1796,1810
        }
      }
    }
  ]
  waterEffectLayerList.forEach(item => {
    getGeojsonFileByCategory(item.value).then(res => {
      if (res.status === 200) {
        const waterLayerOpt = {
          id: item.id,
          geojson: res.data,
          is3D: true //是否立体显示，默认为false
        }
        map3dStore.addWaterEffectLayer(waterLayerOpt, item.waterStyle, props.mapId)
      }
    })
  })
}

// 添加青山嘴模型
const addModelQsz = () => {
  // 主坝
  const mainDamOpt: IModelOption = {
    id: 'main-dam-glb-layer',
    url: import.meta.env.VITE_PUBLIC_PATH + `map/map3d/qsz/model/mainDam.glb`,
    type: 'gltf', //glb格式
    position: { lon: 101.48766841984084, lat: 25.117699823735165, height: 1751, isDegree: true }, //位置 1766，1751
    zoom: false
  }
  map3dStore.addModel(mainDamOpt, props.mapId)

  // 副坝
  let saddleDamOpt: IModelOption = {
    id: 'saddle-dam-glb-layer',
    url: import.meta.env.VITE_PUBLIC_PATH + `map/map3d/qsz/model/saddleDam.glb`,
    type: 'gltf', //glb格式
    position: { lon: 101.4875055330659, lat: 25.113789350548245, height: 1804, isDegree: true }, //位置 1819，1804
    zoom: false
  }
  map3dStore.addModel(saddleDamOpt, props.mapId)

  // 闸门
  // let gateOpt: IModelOption = {
  //   id: 'gate-glb-layer',
  //   url: import.meta.env.VITE_PUBLIC_PATH + `map/map3d/qsz/model/gate.glb`,
  //   type: 'gltf', //glb格式
  //   position: { lon: 101.4875055330659, lat: 25.113789350548245, height: 1820, isDegree: true }, //位置
  //   zoom: false
  // }
  // map3dStore.addModel(gateOpt, props.mapId)
}

const removeModelQsz = () => {
  const layerList = ['main-dam-glb-layer', 'saddle-dam-glb-layer', 'gate-glb-layer']
  layerList.forEach(item => {
    map3dStore.removeModel(item, props.mapId)
  })
}

// 添加青山嘴泛光线
const addGlowDynamicLine = () => {
  getGeojsonFileByCategory(11).then(res => {
    if (res.status === 200) {
      let opt = {
        id: 'glowDynamicLine', //图层id
        idAttr: 'FID_1', //要素的id属性
        // url: import.meta.env.VITE_PUBLIC_PATH + `map/map3d/qsz/data/qsz-upstream-line.geojson`, //河流线矢量
        url: res.data, //河流线矢量
        zoom: false, //添加后是否定位到图层
        symbol: {
          color: { r: 96, g: 166, b: 222, a: 1 }, //线的颜色
          width: 6, //线宽,
          height: 10, //设置整体高度
          clampToGround: true,
          // minRange: 3000,
          // maxRange: 6000000,
          minLevel: 5, //最小地图显示层级
          maxLevel: 14 //最大地图显示层级
        },
        click: function (event) {
          console.log(event)
        }
      }
      map3dStore.addGlowDynamicLine(opt, props.mapId)
    }
  })
}

const removeGlowDynamicLine = () => {
  map3dStore.removeGlowDynamicLine(props.mapId)
}

// 倾斜摄影
const setModelInclineQszVisible = (
  pathNameArr: string[] = [
    'Simulation',
    'SimulationPreview',
    'ImprovementFramework',
    'ImplementTheResponsibilitySystem'
  ]
) => {
  map3dStore.setModelVisible('qsz-tiles-model', pathNameArr.includes(route.name), props.mapId)
}
const addModelInclineQsz = () => {
  let opt: IModelOption = {
    id: 'qsz-tiles-model', //图层id
    type: '3dtiles', //图层类型3dtiles
    url: `/qsz/oblique-photography/3dtiles/qszsk/tileset.json`, //图层地址-确保能访问
    zoom: false, //是否缩放定位到图层
    name: '倾斜摄影'
    // height: 0
  }
  map3dStore.addModel(opt, props.mapId)
  setModelInclineQszVisible()
}

const removeModelInclineQsz = () => {
  map3dStore.removeModel('qsz-tiles-model', props.mapId)
}

// 特殊点
const addSpecialPointsQsz = () => {
  map3dStore.addComPointLayer({
    id: 'specialPoints ',
    data: specialPoints,
    symbol: {
      billboard: {
        image: markerConfig.RR_DYNAMIC_QSZ,
        width: 40,
        height: 40
      },
      label: {
        font: '18px',
        fillColor: { r: 0, g: 0, b: 0 },
        outlineColor: { r: 255, g: 255, b: 255 },
        outlineWidth: 4,
        backgroundColor: { r: 255, g: 255, b: 255, a: 0.8 },
        minRange: 10,
        maxRange: 300000,
        pixelOffset: [0, -50]
      }
    },
    zoom: false
  })
}

// 添蒙版
const addMasking = () => {
  getGeojsonFileByCategory(13).then(res => {
    if (res.status === 200) {
      let opt: ICripEffectOption = {
        id: 'qszMasking', //蒙版名称
        // holesData: import.meta.env.VITE_PUBLIC_PATH + `map/map3d/qsz/data/chuxiongArea.geojson`, //蒙版geojson数据
        holesData: jsonToURL(res.data), //蒙版geojson数据
        // holesData: res.data, //蒙版geojson数据
        style: {
          color: { r: 26, g: 49, b: 63, a: 0.8 },
          isLine: true,
          lineColor: { r: 255, g: 255, b: 0, a: 0.8 },
          lineWidth: 3,
          heightReference: Cesium.HeightReference.CLAMP_TO_GROUND // 贴地设置
        }
      }
      map3dStore.createCripEffect(opt, props.mapId)
    }
  })
}

// 移除蒙版
const removeMasking = () => {
  map3dStore.removeCripEffect('qszMasking')
}

watch(
  () => route.name,
  () => {
    // map3dStore.home()
    setModelInclineQszVisible()
  }
)

onMounted(() => {
  // map3dStore.initMap(props.mapId, props.saveDefaultId)
  map3dStore.initMap2(props.mapId, props.saveDefaultId)
  if (!isHlq) {
    initQszWaterLayer()
    initQszWaterEffectLayer()
    addGlowDynamicLine()
    addModelQsz()
    addModelInclineQsz()
    addMasking()
    // addSpecialPointsQsz()
  }
})
onUnmounted(() => {
  map3dStore.destroyApi(props.mapId)
  if (!isHlq) {
    removeModelQsz()
    removeGlowDynamicLine()
    removeModelInclineQsz()
    removeMasking()
  }
})
</script>
<style lang="scss" scoped>
.map-3d-wrap,
.map-3d-box {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
</style>
