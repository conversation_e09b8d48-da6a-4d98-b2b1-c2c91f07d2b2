import { number } from 'echarts'

// 缩放
interface Zoom {
  minDist: number
  keepAV?: boolean
}

// 点标记配置
interface Symbol<T> {
  label: Partial<Label>
  billboard: Partial<Billboard>
  window: Partial<Window<T>>
  conditionSetDef: (data: T) => Partial<Omit<Symbol<T>, 'conditionSetDef'>> | undefined // 条件设置,参数和Symbol一样
  minLevel: number // 最小显示层级
  maxLevel: number // 最大显示层级
}

// div窗口
interface Window<T> {
  moveShow: boolean // 是否鼠标靠近显示title，false为直接显示
  divData: DivData<T> // div中呈现的数据 可在此预先定义
  div: string // div字符串模板,
  divfun: (data: T) => string // div回调函数,
  pixelOffset: number[] // [水平方向(向左为负),竖直方向(向上为负)]
  customAdjustUI: boolean //是否启动窗口位置自适应调整算法。位置分别为上方居中、下方居中、左侧居中、右侧居中
}

interface DivData<T> {
  [key: string]: ((data: T) => BasicType) | BasicType
}

type BasicType = string | number | boolean

// 图标类配置
interface Billboard {
  image: string // 图标路径
  width: number // 图标宽度
  height: number // 图标高度
  depthTest: boolean // 深度检测
  pixelOffset: number[] // 设置屏幕空间中距此标签原点的像素偏移量
  clampToGround: boolean //贴地，暂不使用
  timeInterval: number // gif频率调整参数，数值越大，闪烁越慢
}

// 标签类配置
interface Label {
  text: string // 标签文本设置
  minRange: number // 最小可视距离
  maxRange: number // 最大可视距离
  pixelOffset: number[] // 设置屏幕空间中距此标签原点的像素偏移量
  font: string // 字体设置
  fillColor: Color // 字体填充颜色
  outlineColor: Color // 字体描边颜色
  outlineWidth: number // 字体描边宽度
  backgroundColor: Color // 标签背景颜色
  backgroundPadding: number[] // [水平方向，竖直方向]
  showBackground: boolean // 是否显示标签背景
  depthTest: boolean // 是否开启标签深度检测
  clampToGround: boolean // 是否贴地
}

// 位置属性
interface PosAttr {
  x: string // 经度字段配置
  y: string // 纬度字段配置
  z?: string // 高度字段配置
}

// 面要素样式设置
interface PolygonSymbol {
  enableFeatureLabel: boolean // 是否开启enableFeatureLabel标签（全局参数，不能通过条件函数修改）
  featureLabel: Partial<FeatureLabel> // 标签设置 不与实体（Polyline/polygon）绑定
  polygon: Partial<Polygon>
  conditionSetDef:
    | undefined
    | ((properties: any) => Partial<Omit<PolygonSymbol, 'conditionSetDef'>>)
}

// 多边形，面
interface Polygon {
  color: Color
  outlineColor: Color
  outlineWidth: number
  outline: boolean
  fill: boolean
  minLevel: number
  maxLevel: number
  zIndex: number
  clampToGround: boolean
}

// 标签样式
interface FeatureLabel {
  featureIdAttr: string // 用做featureLabel唯一标识的字段，该字段值在数据集中必须唯一，无重复值
  text: (properties: any) => string // 设置标签内容的回调函数
  pixelOffset: number[] //设置屏幕空间中距此标签原点的像素偏移量
  font: string // 字体设置
  outlineColor: Color // 字体描边颜色
  outlineWidth: number // 描边宽度
  minRange: number // 最小可视距离
  maxRange: number // 最大可视距离
  minLevel: number //最小地图显示层级
  maxLevel: number //最大地图显示层级
  fillColor: Color
  showBackground: boolean
}

// 线图层
interface PolylineSymbol {
  polyline: Partial<Polyline>
  enableEntityLabel: boolean
  entityLabel: Partial<EntityLabel>
  enableFeatureLabel: boolean
  featureLabel: Partial<FeatureLabel>
  conditionSetDef:
    | ((properties: any) => Partial<Omit<PolylineSymbol, 'conditionSetDef'>>)
    | undefined
}

interface EntityLabel {
  outlineColor: Color
  outlineWidth: number
  pixelOffset: number[]
  text: (properties: any) => string
}

// 线样式
interface Polyline {
  color: Color // 线颜色设置
  width: number // 线宽
  clampToGround: boolean // 是否贴地
  // zIndex: number
  minLevel: number // 最小地图显示层级
  maxLevel: number // 最大地图显示层级
}

// 蒙版style
interface Style {
  color: Color
  isLine: boolean // 是否描边
  isGrow: boolean // 描边线是否泛光
  lineColor: Color // 描边线颜色
  lineWidth: number // 描边线宽度
  glowPower: number // 泛光强度
  height: number // 高度
  heightReference: any // 贴地设置
}

interface ColorItem {
  color: Color
  min: number
  max: number
}

interface BaseModel {
  id: string // 图层id
  url: string // 模型地址
  type: '3dtiles' | 'gltf' // 图层类型
  name?: string // 图层名称
  zoom?: boolean // 是否缩放定位到图层
}

interface WaterStyle {
  value: string // 字段值,如：韩江
  height: number // 整体高度，若设置高度，其优先级比start，end高
  start: number[] //起点
  end: number[] //终点
  color: Color //颜色,颜色一样可能会导致速度，数量，高度设置无效
  waveNum: number
  waveSpeed: number
  waveHeight: number
}

interface ITransferTarget<T> {
  data: T[] // 目标点,，在人员转移业务中为安置点数据
  idAttr: string
  posAttr: PosAttr
  // symbol: Partial<Symbol<T>>
  symbol: Pick<Symbol<T>, 'label' | 'billboard' | 'conditionSetDef'>
}

interface ITransferOrigin<T> extends ITransferTarget<T> {
  targetIdAttr: string // 关联目标点的字段
}

interface Model3dTiles extends BaseModel {
  type: '3dtiles'
}

interface ModelGlb extends BaseModel {
  type: 'gltf' // glb格式
  position: {
    lon: string | number // 经度
    lat: string | number // 纬度
    height?: number // 高度
    isDegree?: boolean // 是否是经纬度,true
  }
  rotation?: {
    heading: number // 围绕z轴旋转
    pitch: number // 围绕y轴旋转
    roll: number // 围绕x轴旋转
  }
}

// 定位到经纬度位置
export interface IFlyToPos {
  lon: number | string // 经度
  lat: number | string // 纬度
  radius?: number // 可视半径（可选，默认值5000）
  height?: number // 高度（可选，默认值500）
  keepAV?: boolean // 是否保持默认的视角（默认fasle）
}

// 颜色
export type Color =
  | string
  | {
      r: number
      g: number
      b: number
      a?: number
    }

// 点图层
export interface IPointOption<T = any> {
  id: string // 图层id
  data: T[] // 数据
  symbol?: Partial<Symbol<T>>
  order?: number // 图层次序，越大越上面
  idAttr?: string // 点数据用于唯一标识的字段
  posAttr?: PosAttr // 位置属性
  // conditionSetDef?: () => any
  click?: (e: any) => void // 点击事件
  mouseover?: () => void
  zoom?: boolean | Zoom // 缩放定位到图层
  zoomDelay?: number // 缩放延时
  layerType?: string
  overlayType?: string
}

// 斜摄影模型
export type IModelOption = Model3dTiles | ModelGlb

// geojson数据图层
export interface IGeojsonOption {
  id: string // 图层id
  data: string | Record<string, any> // geojson数据或者geojson数据的url
  order?: number // 图层顺序，值越大，越在上面
  symbol: Partial<PolygonSymbol> | Partial<PolylineSymbol>
  zoom?: boolean // 是否定位到geojson数据四至范围，默认为false
  zoomParams?: {
    keepAV: boolean // 是否保持原场景视角
    expand?: { E: number; W: number; N: number; S: number } //从东西南北4个方向往外扩展0.1度,度数越大显示范围越广
  }
  click?: (evt: any) => void // 点击事件
}

// 蒙版
export interface ICripEffectOption {
  id: string // 蒙版id
  holesData: string // 蒙版geojson数据
  style: Partial<Style> // 样式设置
  zoom?: boolean // 缩放显示范围
}

// 飞到指定对象
export interface IFlyToOption {
  minDist: number // 缩放的最近距离，防止很小的物体缩放的太近了
  offset: { heading: number; pitch: number; range: number }
  duration: number // 飞行时间，单位s
  keepAV: boolean // 是否保持视角,注：keepAV与offset互斥，设置keepAV就不要设置offset
}

// html图层
export interface IAddLayerOption extends Pick<IPointOption, 'id' | 'data' | 'idAttr' | 'zoom'> {
  symbol?: {
    html?: string
    click?: (evt: any) => void
    mouseover?: (evt: any) => void
  }
}

// 淹没水位变化动画
export interface IRenderFloodedAnimationOption {
  url: string // 淹没范围geojson文件路径
  speed: number // 淹没模拟时的水位上涨高度，单位：米/秒 默认5米
  fromHeight: number // 淹没动画起始高度 默认为0
  toHeight: number // 淹没动画终点点高度 默认为300
  curHeight: number // 淹没动画当前高度 默认为起始高度
  zoomTo: boolean // 定位到淹没区域 默认是true
  floodColor: Color // 洪水淹没 默认为"#A48B59"
  floodAlpha: number // 洪水透明度 0.6
  startPlay: boolean //是否立刻播放
  playingCallback: (data: any) => void //通过此回调方法可以获取淹没模拟过程的水位实时状态
}

// 雨天
export interface IWeatherRainOption {
  enable?: boolean // 是否启动雨天效果
  speed?: number // 雨速 0.5
  rainSize?: number // rainSize 雨密度 0.5
  mixNum?: number // 天空暗度 0.5
}

// 雾天
export interface IWeatherFogOption {
  enable: boolean // 是否启动雾天效果
  color: string // 雾效颜色
  fogFar: number // 能见距离调整
}

// 晴天
export interface IWeatherSunnyOption {
  enable: boolean // 是否启动晴天效果
  intensity: number // 亮度调整
}

// 降雨等值面
export interface IRainContourOption {
  url: string //降雨等值面的地址
  renderZero: boolean //是否渲染雨量为0的区域
  alpha: number //整体透明的
  zIndex?: number //图层的叠加顺序，越大图层越上方
  height?: number // 图层高度，要在水面效果上方，流动线下方，将高度设置为该2个图层之间即可
  colorItemArray: ColorItem[] //颜色配置，
  otherColor: Color
  colorItemAttr: string //设置颜色对应的属性字段
  click?: (evt: any) => void
  mousemove?: (evt: any) => void
  mouseout?: (evt: any) => void
}

// 水面波纹参数
export interface IWaterLayerOption {
  id: string // 图层id
  geojson: string // geojson地址
  deltaHeight?: string
  is3D?: boolean //是否立体显示，默认为false
  minRange?: number
  maxRange?: number
  flyTo?: boolean
}

// 水面波纹样式参数
export interface IWaterStyleOption {
  default: {
    color: Color
    normalMap: string //设置扰动图片
    height: number
    waveNum: number
    waveSpeed: number
    waveHeight: number
  }
  //整体设置
  assignStyle: {
    //根据需要，指定要素设置样式，如这边设置韩江样式不同
    field: string //识别字段,如：Name
    style: WaterStyle[]
  }
}

// 淹没网格接口
export interface ILoadMeshOption {
  meshData: any // 淹没网格数据
  isoConfig: {
    min: number // 标量区间的较小值
    max: number // 标量区间的较大值
    minTx: number // 标量区间的较小值对应图片上的位置, 如：0
    maxTx: number // 标量区间的较大值对应图片上的位置, 如：0.2
  }[]
  colorItemArray: {
    colorMapSource: string // 图例图片
    alpha: string // 透明度
    minTx: string // 图片最小有效值所在的比例，一般为0.0
    maxTx: string // 图片最大有效值所在的比例，一般为1.0
  } // 淹没动画起始高度 默认为0
  deltaZ: number // 网格抬升高度
}

// 淹没结果数据设置
export interface ILoadResultFloodOption {
  // 淹没顶点结果
  listVert: {
    id: string | number // 为顶点编号
    wd: string // 水深
    wl?: string // 水位
    u?: string // 分量
    v?: string // 流速
  }[]
  // 淹没的网格ID编号数组
  listCell: number[] // 格式为[0,1,2,3,4, ....]
}

// 视角定位
export interface ISceneShiftOption {
  isdegree?: boolean //（是角度还是弧度）是弧度,设为false
  lat: string | number // 纬度
  lon: string | number // 经度
  height?: number // 高度
  pitch?: number // 俯仰角
  roll?: number // 翻滚角
  heading?: number // 偏航角
}

// 绘制漫游路径
export interface IDrawRoamPathOption {
  isdegree?: 'SWING' | 'LOOP' // 漫游方式:'SWING'(单次播放)|'LOOP'（循环播放），默认为LOOP
  speed?: number // 漫游速度 默认为 500
  height?: number // 整体高度调节
  useTerrain?: boolean // 是否使用地形资源采样的高度（比较准，但拾取较慢），否则是基于经纬度获取高程
  clickCallback?: (data: any) => void // 左键点击回调函数，返回当前点信息
  callback?: (data: any) => void // 回调函数，返回生成的漫游路线数据
}

// 播放漫游路径
export type PlayRoamPathOption<T extends 'data' | 'url'> = {
  showLine: boolean // 是否显示漫游路径
  model: {
    uri: string // 模型路径
    adjustAngle?: number // 模型角度纠偏-90
    minimumPixelSize?: number // 获取或设置指定模型的近似最小像素大小的数字属性，无论缩放如何
    scale?: number // 获取或设置指定此模型的统一线性比例的数字属性。大于 1.0 的值会增加模型的大小，而小于 1.0 的值会减小它
  }
  dynamicsCamera: any // 场景视角是否动
  playingCallback: (data: any) => void // 播放过程回调函数，返回当前点信息及及当前播放进度
  playCallback: (data: any) => void // 播放完的回调函数
} & (T extends 'data' ? { data: any } : { url: string })

// 调整漫游速度
export type adjustRoamSpeedOption =
  | { posIndex: number } // 通过位置索引
  | { progress: number } // 通过进度百分比
  | { speedScale: number } // 速度倍率，大于1为加速，小于1为减速

export interface ITransferOption<T = any> {
  id: string
  target: ITransferTarget<T> // 目标点
  origin: ITransferOrigin<T> // 起点
  transferInfo: {
    symbol: {
      deltaHeight: number // 当没有路线数据，采用转移示意线时，示意线的最高点
      line?: {
        color?: Color
        width?: number // 线宽
        speed?: 5 // 流动速度
      }
    }
    info: {
      label: Partial<Label> // 标签类配置，详见点图层相关设置
      timeInterval?: number // 信息位置移动的时间间隔
    }
  }
}

export interface ITransferUpdateOption<T> {
  id: string
  data: {
    origin: T[]
    target?: T[]
  }
}

interface DynamicSymbol {
  color: Color
  width: number
  height: number //设置整体高度
  speed: number
  featureLabel: FeatureLabel
  minLevel: number
  maxLevel: number
  conditionSetDef: (data) => Partial<Omit<DynamicSymbol, 'conditionSetDef'>> | undefined
  [key: string]: any
}

interface DynamicFeatureLabel {
  outlineColor: Color
  fillColor: Color
}

// 泛光流动线
export interface IGlowDynamicLineOption {
  id: string
  idAttr: string
  url: string
  zoom?: boolean
  symbol?: Partial<DynamicSymbol>
  click?: (data: any) => void
}
