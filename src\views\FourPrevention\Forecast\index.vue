<template>
  <div class="content">
    <left-region style="color: #fff">
      <div class="pr10 left-content">
        <div class="forecast-plan">
          <div class="plan-title">
            <span class="title-icon"></span>
            预报方案
            <span class="plan-time" v-show="currentPlan"
              >预报时段 {{ currentPlan?.beginTime?.slice(5, 16).replace('T', ' ') }} -
              {{ currentPlan?.endTime?.slice(5, 16).replace('T', ' ') }}</span
            >
          </div>
          <plan-select v-model="currentPlan">
            <div class="plan-name">{{ currentPlan.forecastName || '--' }}</div>
          </plan-select>
        </div>
        <div class="data-container">
          <!-- 气象卫星 -->
          <div class="data-part">
            <base-title class="mt9">
              气象卫星
              <template #extra>
                <div class="ml10 flx-align-center" @click="handleShowSatelliteDialog">
                  <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
                </div>
              </template>
            </base-title>
            <satellite :plan-id="currentPlanId" />
          </div>

          <div class="data-part">
            <base-title class="mt13">
              水库雨水情预报
              <template #extra>
                <div class="ml10 flx-align-center" @click="handleShowWaterForecast">
                  <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
                </div>
              </template>
            </base-title>

            <!-- 水库雨水情预报 -->
            <div class="water-forecast">
              <div class="forecast-future">
                <arrow-title>未来24小时</arrow-title>
                <el-checkbox class="c-checkbox" v-model="showWaterLayer" label="降雨等值面" />
              </div>
              <!-- 水情数据展示 -->
              <div>
                <div class="data-wrap">
                  <base-record
                    v-for="item in STATIC_WATER_LABELS"
                    :key="item.title"
                    :title="item.title"
                    :count="getItemValue(item).value"
                    :unit="getItemValue(item).unit"
                    :color="item.color"
                    class="data-item"
                  >
                    <template #extra>
                      <!-- <span>暴雨</span> -->
                    </template>
                  </base-record>
                </div>
              </div>
            </div>
          </div>
          <div class="data-part">
            <!-- 预测/实测切换 -->
            <!-- <base-tabs
              :active-index="chartModeList.findIndex(item => item.value === chartMode)"
              :list="chartModeList"
              @change-tab="changeChartMode"
              :disabled="!currentPlanId"
              @click-disabled="handleClickDisabled"
            ></base-tabs> -->

            <!-- 图表 -->
            <forecast-chart
              class="forecast-chart-container"
              :is-forcast="chartMode === 'forecast'"
              :time-data="timeData"
              :data="currentForecastData"
            />
          </div>
        </div>
      </div>
    </left-region>
    <right-region style="color: #fff" overflow="visible auto">
      <base-title> 纳雨能力 </base-title>
      <div class="power-container">
        <div class="cloud-icon"></div>
        <div class="cloud-bottom">
          <div class="power-item" v-for="(item, index) in POWER_DATA_CONFIG" :key="index">
            <div class="top-icon" :class="`d${index}`"></div>
            <span class="value">{{ rainAbsorptionAbilityData?.[item.valueKey] || 0 }}</span>
            <div class="bottom-icon"></div>
            <span class="label">{{ item.label }}</span>
          </div>
        </div>
      </div>
      <base-title class="mt40"> 预报概化图 </base-title>
      <!-- 特效1 光点动画 -->
      <conceptualization-with-dots :plan-id="currentPlanId" />
      <!-- 特效2 线条动画 -->
      <!-- <conceptualization :plan-id="currentPlanId" /> -->

      <base-title class="mt19">
        河道水情预报
        <template #extra>
          <div class="ml10 flx-align-center" @click="showSectionDialog">
            <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
          </div>
        </template>
      </base-title>
      <river-forecast-list class="mt10" :data="forecastResult" />
    </right-region>
    <water-layer v-model="showWaterLayer" />
    <f-legend :plan-id="currentPlanId" />
    <sectionDetailDialog
      v-model="sectionDialogVisible"
      :forecast-id="currentPlanId || 0"
      :dispatch-id="currentDispatchId"
      :id="sid"
      title="河道水情预报"
    >
      <el-segmented
        class="sub-segmented c-el-segmented have-padding"
        v-model="sid"
        :options="
          forecastResult?.map(item => {
            return {
              label: item.sname,
              value: item.id
            }
          }) || []
        "
      />
    </sectionDetailDialog>
    <forecast-dialog
      v-model="showWaterForecast"
      :list="compareMonitorDataList"
      type="all"
      :show-measurement="false"
    />
    <satellite-dialog v-model="satelliteDialogVisible" :plan-id="currentPlanId" />
  </div>
</template>

<script lang="ts" setup>
import WaterLayer from './components/WaterLayer.vue'
import RiverForecastList from './components/RiverForecastList.vue'
import ConceptualizationWithDots from './components/ConceptualizationWithDots.vue'
import SatelliteDialog from './components/SatelliteDialog.vue'
// import Conceptualization from './components/Conceptualization.vue'
import Satellite from './components/Satellite.vue'
import FLegend from './components/FLegend.vue'
import ForecastChart from './components/ForecastChart.vue'
import { useRequest, useWatcher } from 'alova/client'
import {
  getCurrentForecast,
  getForecastResult,
  getFutureForecast,
  getRainAbsorptionAbility
} from '@/alova_api/methods/forecast'
import { POWER_DATA_CONFIG, STATIC_WATER_LABELS } from './constant'
import { ForecastPlanDetail } from '@/alova_api/type'
import ForecastDialog from '../Simulation/forecastDialog/index.vue'
import sectionDetailDialog from '../Simulation/preview/popupDownstream/sectionDetailDialog.vue'
import { getDispatchByForecastId } from '@/api/module/fourPrevention/simulation'
import dayjs from 'dayjs'

// 图表模式
const chartMode = ref('forecast') // actual | forecast

// 是否显示降雨等面值
const showWaterLayer = ref(true)

// 是否显示卫星云图详情
const satelliteDialogVisible = ref(false)

const handleShowSatelliteDialog = () => {
  satelliteDialogVisible.value = true
}

const showWaterForecast = ref(false)
const handleShowWaterForecast = () => {
  showWaterForecast.value = true
}

const currentPlan = ref<Partial<ForecastPlanDetail>>({})
// 预报方案数据
const currentPlanId = computed(() => currentPlan.value?.id)
const { data: staticWaterData } = useWatcher(
  () => getFutureForecast(currentPlanId.value || 0),
  [currentPlanId]
)
const sid = ref(0) //断面id

const currentDispatchId = ref(0)
watch(currentPlanId, newVal => {
  if (newVal) {
    getDispatchByForecastId({ forecastId: newVal }).then(res => {
      currentDispatchId.value = res.data[0]?.id
    })
  }
})

const { data: forecastResult } = useWatcher(
  () => getForecastResult(currentPlanId.value || 0),
  [currentPlanId]
)

const { data: currentForecastData } = useWatcher(
  () => getCurrentForecast(currentPlanId.value || 0, chartMode.value === 'forecast'),
  [() => currentPlanId.value, () => chartMode.value === 'forecast'],
  {
    initialData: []
  }
)

const timeData = computed(() => {
  const currentDateTime = dayjs()
  const dataLength = chartMode.value === 'forecast' ? 48 : 24
  const timeData: string[] = new Array(dataLength).fill('') // MM-DD HH时
  for (let i = 0; i < dataLength; i++) {
    if (i < 24) {
      timeData[i] = currentDateTime.subtract(23 - i, 'hour').format('MM-DD HH') + '时'
    } else {
      timeData[i] = currentDateTime.add(i - 23, 'hour').format('MM-DD HH') + '时'
    }
  }
  return timeData
})

const compareMonitorDataList = computed(() => {
  return currentForecastData.value.map((item, idx) => {
    return {
      id: idx,
      deviation: +(item.waterLevel - item.wl).toFixed(3),
      inQ: +item.inflow.toFixed(3),
      outQ: +item.outflow.toFixed(3),
      z: +item.waterLevel.toFixed(3),
      wrz: +item.wl.toFixed(3),
      drp: +item.rainfall.toFixed(3),
      showtm: idx === 0 ? '' : timeData.value[idx - 1]
    } as any
  })
})

// 纳雨能力
const { data: rainAbsorptionAbilityData } = useRequest(() => getRainAbsorptionAbility(), {
  initialData: {}
})

// const chartModeList = ref([
//   { title: '实测', value: 'actual' },
//   { title: '预测', value: 'forecast' }
// ])
// const changeChartMode = (index: number) => {
//   chartMode.value = chartModeList.value[index].value
// }

// const handleClickDisabled = () => {
//   ElMessage.warning('请先选择预报方案')
// }

const getItemValue = (item: (typeof STATIC_WATER_LABELS)[number]) => {
  const value = staticWaterData.value?.[item.key] || 0
  if (item.key === 'totalIn' || item.key === 'totalOut') {
    let moreThan10000 = false

    if (value > 10000) {
      moreThan10000 = true
    }
    return {
      value: moreThan10000 ? (value / 10000).toFixed(2) : value.toFixed(2),
      unit: moreThan10000 ? '万m³' : 'm³'
    }
  }
  return {
    value: item.key === 'maxWaterLevel' ? value.toFixed(3) : value.toFixed(2),
    unit: item.unit
  }
}

const sectionDialogVisible = ref(false)
const showSectionDialog = () => {
  sid.value = forecastResult.value?.[0]?.id || 0
  sectionDialogVisible.value = true
}
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  position: relative;
  .left-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    // justify-content: space-around;
  }
  .data-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    .data-part {
      &:nth-last-of-type(1) {
        flex: 1;
        display: flex;
        flex-direction: column;
        .forecast-chart-container {
          flex: 1;
        }
      }
    }
  }
}

/* 预报方案样式 */
.forecast-plan {
  width: 100%;
  padding: 10px 14px;
  border-radius: 2px;
  box-sizing: border-box;
  background: url('@/assets/images/common/row-bg.png') no-repeat center;
  background-size: 100% 100%;
}

.plan-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  padding: 5px 0;

  .title-icon {
    width: 16px;
    height: 16px;
    background: url('@/assets/images/forecast/ybfa-icon.png') no-repeat center;
    background-size: contain;
    margin-right: 8px;
  }

  .plan-time {
    margin-left: auto;
    font-size: 14px;
    color: #fff;
  }
}
.plan-name {
  font-size: 14px;
  text-align: left;
  font-weight: 400;
  color: #ffffff;
  margin-right: 6px;
  padding: 8px 7px;
  box-sizing: border-box;
  border-radius: 4px;
  border: 1px solid #77ffff;
  background: #083766;
  flex: 1;
}

/* 水库雨水情预报部分 */
.water-forecast {
  padding-left: 11px;
  box-sizing: border-box;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.forecast-future {
  display: flex;
  align-items: center;
  font-size: 16px;
  flex-direction: row;
  justify-content: space-between;
  color: #77ffff;
}

/* 水情数据展示 */

.data-wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: wrap;
  .data-item {
    width: 50%;
    flex-shrink: 0;
    align-items: self-start;
  }
}

.power-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  .cloud-icon {
    width: 174px;
    height: 173px;
    background: url('@/assets/images/forecast/cloud.png') no-repeat center -2.365vw;
    background-size: 100% 100%;
    animation: radar-beam 3s infinite;
    animation-timing-function: cubic-bezier(0.53, 0, 0.43, 0.99);
    animation-delay: 1.4s;
  }
  .cloud-bottom {
    width: 286px;
    height: 130px;
    background: url('@/assets/images/forecast/cloud-bottom.png') no-repeat center center;
    background-size: contain;
    margin-top: -150px;
    display: flex;
    flex-direction: row;
    align-items: self-end;
    justify-content: space-between;
    .power-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: calc(100% / 3);

      .label {
        font-size: 14px;
        color: #fff;
        font-family: 'SourceHanSansCN-Regular';
        margin-top: 6px;
      }
      .value {
        text-shadow: 0 4px 6.8px #00000040;
        font-family: 'YouSheBiaoTiHei';
        font-size: 30px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-top: -10px;
      }
      .top-icon {
        width: 43px;
        height: 43px;
        animation: radar-beam 3s infinite;
        animation-timing-function: cubic-bezier(0.53, 0, 0.43, 0.99);
        &.d1 {
          animation-delay: 1s;
        }
        &.d2 {
          animation-delay: 2s;
        }
      }
      .bottom-icon {
        width: 73px;
        height: 48px;
        margin-top: -30px;
      }
      &:nth-child(1) {
        color: #77ffffff;
        margin-left: -36px;
        .top-icon {
          background: url('@/assets/images/forecast/dqkr.png') no-repeat center center;
          background-size: cover;
        }
        .bottom-icon {
          background: url('@/assets/images/forecast/dqkr-bottom.png') no-repeat center center;
          background-size: cover;
        }
      }
      &:nth-child(2) {
        color: #42e75bff;
        margin-bottom: -40px;
        .top-icon {
          background: url('@/assets/images/forecast/nynl.png') no-repeat center center;
          background-size: cover;
        }
        .bottom-icon {
          background: url('@/assets/images/forecast/nynl-bottom.png') no-repeat center center;
          background-size: cover;
        }
      }
      &:nth-child(3) {
        color: #ffd060ff;
        margin-right: -36px;
        .top-icon {
          background: url('@/assets/images/forecast/xxkr.png') no-repeat center center;
          background-size: cover;
        }
        .bottom-icon {
          background: url('@/assets/images/forecast/xxkr-bottom.png') no-repeat center center;
          background-size: cover;
        }
      }
    }
  }
}

@keyframes radar-beam {
  0% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }

  100% {
    transform: translateY(0);
  }
}
</style>
