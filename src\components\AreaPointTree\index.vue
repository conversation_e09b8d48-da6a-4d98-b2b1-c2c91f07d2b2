<template>
  <div :class="['area-point-tree', 'flex', 'flex-column', className]">
    <!-- 测站类型选择 -->
    <div class="flex pro-type-container mb20">
      <button type="button" :class="[pointTypeCheck.indexOf(val.value) !== -1 ? 'selected' : '']"
        v-for="val of pointTypeData" :label="val.value" :key="val.value" @click="handleProTypeChange(val)">
        <img :src="val.src" />
        <span class="mr10 btn-name">{{ val.label }}</span>
        <span class="btn-num">{{ prointTypeNum[val.value] }}</span>
      </button>
    </div>
    <el-tree class="filter-tree" ref="tree" node-key="id" :data="areaData" v-if="areaData.length" default-expand-all
      highlight-current @node-click="treeNodeClick">
      <template slot-scope="{ node, data }">
        <div class="custom-node ellipsis-text flex-vc">
          <span class="custom-icon flex-vc">
            <i v-if="data.sttp"
              :class="['pro-type', 'pro-type-' + (data.sttp === 'PP' ? 'RAIN' : 'RL') + data.isWarn]"></i>
            <i v-else-if="!data.children || !data.children.length" class=""></i>
            <i v-else-if="node.expanded" class="el-icon-minus"></i>
            <i v-else class="el-icon-plus"></i>
          </span>
          <el-tooltip v-if="data.sttp" class="item" effect="light" :content="node.label" placement="right">
            <span class="custom-label">{{ node.label }}</span>
          </el-tooltip>
          <span v-else class="custom-label">{{ node.label }}</span>
        </div>
      </template>
    </el-tree>
  </div>
</template>

<script>
// 用于“综合评分”及“水雨情”
import { queryPointByArea } from '@/api/OperationMonitoring/waterAndRain'
import { mapState } from 'vuex'
export default {
  name: 'AreaPointTree',
  props: {
    stateCheck: {
      type: Array,
      default: () => [0, 1]
    },
    className: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      areaData: [],
      allAreaData: [],
      pointTypeData: [
        {
          value: 'RR,ZZ',
          label: '水位',
          src: require('@/assets/map/white-rl.png')
        },
        {
          value: 'PP',
          label: '雨量',
          src: require('@/assets/map/white-rain.png')
        }
      ],
      pointTypeCheck: ['RR,ZZ', 'PP'],
      cancelCheckedNum: '',
      prointTypeNum: {
        'RR,ZZ': 0,
        PP: 0
      },
      checkTypeData: [
        {
          value: 0,
          label: '正常'
        },
        {
          value: 1,
          label: '异常'
        }
      ],
      stateTypeNum: {
        0: 0,
        1: 0
      },
      cancelCheckedState: null,
      isFirst: true,
      allSts: []
    }
  },
  computed: {
    ...mapState({
      isSingleProject: state => state.project.isSingleProject,
      areaCode: state => state.user.userInfo.areaCode
    })
  },
  watch: {
    stateCheck: {
      handler(val) {
        if (Array.isArray(val) && val.length > 0) {
          this.allSts = []
          this.areaData = []
          const areaData = JSON.parse(JSON.stringify(this.allAreaData))
          this.loopFormatTreeData(areaData)
          this.areaData = areaData
          this.setPointMap()
        }
      },
      deep: true
    },
    className: {
      handler(val) {
        if (val) {
          this.pointTypeData = [
            {
              value: 'RR,ZZ',
              label: '水位',
              src: require('@/assets/map/normal-rl.png')
            },
            {
              value: 'PP',
              label: '雨量',
              src: require('@/assets/map/normal-rain.png')
            }
          ]
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    handleProTypeChange(val) {
      const index = this.pointTypeCheck.indexOf(val.value)
      if (index === -1) {
        this.pointTypeCheck.push(val.value)
        this.loadData()
      } else {
        if (this.pointTypeCheck.length === 1) {
          this.$message.warning('请至少选择一种类型')
        } else {
          this.pointTypeCheck.splice(index, 1)
          this.loadData()
        }
      }
    },
    // // 检测checkbox group的回调函数
    // handlePointTypeCheckboxChange (val) {
    //   if (val.length === 0) {
    //     this.$message.warning('请至少选择一种类型')
    //     this.pointTypeCheck = [this.cancelCheckedNum]
    //     return false
    //   }
    //   this.loadData()
    // },
    // // 检测checkbox的回调函数
    // handlePointTyeChange (isChecked, val) {
    //   this.cancelCheckedNum = isChecked ? null : val
    // },
    /**
     * 树状列表的数据进行格式化
    */
    loopFormatTreeData(data) {
      if (Array.isArray(data) && data.length > 0) {
        for (let i = 0, num = data.length; i < num; i++) {
          if (data[i].sttp) {
            data[i].label = data[i].pointName
            data[i].value = data[i].sid
            data[i].stcdName = data[i].pointName
            data[i].nodeType = 'point'
            const hour24Rainfall = data[i].hour24Rainfall
            let rainstatus = ''
            switch (data[i].sttp) {
              case 'ZZ':
              case 'RR': // 水位
                data[i].src = 'white-rl'
                break
              case 'PP': // 雨量
                data[i].src = 'white-rain'

                if (hour24Rainfall) {
                  if (hour24Rainfall < 10) {
                    rainstatus = '(小雨)'
                  } else if (hour24Rainfall >= 10 && hour24Rainfall < 25) {
                    rainstatus = '(中雨)'
                  } else if (hour24Rainfall >= 25 && hour24Rainfall < 50) {
                    rainstatus = '(大雨)'
                  } else if (hour24Rainfall >= 50 && hour24Rainfall < 100) {
                    rainstatus = '(暴雨)'
                  } else if (hour24Rainfall >= 100 && hour24Rainfall < 250) {
                    rainstatus = '(大暴雨)'
                  } else if (hour24Rainfall >= 250) {
                    rainstatus = '(特大暴雨)'
                  }
                } else {
                  rainstatus = '(无雨)'
                }
                data[i].label += rainstatus
                break
            }
            data[i].pointId = data[i].sid
            data[i].monitoringCategoryName = data[i].sttp === 'PP' ? '雨量' : '水位'
            data[i].isWarn = 0
            switch (data[i].warnType) {
              case 1: // 大于等于
                if (data[i].floodLimit && data[i].sttp !== 'PP' && (Number(data[i].waterLevel) >= Number(data[i].floodLimit))) {
                  data[i].isWarn = 1
                }
                if (data[i].sttp === 'PP' && (Number(data[i].todayRainfall) >= 50)) {
                  data[i].isWarn = 1
                }
                break
              case 2: // 小于等于
                if (data[i].floodLimit && data[i].sttp !== 'PP' && (Number(data[i].waterLevel) <= Number(data[i].floodLimit))) {
                  data[i].isWarn = 1
                }
                if (data[i].sttp === 'PP' && (Number(data[i].todayRainfall) <= Number(data[i].level3))) {
                  data[i].isWarn = 1
                }
                break
            }

            if (this.isFirst) {
              this.stateTypeNum[data[i].isWarn] += 1
            }

            if (this.stateCheck.indexOf(data[i].isWarn) !== -1) {
              this.allSts.push(data[i])
            }
          } else {
            data[i].label = data[i].areaName + '(' + data[i].pointNum + ')'
            data[i].value = data[i].areaCode
          }
          if (Array.isArray(data[i].childList) && Array.isArray(data[i].waterRainPointVoList)) {
            // 获取不同类型工程的数量
            if (this.isFirst) {
              for (const p of data[i].waterRainPointVoList) {
                this.prointTypeNum[p.sttp === 'PP' ? 'PP' : 'RR,ZZ'] += 1
              }
            }
            data[i].children = [...data[i].childList, ...data[i].waterRainPointVoList]
            this.loopFormatTreeData(data[i].children)
          }
        }
      }
    },
    // 在地图设置测站点
    setPointMap() {
      // const measuredPointsLayer = this.$refs.Map.findLayer('measuredPointsLayer')
      // if (measuredPointsLayer) {
      //   measuredPointsLayer.clearOverlays()
      //   this.$refs.Map.removeLayer('measuredPointsLayer')
      // }
      // this.$refs.Map.addMeasuredPoints(this.allSts, 'measuredPointsLayer', this.detail, true)
      // this.$nextTick(() => {
      //   const measuredPointsLayer = this.$refs.Map.findLayer('measuredPointsLayer')
      //   if (this.isSingleProject) {
      //     measuredPointsLayer.zoomCluster(measuredPointsLayer.getSource().getSource().getFeatures())
      //   }
      // })
      this.$emit('setPoint', this.allSts)
    },
    // 获取数据
    loadData() {
      const type = []
      this.pointTypeCheck.forEach(i => {
        if (i === 'RR,ZZ') {
          type.push('RL')
        } else {
          type.push('RAIN')
        }
      })
      queryPointByArea({
        areaCode: this.areaCode,
        pointType: type.join(),
        module: this.className ? 'wr' : 'sc'
      }).then(async res => {
        if (res.status === 200 && res.data.length) {
          this.allSts = []
          if (this.isSingleProject) {
            res.data[0].areaName = '所有测站'
          }
          this.allAreaData = res.data
          this.loopFormatTreeData(res.data)
          this.areaData = res.data
          this.isFirst = false
          this.setPointMap()
        }
      }).catch(e => {
        console.log(e)
      })
    },
    /**
     * 树点击事件
     */
    treeNodeClick(data) {
      this.$emit('treeNodeClick', data)
    }
  }
}
</script>

<style lang="scss" scoped>
.area-point-tree {
  .pro-type-container {
    button {
      flex: 1;
      height: 48px;
      color: #FFFFFF;
      border: none;
      cursor: pointer;
      background-color: transparent;
      background-repeat: no-repeat;
      background-image: linear-gradient(0deg, #0f4898, #022e6b);
      background-size: 100% 100%;
      border: 1px solid #1F7CD6;
      margin-left: 10px;
      padding: 0;
      font-size: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;

      &:nth-child(1) {
        margin-left: 0;
      }

      &.selected {
        border-color: transparent;
        background-image: url('@/assets/score/score-left-tab-bg-sm.png');
      }

      img {
        user-select: none;
        height: 20px;
        margin: 0 10px;
      }
    }
  }

  .pro-type {
    width: 20px;
    height: 20px;
    user-select: none;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: auto 20px;

    &-RL0 {
      background-image: url('@/assets/map/white-rl.png');
    }

    &-RAIN0 {
      background-image: url('@/assets/map/white-rain.png');
    }

    &-RL1 {
      background-image: url('@/assets/map/warn-rl.png');
    }

    &-RAIN1 {
      background-image: url('@/assets/map/warn-rain.png');
    }
  }

  .el-tree {
    height: 0;
    flex: auto;
    overflow-y: auto;
    background-color: transparent;

    .custom-node {
      font-size: 18px;
      font-weight: 500;
      line-height: 42px;
      color: #FFFFFF;

      .custom-icon i {
        margin-right: 12px;
      }

      .custom-icon i[class*="el-icon-"] {
        display: inline-block;
        width: 16px;
        height: 16px;
        color: #80C0FF;
        border: 1px solid #80C0FF;
        margin-right: 12px;
        text-align: center;
        line-height: 16px;
        font-size: 12px;
      }

      .custom-label {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  ::v-deep(.el-tree-node__content) {
    height: auto !important;

    &:hover {
      background-color: rgba(0, 117, 232, 0.6);
    }
  }

  ::v-deep(.el-tree-node__expand-icon) {
    opacity: 0;
    position: absolute;
    width: 0;
    height: 0;
    box-sizing: border-box;
    padding: 0;
  }

  ::v-deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: rgba(0, 117, 232, 0.6);
  }

  &.area-point-tree-two {
    .pro-type-container {
      button {
        height: 72px;
        border: 1px solid #0A4EAD;
        position: relative;
        color: #0A4EAD;
        font-size: 22px;
        flex-wrap: wrap;
        justify-content: flex-start;
        background-image: none;
        background-color: #FFFFFF;

        &.selected {
          border: 1px solid #0A4EAD;
          background-color: transparent;
          background-image: linear-gradient(0deg, #ebf3ff, #c6deff);

          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 22px;
            height: 22px;
            background-size: 22px 22px;
            background-repeat: no-repeat;
            background-image: url('@/assets/score/btn-selected.png');
          }
        }

        .btn-name {
          color: #222F40;
        }

        .btn-num {
          width: 100%;
          font-weight: 700;
          margin-left: 36px;
          text-align: left;
        }
      }
    }

    .pro-type {
      width: 20px;
      height: 20px;
      user-select: none;
      background-position: center center;
      background-repeat: no-repeat;
      background-size: auto 20px;

      &-RL0 {
        background-image: url('@/assets/map/normal-rl.png');
      }

      &-RAIN0 {
        background-image: url('@/assets/map/normal-rain.png');
      }
    }

    .el-tree {
      .custom-node {
        color: #222F40;

        .custom-icon i[class*="el-icon-"] {
          color: #1064DA;
          border: 1px solid #1064DA;
        }
      }
    }

    ::v-deep(.el-tree-node__content) {
      &:hover {
        background-color: #F0F5FC;
      }
    }

    ::v-deep(.el-tree-node.is-current > .el-tree-node__content) {
      background-color: #F0F5FC;
    }

    .el-tree-node.is-current>.el-tree-node__content .custom-node {
      color: #0A4EAD;
    }
  }
}
</style>
