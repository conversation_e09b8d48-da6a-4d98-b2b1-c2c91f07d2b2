/* 报表-大坝安全 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'
const api = {
  gateOpeningList: apiUrl.defaultUrl + '/goc/getGateHeightForms',
  gateOpeningExport: apiUrl.defaultUrl + '/goc/gateHeightDownFormsExcel',
  getGateHeightCharts: apiUrl.defaultUrl + '/goc/getGateHeightCharts'
}

/**
 * 闸门开度列表查询
 */
export function gateOpeningList (data) {
  return $http.get(api.gateOpeningList, data)
}
// 闸门开度 导出
export function gateOpeningExport (params) {
  return $http.getDownLoad(api.gateOpeningExport, params, undefined, 'blob')
}

// 闸门开度报表统计图
export function getGateHeightCharts (params) {
  return $http.get(api.getGateHeightCharts, params)
}
