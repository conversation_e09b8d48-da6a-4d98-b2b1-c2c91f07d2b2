<template>
  <div class="scroll-container">
    <div class="r-type-switch">
      <component :is="rType ? ArrowTitle : 'span'" @click="rType = true" class="toggle-btn"
        >大坝安全责任人</component
      >
      <component :is="rType ? 'span' : ArrowTitle" @click="rType = false" class="toggle-btn"
        >防汛责任人</component
      >
    </div>
    <div class="responsibility-item" v-for="(item, index) in rPerson" :key="item.id">
      <div class="icon-bg">
        <div class="icon-wrap">
          <div class="avatar"></div>
        </div>
      </div>
      <div class="infos">
        <div class="flx-justify-between">
          <div class="title">
            <span class="name">{{ item.pername || '--' }}</span>
            <span class="tag" :style="{ color: RESPONSIBLE_PERSON_TYPE_COLORS[index] }">{{
              formatDict('RESPONSIBLE_PERSON_TYPE', item.pertp || '--')
            }}</span>
          </div>
          <span>电话：{{ item.tel || '--' }}</span>
        </div>
        <div>职务：{{ item.post || '--' }}</div>
        <div>单位：{{ item.unit || '--' }}</div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRequest } from 'alova/client'
import { getResponsibilityInfo } from '@/alova_api/methods/reservoir'
import { useDictionary } from '@/stores/modules/dictionary'
import { RESPONSIBLE_PERSON_TYPE_COLORS } from '../constant'
import ArrowTitle from '@/components/ArrowTitle/index.vue'
const { data: responsibilityInfo } = useRequest(() => getResponsibilityInfo())
const rType = ref(true)
const rPerson = computed(() => {
  const res =
    responsibilityInfo.value?.attWrpPers?.filter(item => {
      const fxType = ['A', 'B', 'C']
      return (
        item.pertp !== 'F' &&
        (rType.value ? !fxType.includes(item.pertp!) : fxType.includes(item.pertp!))
      )
    }) || []
  if (rType.value) {
    // 防汛责任人按 pertp值按GDE排序，G在前，D次之，E最后
    res.sort((a, b) => {
      const pertpOrder = {
        G: 1,
        D: 2,
        E: 3
      }
      return pertpOrder[a.pertp || ''] - pertpOrder[b.pertp || '']
    })
  }
  return res
})
const { formatDict, initDictionary } = useDictionary()
onMounted(() => {
  initDictionary(['RESPONSIBLE_PERSON_TYPE'])
})
</script>

<style lang="scss" scoped>
.scroll-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-left: 18px;
  .r-type-switch {
    display: flex;
    align-items: center;
    gap: 10px;
    .toggle-btn {
      cursor: pointer;
    }
    span {
      font-family: 'YouSheBiaoTiHei';
      // font-size: 18px;
      color: rgba(119, 255, 255, 0.53);
    }
  }
}
.responsibility-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  .icon-bg {
    width: 70px;
    height: 78px;
    background: url('@/assets/images/common/row-bg.png') no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    flex-shrink: 0;
    .icon-wrap {
      width: 56px;
      height: 56px;
      background: url('@/assets/images/comprehensiveSituation/avatar-circle.png') no-repeat center
        center;
      background-size: 100% 100%;
      display: flex;
      margin: auto;
      .avatar {
        width: 49px;
        height: 49px;
        margin: auto;
        background: url('@/assets/images/comprehensiveSituation/avatar.png') no-repeat center center;
        background-size: 100% 100%;
      }
    }
  }
  .infos {
    height: 78px;
    flex: 1;
    padding: 7px;
    box-sizing: border-box;
    background: url('@/assets/images/comprehensiveSituation/man-info-bg.png') no-repeat center
      center;
    background-size: 100% 100%;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-left: 8px;
    .title {
      display: flex;
      align-items: center;
      flex-direction: row;
      .name {
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        color: #7fffff;
      }
      .tag {
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin-left: 15px;
        height: 20px;
        position: relative;
        &::before {
          content: '';
          position: absolute;
          left: -6px;
          right: -6px;
          bottom: 0;
          top: 0;
          background: currentColor;
          opacity: 0.2;
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
