<template>
  <!-- 预览弹窗 -->
  <el-dialog
    :modal="modal"
    :visible="previewVisible"
    top="0"
    :append-to-body="true"
    class="std-theme theme-dialog preview-dialog"
    width="80vw"
    title="预览"
    @close="previewClose"
  >
    <file-preview v-if="showFile" ref="previewRef"></file-preview>
    <div class="file-warning flex-hvc" v-if="showWarning">
      目前不支持该格式的文件预览，请下载至本地预览
    </div>
    <div slot="footer">
      <el-button class="theme-dialog-primary-btn" type="primary" @click="previewClose"
        >关 闭</el-button
      >
      <el-button type="primary" @click="download">下载</el-button>
    </div>
  </el-dialog>
</template>

<script>
/* 打开弹窗查看附件 */
import FilePreview from "@/components/FilePreview/index";
import { downloadFileByBlob } from "@/utils/utils";
import { downLoad, previewUrl } from "@/api/attachment";
export default {
  name: "FilePreviewDialog",
  components: {
    FilePreview,
  },
  data() {
    return {
      modal: true,
      showFile: false,
      showWarning: false,
      previewVisible: false,
      downloadObj: {},
      typeMap: ["docx", "pdf", "gif", "png", "jpeg", "jpg"],
    };
  },
  methods: {
    previewClose() {
      this.showWarning = false;
      this.showFile = false;
      if (this.$refs.previewRef) {
        this.$refs.previewRef.close();
      }
      this.previewVisible = false;
    },
    downloadFile(id, type) {
      return downLoad({ id }, type).catch((e) => {
        console.log(e);
      });
    },
    async download() {
      const resdata = await this.downloadFile(this.downloadObj.id, "arraybuffer");
      downloadFileByBlob(this.downloadObj.mc, resdata);
      this.previewClose();
    },
    async preview(val, modal = true) {
      this.downloadObj = val;
      this.modal = modal;
      this.previewVisible = true;
      const str = val.zwdlx ? val.zwdlx.toLowerCase() : "";
      if (this.typeMap.indexOf(str) !== -1) {
        this.showFile = true;
        this.$nextTick(() => {
          switch (str) {
            case "docx":
              this.downloadFile(val.id, "blob").then((res) => {
                this.$refs.previewRef.initDocx(res);
              });
              break;
            case "pdf":
              this.$refs.previewRef.initPdf(
                val.file_path ? previewUrl + val.file_path : val.fileData
              );
              break;
            case "png":
            case "jpeg":
            case "gif":
            case "jpg":
              this.$refs.previewRef.initImg(
                val.file_path ? previewUrl + val.file_path : val.fileData
              );
              break;
            default:
              this.$refs.previewRef.initNosuport();
          }
        });
      } else {
        this.showWarning = true;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.preview-dialog {
  ::v-deep .el-dialog__body {
    height: 84vh;
  }
  ::-webkit-scrollbar {
    display: block;
  }
}

.file-warning {
  text-align: center;
  font-size: 28px;
  font-weight: 600;
  height: 100%;
}
</style>
