/* 统计分析-纳雨能力 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";
const api = {
  getWeather: apiUrl.defaultUrl + "/receiveRain/getWeather",
  getRainStatistics: apiUrl.defaultUrl + "/receiveRain/getRainStatistics",
  custom: apiUrl.defaultUrl + "/receiveRain/custom",
};

/*
 * 纳雨能力-天气
 * projectId
 */
export function getWeather(data) {
  return $http.post(api.getWeather, data);
}

/**
 * 纳雨能力-基础数据
 */
export function getRainStatistics(data) {
  return $http.post(api.getRainStatistics, data);
}

/**
 * 纳雨能力-自定义场景
 * type 1=汛限水位 2=设计洪水位 3=校核洪水位
 * rain 模拟雨量
 * beginTime 开始时间 yyyy-MM-dd HH:mm:ss
 */
export function custom(data) {
  return $http.post(api.custom, data);
}
