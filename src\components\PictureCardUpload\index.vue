<template>
  <div class="es-projectmsg-item">
    <el-empty v-show="fileList.length === 0 && !allowUpload" class="nodata" description="暂无数据"></el-empty>
    <div class="es-projectmsg-img" v-for="(val, index) of fileList" :key="val.id">
      <img @click="handlePicturePreview(val)" :src="val.url ? val.url : previewUrl + val.file_path" />
      <button v-show="allowUpload" type="button" @click="delImg(index)" class="es-projectmsg-img-del"></button>
      <el-progress class="es-projectmsg-img-progress" v-if="val.progressFlag" :show-text="false" :text-inside="true" :percentage="val.loadProgress"></el-progress>
    </div>
    <el-upload
      class="es-projectmsg-imglist-upload stdClass"
      :multiple="computedMultiple"
      action="#"
      ref="fileList"
      :disabled="!allowUpload"
      v-show="allowUpload && (!limit || fileList.length < limit)"
      list-type="picture-card"
      :auto-upload="false"
      :file-list="fileList"
      :on-change="(file, fileList) => handleFilechange(file, fileList)"
      :on-progress="(event, file, fileList) => onUploadFileProcess(event, file, fileList)"
      :on-success="(res, file, fileList) => handleFileSuccess(file, fileList)"
      :http-request="fileUpload"
      :show-file-list="false">
      <i class="el-icon-plus es-projectmsg-imglist-upload-i"></i>
    </el-upload>
  </div>
</template>

<script>
/* 基础信息——图片上传——缩略图形式 */
import { upload, previewUrl } from '@/api/attachment'
export default {
  name: 'PictureCardUpload',
  props: {
    fileList: {
      type: Array,
      default: () => []
    },
    allowUpload: {
      type: Boolean,
      default: true
    },
    module: {
      type: String,
      default: 'common' // 默认值为 'common'
    },
    limit: {
      type: Number,
      default: null // 默认无限制
    },
    multiple: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      previewUrl
    }
  },
  computed: {
    computedMultiple() {
      if (this.limit === 1) {
        return false
      }
      return this.multiple
    }
  },
  methods: {
    /**
     * 图片预览
    */
    handlePicturePreview (file) {
      this.$emit('filePreview', file)
    },
    /**
     * 附件列表变更的时候触发的回调函数，附件类型及大小的检测
    */
    handleFilechange (file, fileList) {
      const TYPE = ['jpg', 'png', 'jpeg']
      const BOOL = TYPE.some(val => file.raw.type.indexOf(val) !== -1)
      const isLtdFileSize = file.size / 1024 / 1024 <= 50

      if (!BOOL) {
        this.$message.error('上传附件只能是' + TYPE.join('、') + '格式!')
      }
      if (!isLtdFileSize) {
        this.$message.error('上传图片大小不能超过50M!')
      }
      if (!BOOL || !isLtdFileSize) {
        const currIdx = fileList.indexOf(file)
        fileList.splice(currIdx, 1)
      } else {
        // 如果设置了限制且当前文件数量已达到限制，则替换第一个文件
        if (this.limit && this.fileList.length >= this.limit) {
          // 清空现有文件列表，只保留新上传的文件
          const newFileList = [file]
          this.$emit('update:fileList', newFileList)
          if (file.status === 'ready') {
            this.$refs.fileList.submit()
          }
          return
        }

        if (file.status === 'ready') {
          this.$refs.fileList.submit()
        }
      }
      for (const f in fileList) {
        if (file.name === fileList[f].name) {
          fileList[f] = {
            ...fileList[f],
            ...fileList[f].response
          }
        }
      }
      this.$emit('update:fileList', fileList)
    },
    /**
     * upload组件的onProcess钩子的回调函数
    */
    onUploadFileProcess (e, file, fileList) {
      for (const val of fileList) {
        if (val.name === file.name) {
          val.loadProgress = e.percent
          if (e.percent !== 100) {
            val.progressFlag = true
          }
          break
        }
      }
      this.$emit('update:fileList', fileList)
    },
    /**
     * axios上传时onUploadProgress回调函数，获取文件上传进度，并触发上传时传递的file对象中的onProgress钩子，将文件上传进度传递至upload组件
    */
    uploadFileProcess (event, file) {
      const percent = event.loaded / event.total * 100 // 动态获取文件上传进度
      file.onProgress({ percent })
    },
    /**
     * upload组件的附件上传成功时的钩子，隐藏进度条
    */
    handleFileSuccess (file, fileList) {
      for (const f in fileList) {
        if (file.name === fileList[f].name) {
          fileList[f].progressFlag = false
          break
        }
      }
      this.$emit('update:fileList', fileList)
      this.$message.success('上传成功')
    },
    /**
     * upload组件的自定义上传方法
    */
    fileUpload (file) {
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('module', this.module)
      upload(formData, e => { this.uploadFileProcess(e, file) }).then(res => {
        if (res.status === 200) {
          // 延时触发upload组件的onSuccess钩子，为了显示更好的进度条效果
          setTimeout(() => {
            file.onSuccess(res.data)
          }, 500)
        }
      }).catch(e => {
        console.log(e)
        this.$message.error('上传失败，请稍后再试')
      })
    },
    /**
     * 删除图片
    */
    delImg (index) {
      const LIST = JSON.parse(JSON.stringify(this.fileList))
      LIST.splice(index, 1)
      this.$emit('update:fileList', LIST)
    }
  }
}
</script>

<style lang="scss" scoped>
.nodata {
  text-align: center;
  flex: 1;
}

.es-projectmsg-item {
  margin: 0 0 40px 0;
  position: relative;
  display: flex;
  flex-wrap: wrap;
}

.es-projectmsg-img {
  width: 157px;
  height: 146px;
  position: relative;
  margin-right: 20px;
  margin-bottom: 20px;
  border: 1px solid #ccc;
  border-radius: 6px;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  .es-projectmsg-img-del {
    width: 32px;
    height: 32px;
    background: url('@/assets/basic/del.png') no-repeat;
    background-size: 100% 100%;
    position: absolute;
    top: 0;
    right: 0;
    z-index: 1;
    border: none;
    cursor: pointer;
    border-radius: 50%;
    background-color: #ccc;
    display: none;

    &:hover, &:active {
      background-image: url('@/assets/basic/delHover.png');
    }
  }

  .es-projectmsg-img-progress {
    position: absolute;
    bottom: 10px;
    left: 10px;
    right: 10px;
  }

  &:hover {
    .es-projectmsg-img-del {
      display: block;
    }
  }
}

.es-projectmsg-imglist-upload-i {
  font-size: 40px;
  color: #1064DA;
  font-weight: 700;
}
</style>
