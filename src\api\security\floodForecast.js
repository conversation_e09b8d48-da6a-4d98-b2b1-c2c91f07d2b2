/* 安全管理-洪水预报 */
import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";
const api = {
  findRainByCaiYun: apiUrl.defaultUrl + "/wlf/findRainByCaiYun",
  findSwByCaiYun: apiUrl.defaultUrl + "/wlf/findSwByCaiYun",
};

/**
 * 洪水预报-雨量
 */
export function findRainByCaiYun(params) {
  return $http.get(api.findRainByCaiYun, params);
}

/**
 * 洪水预报-水位
 */
export function findSwByCaiYun(params) {
  return $http.get(api.findSwByCaiYun, params);
}
