<template>
  <div class="theme-file-preview file-preview-container">
    <iframe
      v-if="type === 'pdf'"
      class="file-preview-content"
      :src="pdfUrl"
      frameborder="0"
    ></iframe>
    <div v-show="type === 'docx'" class="file-preview-content-docx" ref="docx"></div>
    <div v-if="type === 'img'" class="file-preview-content-img">
      <el-image
        class="custom-img"
        fit="contain"
        :src="imgUrl"
        :preview-src-list="[imgUrl]"
      >
      </el-image>
    </div>
    <div v-if="type === 'nosuport'" class="file-preview-content-nosuport">
      暂不支持该格式的文件预览
    </div>
  </div>
</template>

<script>
import { renderAsync } from "docx-preview";
export default {
  name: "FilePreview",
  data() {
    return {
      type: "",
      pdfUrl: "",
      imgUrl: "",
    };
  },
  methods: {
    initPdf(url) {
      this.type = "pdf";
      this.pdfUrl =
        window.location.origin +
        process.env.VUE_APP_PUBLIC_PATH +
        "/pdf/web/viewer.html?file=" +
        encodeURIComponent(url);

      console.log(this.pdfUrl, "pdfUrl", process.env.VUE_APP_PUBLIC_PATH);
    },
    initDocx(res) {
      this.type = "docx";
      renderAsync(res, this.$refs.docx);
    },
    initImg(url) {
      this.type = "img";
      this.imgUrl = url;
    },
    initNosuport() {
      this.type = "nosuport";
    },
    close() {
      this.type = "";
      this.pdfUrl = "";
      this.imgUrl = "";
    },
  },
};
</script>

<style lang="scss" scoped>
.file-preview-container {
  width: 100%;
  height: 100%;
  .file-preview-content {
    width: 100%;
    height: 100%;
  }
  .file-preview-content-docx {
    width: 100%;
    height: 100%;
    overflow-y: auto;
  }
  .file-preview-content-img {
    width: 100%;
    height: 100%;
    // overflow-y: auto;
    // overflow-x: auto;
    text-align: center;
    .custom-img {
      width: 100%;
      height: 100%;
    }
  }
  .file-preview-content-nosuport {
    color: #ffffff;
    font-size: 24px;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
