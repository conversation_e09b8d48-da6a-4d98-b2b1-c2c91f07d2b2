<template>
  <div class="searchForm">
    <el-form
      :id="formConfig.id"
      ref="form"
      :class="[formConfig.className, 'stdClass']"
      :label-width="formConfig.labelWidth || ''"
      :size="formConfig.size || 'medium'"
      :rules="formConfig.rules"
      :model="formData"
      :inline="true"
    >
      <template v-for="(item, index) in getConfigList()">
        <el-form-item
          :key="index"
          :prop="item.value"
          :label="item.label"
          :label-width="item.labelWidth"
          :class="item.className"
          v-if="!item.isShow"
          :style="{
            'flex-grow':
              getConfigList() && getConfigList().length - 1 === index ? 1 : 0,
            'margin-right':
              getConfigList() && getConfigList().length - 1 === index
                ? '0px'
                : '20px',
          }"
        >
          <template v-if="item.type === 'slot'">
            <slot :name="'form-' + item.value" />
          </template>
          <component
            :is="item.type"
            v-model="formData[item.value]"
            :options="listTypeInfo[item.list]"
            :type="item.dataType"
            :picker-options="item.timePickerOptions"
            :clearable="item.clearable"
            :disabled="item.disabled"
            :start-placeholder="item.startPlaceholder"
            :end-placeholder="item.endPlaceholder"
            :range-separator="item.rangeSeparator || '-'"
            :filterable="item.filterable"
            :multiple="item.multiple"
            :collapse-tags="item.collapseTags"
            default-first-option
            :allow-create="item.allowCreate"
            :no-data-text="item.noDataText || ''"
            :placeholder="getPlaceholder(item)"
            :value-format="item.valueFormat"
            :format="item.format"
            :default-time="item.defaultTime"
            :style="{ width: item.width || '100%' }"
            :maxlength="item.maxLength"
            :autosize="item.autosize || { minRows: 2, maxRows: 10 }"
            :popper-class="item.popperClass"
            @click.native="
              handleEvent(item.event, formData[item.value], 'click')
            "
            @change="
              handleEvent(
                item.event,
                formData[item.value],
                'change',
                item.value
              )
            "
            @input="
              handleEvent(item.event, formData[item.value], 'input', item.value)
            "
            @blur="handleEvent(item.event, formData[item.value], 'blur')"
          >
            <template v-if="item.type === 'el-select'">
              <el-option
                v-for="(childItem, childIndex) in listTypeInfo[item.list]"
                :key="`${childItem.value}${childIndex}`"
                :label="childItem.label"
                :value="childItem.value"
              />
            </template>
            <template v-if="item.type === 'el-radio-group'">
              <el-radio
                v-for="(childItem, childIndex) in listTypeInfo[item.list]"
                :key="childIndex"
                :label="childItem.value"
              >
                {{ childItem.label }}
              </el-radio>
            </template>
            <!-- {{ item.title || "" }} -->
          </component>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>
<script>
export default {
  name: "FormItemComp",
  props: {
    // 表单配置项
    formConfig: {
      type: Object,
      default: () => {},
    },
    // 表单数据
    data: { type: Object },
    // 下拉选项list
    listTypeInfo: { type: Object },
    // ref
    refObj: { type: Object },
  },
  data() {
    return {
      formData: {},
    };
  },
  watch: {
    data: {
      handler(val) {
        this.updateFormData(val);
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    this.formData = this.data;
    this.$nextTick(() => {
      // const nodes = this.$refs.form.$el.querySelectorAll(".el-form-item");
      // const last = nodes[nodes.length - 1];
      // last.querySelector('.el-form-item__content').style.width = '100%'
    });
    // 将form实例返回到父级
    this.$emit("update:refObj", this.$refs.form);
  },
  methods: {
    updateFormData(val) {
      if (!val) return;
      this.formData = val;
      // 将form实例返回到父级
      this.$emit("update:refObj", this.$refs.form);
    },
    // 获取字段列表
    getConfigList() {
      return this.formConfig.fieldList.filter(
        (item) =>
          !Object.prototype.hasOwnProperty.call(item, "show") ||
          (Object.prototype.hasOwnProperty.call(item, "show") && item.show)
      );
    },
    // 得到placeholder的显示
    getPlaceholder(row) {
      let placeholder;
      const types = ["el-select", "el-date-picker", "el-cascader"];
      if (row.type === "el-input") {
        placeholder = `请输入${row.placeholder || ""}`;
      } else if (types.includes(row.type)) {
        placeholder = `请选择${row.placeholder || ""}`;
      } else {
        placeholder = row.label;
      }
      return placeholder;
    },
    // 表单事件派发
    handleEvent(event, data, type, prop) {
      this.$emit("handleEvent", { event, data, type, prop });
    },
    // 重置表单
    resetForm() {
      this.$refs.form.resetFields();
    },
  },
};
</script>
<style lang="scss" scoped>
.searchForm {
  display: flex;
  padding-top: 15px;

  .el-form {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .el-form-item {
      display: flex;
      align-items: center;
    }
  }
}
</style>
