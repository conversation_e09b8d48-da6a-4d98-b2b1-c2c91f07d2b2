/* 基础管理-工程状况 */

import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getProjectImage: apiUrl.defaultUrl + '/scoreProjectImage/details',
  saveProjectImage: apiUrl.defaultUrl + '/scoreProjectImage/save',
  selectProjectList: apiUrl.defaultUrl + '/scoreProject/selectProjectList',
  querySortByProjectType: apiUrl.defaultUrl + '/scoreProject/querySortByProjectType',
  getSbtzOverview: apiUrl.defaultUrl + '/moreProject/getSbtzOverview',
  querySortByProjectTypeV2: apiUrl.defaultUrl + '/scoreProject/queryEvaluation'
}

/**
 * 工程状况-工程面貌查询
 */
export function getProjectImage (params) {
  return $http.get(api.getProjectImage, params)
}

/**
 * 工程状况-工程面貌保存
 * "greeningRate": 0, 工程绿化率
 * "greeningRateFile": "string", 工程绿化率-文件id，多个用逗号隔开
 * "greeningRateTime": "2023-08-14", 工程绿化率-修改时间
 * "id": 0, 主键
 * "litter": 0, 环境垃圾
 * "litterFile": "string", 环境垃圾-文件id，多个用逗号隔开
 * "litterTime": "2023-08-14", 环境垃圾-修改时间
 * "pollutionSource": 0, 污染源
 * "pollutionSourceFile": "string", 污染源-文件id，多个用逗号隔开
 * "pollutionSourceTime": "2023-08-14", 污染源-修改时间
 * "projectId": 0, 工程id
 * "projectImage": 0, 工程形象面貌
 * "projectImageFile": "string", 工程形象面貌-文件id，多个用逗号隔开
 * "projectImageTime": "2023-08-14", 工程形象面貌-修改时间
 * "soilErosion": 0, 水土流失
 * "soilErosionFile": "string", 水土流失-文件id，多个用逗号隔开
 * "soilErosionTime": "2023-08-14", 水土流失-修改时间
 * "sundries": 0, 杂物堆放
 * "sundriesFile": "string", 杂物堆放-文件id，多个用逗号隔开
 * "sundriesTime": "2023-08-14", 杂物堆放-修改时间
 * "weed": 0, 杂草丛生
 * "weedFile": "string", 杂草丛生-文件id，多个用逗号隔开
 * "weedTime": "2023-08-14" 杂草丛生-修改时间
 */
export function saveProjectImage (data) {
  return $http.post(api.saveProjectImage, data)
}

/**
 * 工程信息查询-分页查询
 * "areaCode": "string",
 * "projectName": "string",
 * "projectScale": 0,
 * "projectType": "string"
 */
export function selectProjectList (data) {
  return $http.post(api.selectProjectList, data)
}

/**
 * 工程信息查询-导航栏工程查询
 * "areaCode": "string",
 * "projectName": "string",
 * "projectScale": 0,
 * "projectType": "string"
 */
export function querySortByProjectType (data) {
  return $http.post(api.querySortByProjectType, data)
}

export function querySortByProjectTypeV2 (data) {
  return $http.post(api.querySortByProjectTypeV2, data)
}

/**
 * 多工程设备台账总览--省市区用户
 *
 */
export function getSbtzOverview (data) {
  return $http.post(api.getSbtzOverview, data)
}
