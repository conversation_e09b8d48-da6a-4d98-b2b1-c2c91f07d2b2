import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getPageList: apiUrl.defaultUrl + '/receiveRain/getPageList',
  getRainPowerList: apiUrl.defaultUrl + '/receiveRain/getRainPowerList',
}

/**
 * 统计分析-纳雨能力分析
 * @param data
 */
// 1、纳雨能力分析
export function getRainPowerList (data) {
  return $http.post(api.getRainPowerList, data)
}
export function getPageList (data) {
  return $http.post(api.getPageList + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}
