import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  login: apiUrl.defaultUrl + '/auth/login',
  refresh: apiUrl.defaultUrl + '/auth/refresh',
  userInfo: apiUrl.defaultUrl + '/auth/user-info',
  getCaptcha: apiUrl.defaultUrl + '/auth/getCaptcha',
  resetPassword: apiUrl.defaultUrl + '/user/reset-password'
}

/**
 * 用户登录
 * "captcha": "string",
 * "codeId": "string",
 * "password": "string",
 * "username": "string"
 */
export function login (data) {
  return $http.post(api.login, data)
}

/**
 * 获取用户信息接口
 * "areaCode": "string",
  "projectName": "string",
  "projectScale": 0,
  "projectType": 0
 */
export function userInfo (isHide) {
  return $http.post(api.userInfo, '', isHide)
}

/**
 * 刷新token接口
 */
export function refresh (data) {
  return $http.post(api.refresh, data)
}

/**
 * 获取验证码接口
 */
export function getCaptcha (params) {
  return $http.get(api.getCaptcha, params)
}

/**
 * 用户修改密码接口
 */
export function resetPassword (data) {
  return $http.post(api.resetPassword, data)
}
