<template>
  <!-- 自定义卡片组件 -->
  <div class="cs-card">
    <div class="cs-card-header">
      <slot name="header-prepend">
        <div class="header-text">{{ title }}</div>
      </slot>
      <slot name="header-append"></slot>
    </div>

    <CustomDivider class="cs-card-divider" />

    <div class="cs-card-body">
      <slot></slot>
    </div>
  </div>
</template>

<script>
import CustomDivider from "./CustomDivider";
export default {
  name: "CustomCard",
  components: { CustomDivider },
  props: {
    title: {
      type: String,
      default: "",
    },
  },
};
</script>

<style lang="scss" scoped>
$color-border: #afc4e2;
.cs-card {
  border: 1px solid $color-border;
}

.cs-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 18px;
  box-sizing: border-box;
  height: 54px;
  background: url("@/assets/basic/titleBg.png") no-repeat;
  background-size: 400px 100%;

  .header-text {
    font-weight: 700;
    font-size: 22px;
    color: $color-primary;
  }
}

.cs-card-divider {
  margin: 0 18px;
}

.cs-card-body {
  padding: 22px;
  box-sizing: border-box;
}
</style>
