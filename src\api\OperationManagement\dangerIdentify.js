/* 运行管护-危险源识别 */
import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  pageList: apiUrl.defaultUrl + '/control/dangerIdentify/page',
  add: apiUrl.defaultUrl + '/control/dangerIdentify/add',
  edit: apiUrl.defaultUrl + '/control/dangerIdentify/edit',
  deleteById: apiUrl.defaultUrl + '/control/dangerIdentify/deleteById',
  exportList: apiUrl.defaultUrl + '/control/dangerIdentify/export'
}

// 分页查询
export function pageList(data) {
  return $http.post(api.pageList, data)
}

// 新增
export function add(data) {
  return $http.post(api.add, data)
}

// 修改
export function edit(data) {
  return $http.post(api.edit, data)
}

// 删除
export function deleteById(data) {
  return $http.post(api.deleteById, data)
}

// 导出
export function exportList(data, contentType, responseType) {
  return $http.postDownLoad(api.exportList, data, contentType, responseType, false, true)
}
