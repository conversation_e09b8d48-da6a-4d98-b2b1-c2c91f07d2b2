<!-- 水位库容曲线 -->
<template>
  <div class="sk-curves">
    <div class="sk-curves-left">
      <div class="sk-curves-left-header">
        <span class="sk-curves-left-header-title">水位库容曲线</span>
        <el-button type="primary" icon="el-icon-plus" @click="addEdit('add')">新增</el-button>
      </div>
      <!-- showPagination
        paginationPosition="bottom"
        :total="pagination.total"
        :current-page.sync="pagination.page"
        @page-change="loadProject" -->
      <free-table
        class="main-table"
        height="100%"
        :data="tableData"
        :column="tableColumn"
        background
      >
        <template #actions="{ row }">
          <el-button type="text" icon="el-icon-edit-outline" @click="addEdit('edit', row)">
            编辑
          </el-button>
          <el-button type="text" icon="el-icon-delete" @click="handleDelete(row)">
            删除
          </el-button>
        </template>
      </free-table>
    </div>
    <div id="sk-curves-echarts" class="sk-curves-echarts"></div>

    <curves-dialog ref="curvesDialog" @refresh="loadData" ></curves-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getStorage } from '@/utils/storage'
import FreeTable from '@/components/elCommon/freeTable'
import CurvesDialog from './CurvesDialog'
import { getCurveList, deleteCurve } from '@/api/basic/reservoirProjectData.js'
export default {
  name: 'Curves',
  components: { FreeTable, CurvesDialog },
  props: {},
  data () {
    return {
      tableData: [{ address: '111' }, { address2: '111' }, { address3: '111' }, { address: '111' }],
      pagination: {
        total: 0,
        page: 1,
        limit: 10
      },
      tableColumn: [
        {
          type: 'index',
          label: '序号',
          // fixed: 'left',
          width: 55
        },
        {
          label: '水位(m)',
          prop: 'wl'
        },
        {
          label: '库容(万m³)',
          prop: 'stcp'
        },
        {
          label: '水面面积(万㎡)',
          prop: 'ar'
        },
        {
          label: '操作',
          prop: 'actions',
          width: 140,
          slotScope: true
        }
      ]
    }
  },
  computed: {
    ...mapState({
      projectInfo: (state) => state.project.projectInfo
    })
  },
  watch: {},
  created () {
    this.loadData()
  },
  mounted () {},
  methods: {
    loadData () {
      getCurveList({
        wrpcd: this.projectInfo.wrpcd
      }).then(res => {
        if(res.status === 200) {
          this.tableData = res.data
          this.drawChart()
        }
      })
    },
    // 新增/编辑
    addEdit (type, row = {}) {
      this.$refs.curvesDialog.show(type, row)
    },
    // 删除
    handleDelete (row) {
      this.$confirm('是否删除选择数据?', '提示', {
        type: 'warning'
      }).then(() => {
        deleteCurve({
          id: row.id
        }).then(res => {
          if (res.status === 200) {
            this.$message.success('删除成功！')
            this.loadData()
          }
        })
      })
    },
    drawChart (data) {
      const chartBox = this.$echarts.init(document.getElementById('sk-curves-echarts'))
      console.log('tableData', this.tableData);
      const xData = []
      const yData = this.tableData.map(item => {
        xData.push(item.stcp)
        return item.wl
      })
      const option = {
        tooltip: {
          trigger: 'axis',
          formatter: (params) => {
            console.log('params', params)
            return `水位：${params[0].value}(m)<br/>库容：${params[0].axisValue}(万m³)`
          }
        },
        grid: {
          left: '10%',
          top: '15%',
          right: '5%',
          bottom: '15%'
        },
        xAxis: {
          name: '库容(万m³)',
          nameLocation: 'center',
          nameTextStyle: {
            padding: [8, 0, 0, 0]
          },
          type: 'category',
          boundaryGap: false,
          data: xData
        },
        yAxis: {
          name: '水位(m)',
          type: 'value',
          axisLine: {
            show: true
          },
          max: Math.ceil(Math.max(...yData)) + 1,
          min: Math.floor(Math.min(...yData)) - 1
        },
        series: [
          {
            data: yData,
            type: 'line',
            smooth: true
          }
        ]
      }
      // chartBox.clear()
      chartBox.setOption(option)
      window.addEventListener('resize', function () {
        chartBox.resize()
      })
    },
    loadProject () {}
  }
}
</script>
<style lang="scss" scoped>
.sk-curves {
  display: flex;
  padding: 20px;
  margin-top: 20px;
  height: 400px;
  .sk-curves-left {
    width: 50%;
    height: 100%;
  }
  .sk-curves-left-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
  .sk-curves-left-header-title {
    font-size: 18px;
    font-weight: 700;
  }
  .sk-curves-echarts {
    width: 50%;
    height: 100%;
  }
}
</style>
