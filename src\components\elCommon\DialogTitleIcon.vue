<template>
  <span class="c-dialog-title-icon">
    <img v-if="iconUrl" class="dialog-img" :src="iconUrl" alt="" />
    <i v-else class="dialog-icon" :class="elIcon"></i>
    <span class="dialog-title" :style="{ color: titleColor }">{{ title }}</span>
  </span>
</template>

<script>
export default {
  name: 'DialogTitleIcon',
  components: {},
  props: {
    // element图标
    elIcon: {
      type: String,
      default: 'el-icon-info'
    },
    // 自定义图标
    iconUrl: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '标题'
    },
    titleColor: {
      type: String
    }
  }
}
</script>

<style lang="scss" scoped>
.c-dialog-title-icon {
  display: flex;
  align-items: center;
  .dialog-icon {
    color: #575757;
    margin-right: 8px;
    font-size: 20px;
  }
  .dialog-img {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
  .dialog-title {
    color: #fff;
    font-size: 22px;
    font-weight: 700;
  }
}
</style>
