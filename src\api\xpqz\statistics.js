import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getEngineering: apiUrl.defaultUrl + '/xpqz/statistics/getEngineering',
  getFlow: apiUrl.defaultUrl + '/xpqz/statistics/getFlow',
  getHidedanger: apiUrl.defaultUrl + '/xpqz/statistics/getHidedanger',
  getRain: apiUrl.defaultUrl + '/xpqz/statistics/getRain',
  getWaterLevel: apiUrl.defaultUrl + '/xpqz/statistics/getWaterLevel',
  getReportList: apiUrl.defaultUrl + '/xpqz/statistics/getReportList',
  waterAndFlowReportExport:
    apiUrl.defaultUrl + '/xpqz/statistics/waterAndFlowReportExport',
  waterAndFlowReportList:
    apiUrl.defaultUrl + '/xpqz/statistics/waterAndFlowReportList'
}

/**
 * 工程巡查接口
 * @param data
 */
export function getEngineering (params) {
  return $http.postParams(api.getEngineering, '', params, '', true)
}

/**
 * 报表-昨天平均流量接口
 */
export function getFlow (data) {
  return $http.post(api.getFlow, data, true)
}

/**
 * 隐患接口
 */
export function getHidedanger (params) {
  return $http.postParams(api.getHidedanger, '', params, '', true)
}

/**
 * 雨量图接口
 */
export function getRain (params) {
  return $http.postParams(api.getRain, '', params, true)
}

/**
 * 水位图接口
 */
export function getWaterLevel (params) {
  return $http.postParams(api.getWaterLevel, '', params, true)
}

/**
 * 获取报表列表接口
 */
export function getReportList () {
  return $http.get(api.getReportList)
}

/**
 * 水位及流量日报表-下载接口
 */
export function waterAndFlowReportExport (data) {
  return $http.postDownLoadByParams(
    api.waterAndFlowReportExport,
    data,
    '',
    'blob',
    true
  )
}

/**
 * 水位及流量日报表-查看列表（无分页）接口
 */
export function waterAndFlowReportList (params) {
  return $http.postParams(api.waterAndFlowReportList, '', params, '', true)
}
