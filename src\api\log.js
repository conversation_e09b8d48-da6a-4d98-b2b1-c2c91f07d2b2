import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getLoginLogList: apiUrl.defaultUrl + '/log/getLoginLogList',
  getLogList: apiUrl.defaultUrl + '/log/getLogList',
  deleteLoginLog: apiUrl.defaultUrl + '/log/deleteLoginLog',
  deleteLog: apiUrl.defaultUrl + '/log/deleteLog'
}

/**
 * 登录日志列表接口
 * @param data
 */
export function getLoginLogList (data) {
  return $http.post(api.getLoginLogList, data)
}

/**
 * 操作日志列表接口
 */
export function getLogList (data) {
  return $http.post(api.getLogList, data)
}

/**
 * 删除登录日志接口
 */
export function deleteLoginLog (data) {
  return $http.post(api.deleteLoginLog, data)
}

/**
 * 删除操作日志接口
 */
export function deleteLog (data) {
  return $http.post(api.deleteLog, data)
}
