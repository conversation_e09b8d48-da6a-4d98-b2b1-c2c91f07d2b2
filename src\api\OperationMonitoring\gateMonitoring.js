import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getZmReal: apiUrl.defaultUrl + '/skmnt/getZmReal',
  getGateMonitoring: apiUrl.defaultUrl + '/skmnt/getGateMonitoring',
  getZmHistory: apiUrl.defaultUrl + '/skmnt/getZmHistory',
  zmExcel: apiUrl.defaultUrl + '/skmnt/zmExcel',
  getProjectGateMonitoringPage: apiUrl.defaultUrl + '/moreProjectMonitor/getProjectGateMonitoringPage',
  queryProjectTreeOfGate: apiUrl.defaultUrl + '/moreProjectMonitor/queryProjectTreeOfGate',
  getGateProjectOverviewVo: apiUrl.defaultUrl + '/moreProjectMonitor/getGateProjectOverviewVo',
}

/**
 * 运行监控-闸门监视接口
 * @param data
 */
// 1、实时监测
export function getZmReal (data) {
  return $http.post(api.getZmReal, data)
}
// 2、统计数据
export function getGateMonitoring (data) {
  return $http.post(api.getGateMonitoring, data)
}
// 3、历史数据
export function getZmHistory (data) {
  return $http.post(api.getZmHistory + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}
// 4、历史数据导出
export function zmExcel (data,contentType, responseType) {
  return $http.postDownLoad(api.zmExcel, data,  contentType, responseType)
}
// 多工程运行监控-闸门监视列表
export function getProjectGateMonitoringPage (data) {
  return $http.post(api.getProjectGateMonitoringPage, data)
}
// 多工程运行监控-闸门工程树 工程类型只有水库和水闸
export function queryProjectTreeOfGate (data) {
  return $http.post(api.queryProjectTreeOfGate, data)
}
// 多工程闸门监测总览--省市区用户
export function getGateProjectOverviewVo (data) {
  return $http.post(api.getGateProjectOverviewVo, data)
}
