import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  getDamList: apiUrl.defaultUrl + '/skmnt/getDamList',
  getDamMntHistory: apiUrl.defaultUrl + '/skmnt/getDamMntHistory',
  getDamMntAllList: apiUrl.defaultUrl + '/skmnt/getDamMntAllList',
  getDamSafetyClassify: apiUrl.defaultUrl + '/skdsc/getDamSafetyClassify',
}

/**
 * 运行监控-安全检测接口
 * @param data
 */
// 测站类型
export function getDamList (data) {
  return $http.get(api.getDamList, data)
}
export function getDamMntHistory (data) {
  return $http.post(api.getDamMntHistory + `?pageNum=${data.pageNum}&pageSize=${data.pageSize}`, data)
}
export function getDamMntAllList (data) {
  return $http.post(api.getDamMntAllList, data)
}
export function getDamSafetyClassify (data) {
  return $http.get(api.getDamSafetyClassify, data)
}
