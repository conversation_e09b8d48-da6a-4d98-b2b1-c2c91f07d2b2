import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  projectList: apiUrl.defaultUrl + '/holisticScore/projectList',
  scoreRanking: apiUrl.defaultUrl + '/holisticScore/scoreRanking',
  queryRainList: apiUrl.defaultUrl + '/holisticScore/queryRainList',
  queryWaterList: apiUrl.defaultUrl + '/holisticScore/queryWaterList',
  setPointStatus: apiUrl.defaultUrl + '/holisticScore/setPointStatus',
  getProjectPointList: apiUrl.defaultUrl + '/holisticScore/getProjectPointList',
  levelChart: apiUrl.defaultUrl + '/holisticScore/levelChart',
  countByProject: apiUrl.defaultUrl + '/holisticScore/countByProject',
  scoreAndProjectRanking: apiUrl.defaultUrl + '/holisticScore/scoreAndProjectRanking',
  getSoreChartAndOtherVo: apiUrl.defaultUrl + '/holisticScore/getSoreChartAndOtherVo',
  getOnlyProjectHomePage: apiUrl.defaultUrl + '/holisticScore/getOnlyProjectHomePage',
  getDamSafetyCount: apiUrl.defaultUrl + '/dsc/getDamSafetyCount'
}

/**
 * 综合评分-评分详细信息列表查询
 * "areaCode": "string",
  "projectName": "string",
  "projectScale": 0,
  "projectType": 0,
  "level": "string" // 等级
  "orderBy": "string" // 排序 ASC-升序，DESC-降序，默认DESC
 */
export function projectList (data, params) {
  return $http.postParams(api.projectList, data, params)
}

/**
 * 综合评分-评分排名查询
 * "areaCode": "string",
  "projectName": "string",
  "projectScale": 0,
  "projectType": 0
 */
export function scoreRanking (data, params) {
  return $http.postParams(api.scoreRanking, data, params)
}

/**
 * 重要站点-查询工程水雨站点列表
 * "pageNum": 0,
 * "pageSize": 0,
 * "pointType": "string", // 站点类型：RL-水位，RAIN-雨量
 * "projectId": 0
*/
export function getProjectPointList (data) {
  return $http.post(api.getProjectPointList, data, true)
}

/**
 * 重要站点-查询重要雨量站点
 * "pageNum": 0,
 * "pageSize": 0,
 * "pointType": "string", // 站点类型：RL-水位，RAIN-雨量
 * "projectId": 0
*/
export function queryRainList (data) {
  return $http.post(api.queryRainList, data, true)
}

/**
 * 重要站点-查询重要水位站点
 * "pageNum": 0,
 * "pageSize": 0,
 * "pointType": "string", // 站点类型：RL-水位，RAIN-雨量
 * "projectId": 0
*/
export function queryWaterList (data) {
  return $http.post(api.queryWaterList, data, true)
}

/**
 * 重要站点-设置重要站点
 * "id": 0,
 * "itFlag": false // 当前状态
*/
export function setPointStatus (data) {
  return $http.post(api.setPointStatus, data)
}

/**
 * 综合评分-工程级别图表统计
 * "areaCode": "string", // 行政区划
 * "projectName": "string", // 工程名称
 * "projectScale": 0, // 工程规模
 * "projectType": "string" // 工程类型，1-水库，2-水闸，3-堤防
*/
export function levelChart (data) {
  return $http.post(api.levelChart, data)
}

/**
* 综合评分-工程数量统计
* "areaCode": "string", // 行政区划
* "projectName": "string", // 工程名称
* "projectScale": 0, // 工程规模
* "projectType": "string" // 工程类型，1-水库，2-水闸，3-堤防
* "level": "string" // 等级
* "orderBy": "string" // 正序、倒序
*/
export function countByProject (data) {
  return $http.post(api.countByProject, data)
}

/**
* 综合评分-评分和项目排名查询
* "areaCode": "string", // 行政区划
* "projectName": "string", // 工程名称
* "projectScale": 0, // 工程规模
* "projectType": "string" // 工程类型，1-水库，2-水闸，3-堤防
* "level": "string" // 等级
* "orderBy": "string" // 正序、倒序
*/
export function scoreAndProjectRanking (data) {
  return $http.post(api.scoreAndProjectRanking, data)
}

/**
* 综合评分-图表数据查询
*/
export function getSoreChartAndOtherVo () {
  return $http.post(api.getSoreChartAndOtherVo)
}

/**
* 综合评分-图表数据查询
*/
export function getDamSafetyCount (params) {
  return $http.get(api.getDamSafetyCount, params)
}
