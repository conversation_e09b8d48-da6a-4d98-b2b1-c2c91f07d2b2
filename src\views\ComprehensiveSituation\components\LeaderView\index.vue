<template>
  <div class="leader-container">
    <left-region>
      <base-title>综述</base-title>
      <overview class="mt6" />
      <base-title class="mt16" :extra-persent="50">
        隐患情况
        <template #extra>
          <el-select class="emergency-select" v-model="year">
            <el-option v-for="item in YEAR_OPTIONS" :key="item" :label="item" :value="item" />
          </el-select>
          <div @click="showRiskAccount" class="ml10 flx-align-center">
            <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
          </div>
        </template>
      </base-title>
      <hidden-danger-statistics :year="year" />
      <base-title :extra-persent="50" class="mt10">
        智能分析
        <template #extra>
          <div class="analysis-extra">
            <el-select
              class="emergency-select"
              :style="`width: ${px2vw(110)}`"
              v-model="analysisTime"
            >
              <el-option
                v-for="item in ANALYSIS_TIMES"
                :key="item.label"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div @click="showAnalysisDialog" class="flx-align-center">
              <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
            </div>
          </div>
        </template>
      </base-title>
      <div class="scroll-container">
        <intelligent-analysis :last-day-num="analysisTime" />
      </div>
    </left-region>
    <visual-angle />
    <more-info />
    <right-region>
      <base-title tip="今天以及明天的预警数据">预警信息</base-title>
      <warn-info />
      <base-title>
        水库划界
        <template #extra>
          <div class="flx-align-center" @click="showReservoirDivision">
            <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
          </div>
        </template>
      </base-title>
      <div class="info-container">
        <arrow-title class="mt15 mb16">管理范围和保护范围</arrow-title>
        <reservoir-division class="mt10" />
      </div>
      <base-title class="mt10" :extra-persent="40">
        履职情况
        <template #extra>
          <div @click="showRPDetail" class="flx-align-center">
            <span class="mr10">详情</span> <el-icon><ArrowRight /></el-icon>
          </div>
        </template>
      </base-title>
      <responsibile-person-list class="mt8" />
    </right-region>
    <SmartInspectionDialog
      v-model="showDialog"
      :last-day-num="analysisTime"
      @show-video-layer="handleShowVideoLayer"
      show-locate-to-video-btn
    />
    <summary-info v-if="mapVisualAngle === 1" ref="summaryInfoRef" :default-navs="['RR']" />
    <RiskAccountDialog v-model="riskAccountDialogVisible" ref="riskAccountDialogRef" />
    <ResponsiblePersonDetailDialog v-model="showRPDialog" />
  </div>
</template>
<script lang="ts" setup>
import Overview from './components/Overview.vue'
import HiddenDangerStatistics from './components/HiddenDangerStatistics.vue'
import ResponsibilePersonList from './components/ResponsibilePersonList.vue'
import VisualAngle from './components/VisualAngle.vue'
import MoreInfo from './components/MoreInfo.vue'
import WarnInfo from './components/WarnInfo.vue'
import ResponsiblePersonDetailDialog from './components/ResponsiblePersonDetailDialog.vue'
import IntelligentAnalysis from './components/IntelligentAnalysis.vue'
import ReservoirDivision from './components/ReservoirDivision.vue'
import { useCSMap } from '../__map__/useCSMap'
import { ANALYSIS_TIMES, YEAR_OPTIONS } from './constant'
import SmartInspectionDialog from '@/views/ComprehensiveSituation/components/LeaderView/components/SmartInspectionDialog.vue'
import { px2vw } from '@/utils'
import SummaryInfo from '../NormalView/components/summaryInfo/index.vue'
import RiskAccountDialog from '@/views/FourManagement/TimelyRiskElimination/components/RiskAccountDialog.vue'
import { useSetMapGeoJsonLayer } from '@/hooks/useSetMapGeoJsonLayer'

const riskAccountDialogRef = useTemplateRef('riskAccountDialogRef')
const showDialog = ref(false)
const showRPDialog = ref(false)
const riskAccountDialogVisible = ref(false)
const summaryInfoRef = useTemplateRef('summaryInfoRef')

const { mapVisualAngle } = useCSMap()

const year = ref(new Date().getFullYear().toString())

const analysisTime = ref(7)

const setMapGeoJsonLayer = new useSetMapGeoJsonLayer()

watch(
  () => mapVisualAngle.value,
  val => {
    if (val === 1) {
      setMapGeoJsonLayer.removeAllLayer()
      setMapGeoJsonLayer.layerRender([
        {
          category: '5', //水库管理范围图层分类字典值
          textColor: 'rgb(253, 187, 29)',
          borderColor: 'rgb(253, 187, 29)',
          labelKey: '名称|name',
          text: '水库管理范围'
        },
        {
          category: '6', //水库保护范围图层分类字典值
          textColor: 'rgb(245,128,128)',
          borderColor: 'rgb(245,128,128)',
          labelKey: '名称|name|',
          text: '水库保护范围'
        }
      ])
    }
  }
)

const showRiskAccount = () => {
  riskAccountDialogVisible.value = true
  riskAccountDialogRef.value?.setYearRange(Number(year.value))
}

const showRPDetail = () => {
  showRPDialog.value = true
}

const showReservoirDivision = () => {
  mapVisualAngle.value = 1
}

const showAnalysisDialog = () => {
  showDialog.value = true
}

const handleShowVideoLayer = () => {
  nextTick(() => {
    if (!summaryInfoRef.value?.selectedNavs?.includes('SP')) {
      summaryInfoRef.value?.handleNavClick('SP', false)
    }
  })
}

onMounted(() => {
  mapVisualAngle.value = 2
})
onUnmounted(() => {
  mapVisualAngle.value = 1
  setMapGeoJsonLayer.removeAllLayer()
})
</script>

<style lang="scss" scoped>
:deep(.year-picker) {
  width: 100px;
}

.info-container {
  padding-left: 18px;
}

.analysis-extra {
  display: flex;
  flex-direction: row;
  gap: 5px;
  align-items: center;
}

.emergency-select {
  width: 90px;

  :deep(.el-select__wrapper) {
    background-color: transparent;
    box-shadow: unset !important;
  }

  :deep(.el-select__input),
  :deep(.el-select__placeholder) {
    font-size: 14px;
    color: #7ff;
  }

  :deep(.el-select__caret) {
    font-size: 20px !important;
    color: rgb(119 255 255 / 50%);
  }
}
</style>
