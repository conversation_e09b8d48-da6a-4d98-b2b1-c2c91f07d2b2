<template>
  <span class="c-dialog-footer-btn">
    <el-button
      class="cancel-btn el-icon-close theme-dialog-btn"
      v-if="showCancelBtn"
      @click="cancelHandle"
      >取 消</el-button
    >
    <el-button
      class="confirm-btn el-icon-check theme-dialog-primary-btn"
      v-if="showConfirmBtn"
      type="primary"
      @click="confirmHandle"
      >确 定</el-button
    >
  </span>
</template>

<script>
export default {
  name: 'DialogFooterBtn',
  components: {},
  props: {
    showCancelBtn: {
      type: Boolean,
      default: true
    },
    showConfirmBtn: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {}
  },
  filters: {},
  computed: {},
  watch: {},
  methods: {
    cancelHandle () {
      this.$emit('cancelClick')
    },
    confirmHandle () {
      this.$emit('confirmClick')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-button[class*='el-icon-'] > span {
  margin-left: 10px;
}
</style>
