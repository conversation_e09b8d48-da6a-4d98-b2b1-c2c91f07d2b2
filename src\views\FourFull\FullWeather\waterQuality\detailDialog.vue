<template>
  <base-dialog
    v-model:showDialog="dialogVisible"
    :append-to-body="true"
    :show-footer="false"
    align-center
    :title="stnm || '水质监测'"
    width="60vw"
  >
    <div class="detail-container flx-column overflow-hide">
      <div class="flx-align-center mb10">
        <span>时间：</span>
        <TimeRangePicker class="mr8" v-model="dateRange" @change="handleStationChange" />
      </div>
      <el-tabs
        v-if="stations.length > 1 && !stcd"
        v-model="activeStation"
        class="c-btn-tabs"
        @tab-change="handleStationChange"
      >
        <el-tab-pane
          v-for="opt in stations"
          :key="opt.stcd"
          :label="opt.stnm"
          :name="opt.stcd"
        ></el-tab-pane>
      </el-tabs>
      <el-tabs v-model="activeFactor" class="c-btn-tabs" @tab-change="handleFactorChange">
        <el-tab-pane
          v-for="opt in factorOptions"
          :key="opt.key"
          :label="opt.label"
          :name="opt.key"
        ></el-tab-pane>
      </el-tabs>
      <div class="chart-container" ref="chartRef"></div>
      <div class="flx-1 overflow-hide mt16">
        <el-table class="c-dialog-el-table" border height="100%" :data="tableData">
          <el-table-column
            v-for="column in tableColumns"
            :key="column.label"
            :prop="column.prop"
            :label="column.label"
            align="center"
          >
          </el-table-column>
        </el-table>
      </div>
      <div class="flx-end mt10">
        <el-pagination
          class="c-black-el-pagination"
          v-model:current-page="pagination.pageNum"
          v-model:page-size="pagination.pageSize"
          :total="allTableData.length"
          background
          layout="total, prev, pager, next"
        />
      </div>
    </div>
  </base-dialog>
</template>
<script setup lang="ts">
import $dayjs from 'dayjs'
import useStaion from '../useStaion'
import useDrawChart, { factorOptions } from './useDrawChart'
import TimeRangePicker from '@/views/FourFull/FullWeather/components/timeRangePicker.vue'

const dialogVisible = defineModel({ type: Boolean, default: false })
const props = withDefaults(defineProps<{ stcd?: string; stnm?: string }>(), {
  stcd: '',
  stnm: ''
})

const chartRef = useTemplateRef('chartRef')

const { stationList } = useStaion()
const { list, loadData, initChart } = useDrawChart(chartRef)

const dateRange = ref([
  $dayjs().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
  $dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
])

const activeStation = ref('')
const activeFactor = ref(factorOptions[0]?.key || '')

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  total: 0
})

const stations = computed(() => {
  return stationList.value.filter(i => i.sttp === 'WQ')
})

const tableColumns = computed(() => {
  const columns = [
    { prop: 'stnm', label: '测站' },
    { prop: 'tm', label: '日期' }
  ]
  const opt = factorOptions.find(item => item.key === activeFactor.value)
  if (opt) {
    return [
      ...columns,
      { prop: opt.key, label: opt.unit ? `${opt.label}(${opt.unit})` : opt.label }
    ]
  }
  return columns
})
const allTableData = computed(() => {
  return list.value.slice(0).reverse()
})
const tableData = computed(() => {
  const start = (pagination.pageNum - 1) * pagination.pageSize
  const end = start + pagination.pageSize
  return allTableData.value.slice(start, end)
})

const handleStationChange = async () => {
  const [startTm, endTm] = dateRange.value
  await loadData(activeStation.value, startTm, endTm)
  handleFactorChange(activeFactor.value)
}

const handleFactorChange = (key: any) => {
  pagination.pageNum = 1
  const opt = factorOptions.find(item => item.key === key)
  if (opt) {
    initChart(opt)
  }
}

watch(
  () => stations.value.length && dialogVisible.value,
  newVal => {
    if (newVal) {
      dateRange.value = [
        $dayjs().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
        $dayjs().endOf('day').format('YYYY-MM-DD HH:mm:ss')
      ]
      activeStation.value = props.stcd || stations.value[0].stcd || ''
      handleStationChange()
    }
  },
  {
    immediate: true
  }
)
</script>
<style lang="scss" scoped>
@import url('./index.scss');

.detail-container {
  height: 70vh;
  color: #fff;
}

.chart-container {
  box-sizing: border-box;
  height: 240px;
  border: 1px solid #1882fb80;
  border-radius: 4px;
}
</style>
