import $http from '@/utils/request'
import apiUrl from '@/utils/apiUrl'

const api = {
  login: apiUrl.defaultUrl + '/xpqz/auth/login',
  userinfo: apiUrl.defaultUrl + '/xpqz/auth/user-info',
  userList: apiUrl.defaultUrl + '/xpqz/user/list',
  deptList: apiUrl.defaultUrl + '/xpqz/common/option/getAllOptionList',
  addUser: apiUrl.defaultUrl + '/xpqz/user/add',
  updateUser: apiUrl.defaultUrl + '/xpqz/user/update',
  roleList: apiUrl.defaultUrl + '/xpqz/user/getAllotRoles',
  allotRoles: apiUrl.defaultUrl + '/xpqz/user/allotRoles',
  loadUser: apiUrl.defaultUrl + '/xpqz/user/load',
  updateUserStatus: apiUrl.defaultUrl + '/xpqz/user/updateStatus',
  deleteUser: apiUrl.defaultUrl + '/xpqz/user/delete',
  refresh: apiUrl.defaultUrl + '/xpqz/auth/refresh',
  getCaptcha: apiUrl.defaultUrl + '/xpqz/auth/getCaptcha',
  resetPassword: apiUrl.defaultUrl + '/xpqz/user/reset-password'
}

/**
 * 登录接口
 * @param data
 * username string
 * password string
 */
export function login (data) {
  return $http.post(api.login, data)
}

/**
 * 获取验证码接口
 */
export function getCaptcha (params) {
  return $http.get(api.getCaptcha, params)
}

/**
 * 登录用户信息接口
 */
export function userInfo (isHide) {
  return $http.post(api.userinfo, '', isHide)
}

/**
 * 用户修改密码接口
 */
export function resetPassword (data) {
  return $http.post(api.resetPassword, data)
}

/**
 * 用户列表接口
 */
export function userList (data) {
  return $http.post(api.userList, data)
}

/**
 * 部门列表接口
 */
export function deptList (params) {
  return $http.get(api.deptList, params)
}

/**
 * 增加用户接口
 */
export function addUser (data) {
  return $http.post(api.addUser, data)
}

/**
 * 修改用户接口
 */
export function updateUser (data) {
  return $http.post(api.updateUser, data)
}

/**
 * 待分配角色列表接口
 * 不传userId返回所有角色
 */
export function roleList (data) {
  return $http.post(api.roleList, data)
}

/**
 * 分配角色接口
 */
export function allotRoles (data) {
  return $http.post(api.allotRoles, data)
}

/**
 * 加载用户接口
 */
export function loadUser (params) {
  return $http.get(api.loadUser, params)
}

/**
 * 批量修改用户状态接口
 */
export function updateUserStatus (data) {
  return $http.post(api.updateUserStatus, data)
}

/**
 * 批量删除用户接口
 */
export function deleteUser (data) {
  return $http.post(api.deleteUser, data)
}

/**
 * 刷新token接口
 */
export function refresh (data) {
  return $http.post(api.refresh, data)
}
