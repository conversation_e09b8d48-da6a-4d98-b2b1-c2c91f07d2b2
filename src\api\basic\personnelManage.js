/* 基础管理-人员管理 */

import $http from "@/utils/request";
import apiUrl from "@/utils/apiUrl";

const api = {
  deleteJobFile: apiUrl.defaultUrl + "/scorePersonnelManage/deleteJobFile",
  delPersonById: apiUrl.defaultUrl + "/scorePersonnelManage/delPersonById",
  delTrainById: apiUrl.defaultUrl + "/scorePersonnelManage/delTrainById",
  getTrainByPersonId:
    apiUrl.defaultUrl + "/scorePersonnelManage/getTrainByPersonId",
  jobMatterUpload:
    apiUrl.defaultUrl + "/scorePersonnelManage/jobMatterUpload",
  list: apiUrl.defaultUrl + "/scorePersonnelManage/list",
  savePersonnelInfo:
    apiUrl.defaultUrl + "/scorePersonnelManage/savePersonnelInfo",
  saveTrain: apiUrl.defaultUrl + "/scorePersonnelManage/saveTrain",
  getByProjectId:
    apiUrl.defaultUrl + "/scorePersonnelManage/getByProjectId",
  getPersonnelOverview:
    apiUrl.defaultUrl + "/moreProject/getPersonnelOverview",
  saveAwardRecord: apiUrl.defaultUrl + "/scorePersonnelManage/saveAwardRecord",
  delAwardRecordById:
    apiUrl.defaultUrl + "/scorePersonnelManage/delAwardRecordById",
  getAwardRecordByPersonId:
    apiUrl.defaultUrl + "/scorePersonnelManage/getAwardRecordByPersonId",
};

/**
 * 人员管理-删除岗位-事项-人员表
 * id
 */
export function deleteJobFile(data) {
  return $http.post(api.deleteJobFile, data);
}

/**
 * 人员管理-删除人员
 * id
 */
export function delPersonById(data) {
  return $http.post(api.delPersonById, data);
}

/**
 * 人员管理-删除培训记录
 * id
 * personnelId // 人员id
 */
export function delTrainById(data) {
  return $http.post(api.delTrainById, data);
}

/**
 * 人员管理-查询培训记录
 * personId // 人员id
 */
export function getTrainByPersonId(params) {
  return $http.get(api.getTrainByPersonId, params);
}

/**
 * 人员管理-岗位-事项-人员表文件上传
 * "id": 0, 主键
 * "projectId": 0 // 工程id
 * "file": "object" 文件
 */
export function jobMatterUpload(data) {
  return $http.postUpLoadFile(api.jobMatterUpload, data);
}

/**
 * 人员管理-分页查询
 * "areaCode": "string",
 * "projectName": "string",
 * "projectScale": 0,
 * "projectType": "string"
 * "pageNum": 1
 * "pageSize": 10
 */
export function list(data, parmas) {
  return $http.postParams(api.list, data, parmas);
}

/**
 * 人员管理-保存人员信息
 * "id": 0, 主键
 * "projectId": 0 // 工程id
 * "workLicense": 0 // 工作证附件ID
 * "name": ""
 * "phone": ""
 * "remarks": ""
 * "type": 1 // 类型：1-防汛抢险，2-防汛物料，3-安全生产，4-档案管理
 */
export function savePersonnelInfo(data) {
  return $http.post(api.savePersonnelInfo, data);
}

/**
 * 人员管理-保存培训记录
 * "id": 0, // 主键
 * "personnelId": 0, // 人员id
 * "signInFileId": "string", // 签到表文件id
 * "trainingContentFileId": "string", // 培训内容文件id
 * "trainingDuration": 0, // 培训时长
 * "trainingPlace": "string", // 培训地点
 * "trainingTime": "2023-08-31T01:13:25.824Z" // 培训时间
 */
export function saveTrain(data) {
  return $http.post(api.saveTrain, data);
}
/*
人员管理-根据工程projectId查询详情
*/
export function getByProjectId(data) {
  return $http.post(api.getByProjectId, data);
}
/*
人员管理-多工程人员管理总览--省市区用户
*/
export function getPersonnelOverview(data) {
  return $http.post(api.getPersonnelOverview, data);
}


/*
基础管理-人员管理
保存获奖记录
POST
/scorePersonnelManage/saveAwardRecord
*/
export function saveAwardRecord(data) {
  return $http.post(api.saveAwardRecord, data);
}

/*
基础管理-人员管理
删除获奖记录
POST
/scorePersonnelManage/delAwardRecordById
*/
export function delAwardRecordById(id) {
  return $http.post(api.delAwardRecordById + '?id=' + id);
}

/*
基础管理-人员管理
根据人员ID查询获奖记录
GET
/scorePersonnelManage/getAwardRecordByPersonId
*/
export function getAwardRecordByPersonId(data) {
  return $http.get(api.getAwardRecordByPersonId, data);
}
