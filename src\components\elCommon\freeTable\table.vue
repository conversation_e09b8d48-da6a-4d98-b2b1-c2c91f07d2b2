<template>
  <div
    :class="{ 'no-flex': tableHeight !== 'auto', stdClass: true, 'dispose-table-align':  hasDisposeTableAlign}"
    class="free-table"
    ref="table"
  >
    <el-pagination
      class="free-table-top-pagination"
      v-if="showPagination && paginationPosition === 'top'"
      v-bind="$attrs"
      @size-change="currentSizeChange"
      @current-change="currentChange"
    >
    </el-pagination>

    <el-table
      :header-cell-style="{ backgroundColor: '#DFECFF', color: '#0B0306' }"
      :cell-style="resetEvenRowStyle"
      stripe
      ref="elTable"
      :data="data"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <template slot="empty">
        <el-empty description="暂无数据"></el-empty>
      </template>
      <!-- 新增拖拽列（可选） -->
      <el-table-column
        v-if="draggable"
        width="50"
        align="center"
      >
        <template #default>
          <i class="el-icon-rank" :class="handleClass"></i>
        </template>
      </el-table-column>
      <template v-for="item in tbColumns">
        <el-table-column
          v-if="item.slotScope"
          v-bind="item"
          :key="item.prop"
          v-on="$listeners"
          show-overflow-tooltip
        >
          <template slot="header" slot-scope="scope">
            <slot :name="item.prop + 'Header'" v-bind="scope">
              {{ item.label }}
            </slot>
          </template>
          <template slot-scope="scope">
            <slot :name="item.prop" v-bind="scope"></slot>
          </template>
        </el-table-column>
        <free-column
          :showTooltip="showTooltip"
          v-else
          v-bind="$attrs"
          :key="item.prop"
          :column="item"
        />
      </template>
    </el-table>

    <el-pagination
      v-if="showPagination && paginationPosition === 'bottom'"
      v-bind="$attrs"
      @size-change="currentSizeChange"
      @current-change="currentChange"
    >
    </el-pagination>
  </div>
</template>

<script>
import FreeColumn from './column'
import Sortable from 'sortablejs'

export default {
  name: 'FreeTable',
  data () {
    return {
      dom: null,
      hasDisposeTableAlign: false
    }
  },
  components: {
    FreeColumn
  },
  props: {
    // ...原有 props
    draggable: { // 是否启用拖拽，默认 false
      type: Boolean,
      default: false
    },
    sortKey: { // 排序字段名，默认 'sort'
      type: String,
      default: 'sort'
    },
    handleClass: { // 拖拽手柄的 class，默认 'drag-handle'
      type: String,
      default: 'drag-handle'
    },
    data: Array,
    column: Array,
    columnsProps: {
      type: Object,
      default: () => undefined
    },
    showPagination: {
      type: Boolean,
      default: false
    },
    showTooltip: {
      type: Boolean,
      default: true
    },
    tableHeight: {
      type: [Number, String],
      default: 'auto'
    },
    paginationPosition: {
      type: String,
      default: 'bottom'
    },
    isDisposeTableAlign: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    tbColumns () {
      const { column, columnsProps: props } = this
      const Columns = column.filter(c => !c.noShow)
      return Columns.map((col) => {
        const it = Object.assign({}, props, col)
        return it
      })
    }
  },
  mounted () {
    this.$nextTick(() => {
      if (this.draggable) {
        this.initSortable()
      }
      if (this.isDisposeTableAlign) {
        setTimeout(() => {
          this.hasDisposeTableAlign = true
        }, 700)
      }
      // 根据浏览器高度设置初始高度 不要动态计算，用flex布局
      // this.tableHeight = window.innerHeight - this.$refs.elTable.$el.offsetTop - 180
      // 监听浏览器高度变化，修改表格高度
      // window.onresize = () => {
      // this.tableHeight = window.innerHeight - this.$refs.elTable.$el.offsetTop - 180
      // }
    })
  },
  methods: {
    initSortable() {
      const el = this.$refs.elTable.$el.querySelector('.el-table__body-wrapper tbody')
      const sortable = new Sortable(el, {
        handle: `.${this.handleClass}`, // 指定拖拽手柄
        animation: 150, // 动画效果
        ghostClass: 'sortable-ghost', // 拖拽占位样式
        onEnd: ({ newIndex, oldIndex }) => {
          // 获取当前行数据
          const currRow = this.data.splice(oldIndex, 1)[0]
          this.data.splice(newIndex, 0, currRow)

          // 更新 sort 值
          this.updateSortOrder()

          // 触发排序事件
          this.$emit('sort-change', this.data)
        }
      })
    },

    // 更新 sort 值
    updateSortOrder () {
      this.data.forEach((item, index) => {
        item[this.sortKey] = index + 1
      })
    },
    resetEvenRowStyle (row) {
      const STYLE = {
        color: '#222F40',
        fontSize: '14px'
      }
      if ((row.rowIndex + 1) % 2 === 0) {
        STYLE.backgroundColor = '#EDF2F9'
      }
      return STYLE
    },
    // 分页
    // 切换当前页
    currentChange (page) {
      this.$emit('update:current-page', page)
      this.$emit('page-change', page)
    },
    // 当前页显示条数
    currentSizeChange (size) {
      this.$emit('update:page-size', size)
      this.$emit('page-size-change', size)
    }
  }
}
</script>
<style lang="scss" scoped>
.free-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  ::-webkit-scrollbar {
    display: block !important;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #ccc !important;
  }
  &.no-flex {
    display: block;
  }

  ::v-deep .el-table {
    flex: 30;
    overflow: scroll;

    .el-table__cell,
    th.el-table__cell.is-leaf {
      border-bottom-color: #edf2f9;
    }

    &::before {
      height: 0px;
    }

    .sort-caret {
      border-width: 10px;
      &.ascending {
        top: 0;
      }
      &.descending {
        bottom: 0;
      }
    }

    .ascending .sort-caret.ascending {
      border-bottom-color: #ffffff;
    }
    .descending .sort-caret.descending {
      border-top-color: #ffffff;
    }
  }
  .el-pagination {
    flex: 2;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 24px;
  }

  .free-table-top-pagination {
    &.el-pagination {
      margin-top: 0;
      margin-bottom: 20px;
      padding-bottom: 20px;

      @include borBottom;
    }
  }
}
.dispose-table-align{
  margin-top: 10px;
}
</style>
