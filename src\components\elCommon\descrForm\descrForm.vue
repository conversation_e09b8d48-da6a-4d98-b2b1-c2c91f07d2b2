<template>
  <div class="desc-form">
    <el-form :ref="'descForm'" :model="comFormData" :rules="rules" hide-required-asterisk status-icon>
      <el-descriptions class="margin-top" :column="defaultColumn" border>
        <!-- 标题 -->
        <template slot="title">
          <slot name="title">{{ title }}</slot>
        </template>
        <!-- 按钮 -->
        <template slot="extra">
          <slot name="extra"></slot>
        </template>
        <el-descriptions-item v-for="(dItem, dIndex) of fieldList" :key="dIndex" :labelStyle="{
          width: dItem.labelWidth || '10%',
          height: dItem.height + 'px',
          textAlign: 'right'
        }" :contentStyle="{ width: dItem.contentWidth || '15%' }" :span="dItem.span || 1">
          <template slot="label">
            <span class="red-require" v-if="rules[dItem.value] && rules[dItem.value][0].required">*</span>
            {{ dItem.label }}
          </template>
          <div class="edit flex-between" v-if="type !== 'VIEW'">
            <el-form-item :prop="dItem.value" style="width: 100%">
              <template v-if="dItem.type !== 'none'">
                <component v-if="dItem.type !== 'upload'" :is="'el-' + dItem.type" v-model="comFormData[dItem.value]"
                  :props="dItem.props" :options="listTypeInfo[dItem.list]" :type="dItem.dataType"
                  :picker-options="dItem.timePickerOptions" :clearable="dItem.clearable !== false ? true : false"
                  :disabled="dItem.disabled" :start-placeholder="dItem.startPlaceholder"
                  :end-placeholder="dItem.endPlaceholder" :range-separator="dItem.rangeSeparator || '-'"
                  :filterable="dItem.filterable" :multiple="dItem.multiple" :collapse-tags="dItem.collapseTags"
                  default-first-option :allow-create="dItem.allowCreate" :no-data-text="dItem.noDataText || ''"
                  :placeholder="getPlaceholder(dItem)" :value-format="dItem.valueFormat" :format="dItem.valueFormat"
                  :style="{ width: dItem.width || '100%' }" :maxlength="dItem.maxLength"
                  :autosize="dItem.autosize || { minRows: 2, maxRows: 10 }" v-bind="dItem.attrs" @click.native="
                    handleEvent(
                      dItem.event,
                      dItem,
                      comFormData[dItem.value],
                      'click'
                    )
                  " @change="
  handleEvent(
    dItem.event,
    dItem,
    comFormData[dItem.value],
    'change'
  )
" @input="
  handleEvent(
    dItem.event,
    dItem,
    comFormData[dItem.value],
    'input'
  )
" @blur="
  handleEvent(
    dItem.event,
    dItem,
    comFormData[dItem.value],
    'blur'
  )
">
                  <template v-if="dItem.type == 'select'">
                    <el-option v-for="(childItem, childIndex) in listTypeInfo[
                      dItem.list
                    ]" :key="`${childItem.value}${childIndex}`" :label="childItem.name" :value="childItem.code" />
                  </template>
                </component>
                <slot v-else :name="dItem.value"></slot>
              </template>
            </el-form-item>
            <span class="unit" v-if="dItem.valueUnit">{{
              dItem.valueUnit
            }}</span>
          </div>
          <div class="view" v-else>
            <div v-if="dItem.slot">
              <slot :name="dItem.value"></slot>
            </div>
            <div v-else class="flex-between">
              <span
                v-if="dItem.type == 'select' && (comFormData[dItem.value] !== null || comFormData[dItem.value] !== undefined || comFormData[dItem.value] !== '')">{{
                  comSelectValue(dItem.value, dItem.list)
                }}</span>
              <!-- 针对 switch 需要显示文字的组件 -->
              <span v-else-if="dItem.type == 'switch'">
                   {{comFormData[dItem.value] === true ? '是' : '否'}}
              </span>
              <span v-else>{{ comFormData[dItem.value] }}</span>
              <span v-if="dItem.valueUnit">{{ dItem.valueUnit }}</span>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'DescForm',
  props: {
    // 表单配置项
    fieldList: {
      type: Array,
      default: () => []
    },
    // 表单数据
    formData: { type: Object },
    // 下拉选项list
    listTypeInfo: { type: Object },
    // 校验规则
    rules: { type: Object },
    // 表单默认显示列数
    defaultColumn: { type: Number, default: () => 4 },
    // 类型
    type: { type: String },
    // 标题
    title: { type: String, default: '' }
  },
  data() {
    return {
      comFormData: {},
      listTypeObject: {}
    }
  },
  watch: {
    formData: {
      handler(val) {
        if (val) {
          this.comFormData = val
        }
      },
      deep: true,
      immediate: true
    },
    listTypeInfo: {
      handler(val) {
        if (val) {
          this.listTypeObject = val
        }
      },
      deep: true
    }
  },
  computed: {
    // 下拉框查找值
    comSelectValue() {
      return (key, listKey) => {
        if (!this.listTypeInfo[listKey]) return
        return this.listTypeInfo[listKey].find(v => {
          return v.code === this.comFormData[key]
        })?.name
      }
    }
  },
  methods: {
    // 得到placeholder的显示
    getPlaceholder(row) {
      if (!row.require) return
      let placeholder
      const types = ['select', 'date-picker', 'cascader']
      if (row.type === 'input') {
        placeholder = `请输入${row.label || ''}`
      } else if (types.includes(row.type)) {
        placeholder = `请选择${row.label || ''}`
      } else {
        placeholder = row.label
      }
      return placeholder
    },
    // 表单事件派发
    handleEvent(event, item, data, type) {
      this.$emit('handleEvent', { event, item, data, type })
    },
    // 表单校验事件
    validateForm() {
      return this.$refs.descForm.validate()
    },
    validateFormResult() {
      let pass = true
      this.$refs.descForm.validate(valid => {
        if (valid) {
          pass = true
        } else {
          pass = false
        }
      })
      return pass
    },
    resetForm() {
      this.$refs.descForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-descriptions {
  .red-require {
    color: red;
  }

  .rule-empty {
    margin-top: 2px;
    color: #f56c6c;
    position: absolute;
    bottom: -25px;
    left: 0;
    font-size: 12px;
    white-space: nowrap;
  }

  .edit {
    position: relative;
    margin-bottom: 10px;
  }

  .flex-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .unit {
    white-space: nowrap;
    padding-left: 5px;
    padding-right: 5px;
    width: 55px;
  }

  .is-bordered .el-descriptions-item__cell {
    border-color: #D9E3F1;
  }

  .el-descriptions-item__label.is-bordered-label {
    background: #EDF2F9;
    color: #222F40;
  }

  .el-descriptions__extra .el-button--text {
    color: #0A4EAD;
  }
}

::v-deep .el-button--text {
  span {
    margin-left: 10px;
  }
}

::v-deep .el-form-item {
  margin-bottom: 0px !important;
}
</style>
